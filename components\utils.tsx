import { api } from "@/app/utils";
import { useEffect, useId, useRef, useState } from "react";

export const useConst = (initialValue: any) => {
  const ref = useRef();
  if (ref.current === undefined) {
    ref.current =
      typeof initialValue === "function" ? initialValue() : initialValue;
  }
  return ref.current;
};

export const ToggleButton = (props: any) => {
  const [checked, setChecked] = useState(props.checked);
  useEffect(() => {
    props.onClick();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checked]);
  const id = useId();
  return (
    <label className="flex items-center cursor-pointer">
      <div className="relative">
        <input
          id={id}
          type="checkbox"
          checked={checked}
          className="sr-only"
          onChange={(e) => setChecked(e.target.checked)}
        />
        <div className="w-10 h-4 bg-ash2 rounded-full shadow-inner"></div>
        <div
          className={`${
            checked ? "bg-blue translate-x-full" : ""
          } absolute w-6 h-6 bg-white rounded-full shadow -left-1 -top-1 transition`}
        ></div>
      </div>
    </label>
  );
};

export async function getRate(selectedCrypt: string, selectedFiat: string) {
  const KEY =
    "290dc5a51a178e506a9fa0edbde18dd893a0f50afa2d823d6b11b37f7003c0d7";
  const pairKey = `${selectedCrypt || "USDT"}:${selectedFiat || "NGN"}`;
  let rate = (global as any)[pairKey];
  if (!rate) {
    const url_path = `https://min-api.cryptocompare.com/data/price?fsym=${
      selectedCrypt || "USDT"
    }&tsyms=${selectedFiat || "NGN"}&api_key=${KEY}`;
    const rate_result: any = await api(url_path);
    rate = rate_result.data[(selectedFiat || "NGN") as string];
    (global as any)[pairKey] = rate;
    setTimeout(() => {
      delete (global as any)[pairKey];
    }, 5 * 60 * 1000); // expire after 5 minutes
  }
  return { [pairKey]: rate };
}
