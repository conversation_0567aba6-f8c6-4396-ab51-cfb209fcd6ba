"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { adminApi, getTokenName } from "@/app/utils";
import { exportReceiptAsImage, generatePDF } from "@/lib/utils";
import RecentTransactionsTable from "@/components/dashboard/RecentTransactionsTable";

import Analytics from "@/components/admin/Analytics";

function Page({ params }: { params: { userId: string } }) {
  const [loading, setLoading] = useState(false);
  const [transactions, setTransactions] = useState([]);

  useEffect(() => {
    setLoading(true);
    adminApi
      .get(`/v1/admin/transactions?done=true`)
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          setTransactions(res.data.data);
        } else {
          console.log("Couldn't fetch transactions");
        }
      })
      .catch((err) => {
        console.log(err);
        if (err.response.data) {
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <section className="mt-4">
      <div className="">
        <Analytics />
      </div>

      <RecentTransactionsTable
        exportReceiptAsImage={exportReceiptAsImage}
        transactions={transactions}
        generatePDF={generatePDF}
      />
    </section>
  );
}

export default Page;
