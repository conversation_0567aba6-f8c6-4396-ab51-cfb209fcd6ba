"use client";
import { api } from "@/app/utils";
import LoadingSpinner from "@/components/LoadingSpinner";
import { EyeIcon, EyeSlashIcon, NewTabIcon } from "@/components/icons";
import useStore from "@/store";
import React, { useEffect, useState } from "react";

function Developers() {
  const [loading, setLoading] = useState(false);
  const [secretKey, setSecretKey] = useState("");
  const [publicKey, setPublicKey] = useState("");
  const [viewSecret, setViewSecret] = useState(false);
  const [viewPublic, setViewPublic] = useState(false);
  const [copiedSecret, setCopiedSecret] = useState(false);
  const [copiedPublic, setCopiedPublic] = useState(false);
  const [liveMode, setLiveMode] = useState(false);
  const { user } = useStore();

  const generateNewKeys = () => {
    setLoading(true);
    api
      .post("/v1/settings/api-keys?env=live")
      .then((res) => {
        console.log(res);
        setSecretKey(res.data.data.liveSecretKey);
        setPublicKey(res.data.data.livePublicKey);
        // if (liveMode) {
        //   setSecretKey(res.data.data.liveSecretKey);
        //   setPublicKey(res.data.data.livePublicKey);
        // } else {
        //   setSecretKey(res.data.data.testSecretKey);
        //   setPublicKey(res.data.data.testPublicKey);
        // }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      setLiveMode(location.hostname.startsWith("www.flincap.com"));
      // fetch api keys
      api
        .get("/v1/settings/api-keys?env=live")
        .then((res) => {
          console.log(res);
          setSecretKey(res.data.data.liveSecretKey);
          setPublicKey(res.data.data.livePublicKey);
          // if (liveMode) {
          //   setSecretKey(res.data.data.liveSecretKey);
          //   setPublicKey(res.data.data.livePublicKey);
          // } else {
          //   setSecretKey(res.data.data.testSecretKey);
          //   setPublicKey(res.data.data.testPublicKey);
          // }
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, []);

  if (!user || (user?.role == "Support" && user?.organisation !== null)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <h2 className="text-lg font-semibold mb-2">Not authorized</h2>
        <p className="text-gray-600">
          You do not have permission to view this page.
        </p>
      </div>
    );
  }

  return (
    <section className="px-4 py-6">
      <div className="border-b border-b-[#eeeeee] px-4 py-4">
        <h2 className="text-black">API Keys</h2>
        <p className="font-light text-sm">
          Here are your API keys for seamless integration with our platform.
          Whether you&apos;re testing in sandbox mode or deploying live.
        </p>
      </div>

      <div className="px-4 py-4 mt-6 space-y-4">
        {secretKey || publicKey ? (
          <>
            <div className="border px-2.5 py-4 rounded flex justify-between max-w-[500px] ">
              <div
                className="grow cursor-pointer"
                onClick={() => {
                  navigator.clipboard.writeText(secretKey).then(() => {
                    setCopiedSecret(true);
                    setTimeout(() => setCopiedSecret(false), 2000);
                  });
                }}
              >
                <h3>{liveMode ? "Live" : "Test"} Secret Key</h3>
                <input
                  type="text"
                  value={
                    (viewSecret
                      ? secretKey
                      : secretKey?.replaceAll(/./g, "*")) || "-----".repeat(14)
                  }
                  className="text-[13px] text-appGrayTextLight w-full grow outline-none border-none pointer-events-none"
                />
                <p
                  className={`text-[10px] mt-1 ${
                    copiedSecret ? "text-green-500" : "text-appPurple"
                  }`}
                >
                  {copiedSecret ? "Copied" : "Click to copy"}
                </p>
              </div>
              <button
                onClick={() => setViewSecret((prev) => !prev)}
                className="pl-2 pr-2"
              >
                {viewSecret ? (
                  <EyeIcon className="w-5 stroke-appPurple" />
                ) : (
                  <EyeSlashIcon className="w-5 stroke-appPurple" />
                )}
              </button>
            </div>
            <div className="border px-2.5 py-4 rounded flex justify-between max-w-[500px] ">
              <div
                className="grow cursor-pointer"
                onClick={() => {
                  navigator.clipboard.writeText(publicKey).then(() => {
                    setCopiedPublic(true);
                    setTimeout(() => setCopiedPublic(false), 2000);
                  });
                }}
              >
                <h3>{liveMode ? "Live" : "Test"} Public Key</h3>
                {/* <p className='text-appGrayTextLight font-light text-sm'>{publicKey || "-----".repeat(14)}</p> */}
                <input
                  type="text"
                  value={
                    (viewPublic
                      ? publicKey
                      : publicKey?.replaceAll(/./g, "*")) || "-----".repeat(14)
                  }
                  className="text-[13px] text-appGrayTextLight w-full grow outline-none border-none pointer-events-none"
                />
                <p
                  className={`text-[10px] mt-1 ${
                    copiedPublic ? "text-green-500" : "text-appPurple"
                  }`}
                >
                  {copiedPublic ? "Copied" : "Click to copy"}
                </p>
              </div>
              <button
                onClick={() => setViewPublic((prev) => !prev)}
                className="pl-2 pr-2"
              >
                {viewPublic ? (
                  <EyeIcon className="w-5 stroke-appPurple" />
                ) : (
                  <EyeSlashIcon className="w-5 stroke-appPurple" />
                )}
              </button>
            </div>
          </>
        ) : (
          <p>No secret/public key</p>
        )}

        <div className="flex gap-3">
          <button
            onClick={generateNewKeys}
            className="bg-black text-white px-4 py-1.5 text-sm rounded"
          >
            {!loading ? (
              "Generate New Keys"
            ) : (
              <LoadingSpinner className="mx-auto" />
            )}
          </button>
          <button
            onClick={() => window.open("https://docs.flincap.com", "_blank")}
            className="flex gap-1 items-center bg-[#71bbff] text-white px-4 py-1.5 text-sm rounded"
          >
            <p>Read Docs</p>
            <NewTabIcon className="w-4" />
          </button>
        </div>
      </div>
    </section>
  );
}

export default Developers;
