"use client";

import { CONSTANTS } from "@/app/utils";
import LoadingSpinner from "@/components/LoadingSpinner";
import axios from "axios";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import OtpInput from 'react-otp-input';
import React, { FormEvent, useEffect, useState } from "react";

function Page() {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const searchParams = useSearchParams();
  const [errorMessage, setErrorMessage] = useState("");
  const [otp, setOtp] = useState("");
  const [screenWidth, setScreenWidth] = useState(1000);
  const [formDetails, setFormDetails] = useState({
    email: "",
  });

  useEffect(() => {
    const resizeHandler = () => {
      setScreenWidth(window.innerWidth);
    };
    window.addEventListener("resize", resizeHandler);

    return () => {
      window.removeEventListener("resize", resizeHandler);
    }
  }, []);

  const sendPasswordResetLink = async (e: FormEvent) => {
    e.preventDefault();

    if (!formDetails.email) {
      // Handle validation
      return;
    }

    try {
      setLoading(true);
      setErrorMessage("");
      const uri = searchParams.get("redirect_uri");
      const res = await axios.post(`${CONSTANTS.SERVER_URL}/v1/auth/forgot-password`, {
        email: formDetails.email,
        redirectURL: uri ? uri : location.origin + "/dashboard",
      });
      if (!res.status.toString().startsWith("2")) {
        alert("Failed to signin");
      } else {
        setStep(2);
      }
    } catch (err: any) {
      if (err.response) {
        setErrorMessage(err.response.data.message);
      } else if (err.request) {
        if (navigator.onLine) {

        } else {
          console.log("Seems like you're not online");
        }
      }
      console.log(err);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="flex items-center justify-center px-4 py-10 md:py-4 md:h-[550px] w-full">
      {{
        1: <form onSubmit={sendPasswordResetLink} className="grow max-w-md">
          <h1 className="text-3xl font-semibold">Forgot Password</h1>
          <p className="text-appGrayText mt-2">Kindly provide email associated with your Flincap account</p>

          <div className="border-[1px] border-[#cacaca] rounded py-1 px-2 my-4">
            <label htmlFor="email" className="text-xs text-[#999999]">Email Address</label>
            <input type="text" id="email" value={formDetails.email} onChange={(e) => setFormDetails(prev => ({ ...prev, [e.target.id]: e.target.value }))} className="bg-white block w-full outline-none border-none" />
          </div>
          <p className="text-xs text-red-500">{errorMessage}</p>

          <button className="bg-appPurple text-white rounded-md py-2.5 w-full block mt-2 text-sm">
            {!loading ? "Proceed" : <LoadingSpinner className="mx-auto" />}
          </button>
        </form>,
        2: <div className="grow maw-w-md text-center">
          <Image src={"/images/message-sent.png"} width={80} height={80} className="mx-auto mb-3" alt="." />
          <h1 className="text-lg md:text-3xl text-[#0A2540] font-semibold">
            Password Reset Link Sent
          </h1>
          <p className="text-[#424242] text-sm md:text-base mt-3">
            If an account is associated with {" "}
            <span className="text-appPurple">{formDetails.email}</span>.
            A password reset <br /> link will be sent.
          </p>
          <div className="mt-8 flex flex-col items-center gap-4">
            <Link
              href={`mailto:${formDetails.email}`}
              className="bg-appPurple text-white py-2 px-3 rounded-md"
            >
              Open mail app
            </Link>
            <button
              type="button"
              onClick={() => setStep(1)}
              className="text-appPurple py-2 rounded-md w-full"
            >
              Resend link
            </button>
          </div>
        </div>
      }[step]}
    </div>
  );
}

export default Page;
