"use client";
import Nav from "@/components/Nav";
import CookieConsent from "@/components/CookieConsent";
import Image from "next/image";
import Link from "next/link";
import Footer from "@/components/Footer";

export default function Page() {
  return (
    <>
      <Nav />
      <CookieConsent />
      <section className="h-screen md:h-[620px] bg-[url(/images/hero-bg.svg)] bg-[#F6F6F6] bg-cover bg-top bg-no-repeat">
        <div className="h-full flex max-w-7xl mx-auto justify-start gap-20 px-4 items-center py-28 md:py-10 md:justify-between flex-col md:flex-row">
          <div className="md:block md:text-left text-center flex justify-center items-center flex-col">
            <h1 className="text-3xl md:text-6xl font-bold text-black">
              Whitelabel Crypto Exchange
            </h1>
            <p className="my-4 md:my-7 max-w-md text-md md:text-lg">
              Building an online crypto exchange can be expensive and stressful,
              requiring extensive technical know-how. Our whitelabel crypto
              exchange enables traders to own an exchange without stress in no
              time.
            </p>
            <Link
              href="../auth/get-started"
              className="bg-appPurple text-white px-20 py-2.5 rounded-md block w-fit"
            >
              Start Here
            </Link>
          </div>
          <div className="image md:block">
            <Image
              width={200}
              height={200}
              alt="banner illustration"
              src={"/images/wallet-img.svg"}
              className="bg-blend-darken md:w-[350px] md:h-[350px]"
            />
          </div>
        </div>
      </section>
      <section className="h-auto">
        <div className="max-w-7xl py-10 gap-10 mx-auto flex flex-col px-10 items-center md:gap-16 md:py-20">
          <h2 className="text-left md:text-center text-2xl font-bold text-[#0A2540] md:text-3xl md:w-3/6">
            Own an Exchange Without Any Technical Knowledge or Stress!
          </h2>
          <div className="flex flex-col gap-10 max-w-4xl md:flex-row">
            <div className="shadow-xl rounded-xl w-[350px] px-10 pb-20 pt-10 md:w-[400px]">
              <h3 className="font-bold text-lg text-[#0A2540] py-3 font-circularStd">
                Build Your Crypto Exchange with Ease!
              </h3>
              <p className="text-[#425466] font-circularStd pt-4">
                Our whitelabel crypto exchange allows you to build your exchange
                without starting from scratch. All you need is your brand name
                and logo; we will handle the other technicalities. Scale your
                P2P crypto trading brand to new heights with your own crypto
                exchange!
              </p>
            </div>
            <div className="shadow-xl rounded-xl w-[350px] px-10 pt-10 pb-20 md:w-[400px]">
              <h3 className="font-bold text-lg py-3 text-[#0A2540] font-circularStd">
                Automate your Trading Processes!
              </h3>
              <p className="text-[#425466] font-circularStd pt-4">
                With Flincap, you never have to lose out on deals again just
                because you were asleep or unavailable! Our whitelabel exchange
                is designed to help you automate your trading processes so you
                can trade even when you are asleep.
              </p>
            </div>
          </div>
        </div>
      </section>
      <section className="h-auto bg-[#E8F9F0]">
        <div className="max-w-7xl py-10 mx-auto flex flex-col px-10 items-center gap-14 md:gap-14 md:py-16">
          <h2 className="text-center text-3xl font-bold text-[#0A2540]">
            Why Use Our Whitelabel Exchange Infrastructure?
          </h2>
          <div className="grid grid-cols-1 md:grid-rows-2 gap-10 max-w-4xl md:gap-y-10 md:grid-cols-2">
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-one.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Easy Setup
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  Setting up your exchange on Flincap is as simple as three
                  clicks of a button. Your personalized exchange can be ready in
                  minutes. Start your trading journey with ease using Flincap!
                </p>
              </div>
            </div>
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-two.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Automated Features
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  Trading manually is a full-time job that can chip away at your
                  health. With Flincap, customers can continue to trade on your
                  exchange without your input. All you have to do is set your
                  rates and go!
                </p>
              </div>
            </div>
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-three.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Lower Technical Costs
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  Building a personalized crypto exchange from scratch can be
                  costly. With Flincap, you can save on all those technical
                  costs. You don’t have to wait on funds to build your exchange
                  anymore!
                </p>
              </div>
            </div>
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-four.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Launch Now
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  Are you tired of delaying your dreams of building a crypto
                  exchange till tomorrow? Don’t wait until your customers start
                  looking for more reliable alternatives! Launch your exchange
                  powered by Flincap today!
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <aside className="bg-black">
        <div className="max-w-7xl mx-auto md:px-36 py-24 flex flex-col md:flex-row justify-between items-center">
          <div className="text md:block flex flex-col justify-center items-center mb-5">
            <h2 className="text-3xl leading-8 md:text-5xl font-semibold mb-14 text-white">
              <strong className="text-[#7928FF]">Ready to build your</strong>{" "}
              <br />
              <strong>Crypto business?</strong>
            </h2>
            <Link
              href="#"
              className="bg-appPurple text-white px-7 md:px-12 py-2.5 md:py-3 rounded-md block w-fit mt-2"
            >
              Get started
            </Link>
          </div>
          <div className="w-[200px] h-[200px] mt-10 md:mt-0">
            <Image
              src="/images/illustrations/Group.svg"
              alt="Group"
              width={200}
              height={200}
              className="w-full h-full"
            />
          </div>
        </div>
      </aside>
      <Footer />
    </>
  );
}
