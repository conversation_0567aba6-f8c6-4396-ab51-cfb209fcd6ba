"use client";

import { CONSTANTS } from "@/app/utils";
import LoadingSpinner from "@/components/LoadingSpinner";
import oa from "@/components/LoadingSpinner";
import axios from "axios";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import React, { FormEvent, useState } from "react";

function Page() {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const searchParams = useSearchParams();
  const [formDetails, setFormDetails] = useState({
    password: "",
    confirm: "",
  });

  const setupPassword = async (e: FormEvent) => {
    e.preventDefault();

    if (!formDetails.password || !formDetails.confirm) {
      // Handle validation
      return;
    }

    if (formDetails.confirm != formDetails.password) {
      setErrorMessage("Confirm password should match");
      return;
    }

    try {
      setLoading(true);
      const res = await axios.post(`${CONSTANTS.SERVER_URL}/v1/auth/setup-password`, {
        password: formDetails.password,
        otp: searchParams.get("otp"),
      });
      if (!res.status.toString().startsWith("2")) {
        alert("Failed to signin");
        console.log(res);
      } else {
        // Log user in immediately
        localStorage.setItem("tid", res.data.data.accessToken);
        window.location.href = "/dashboard";
      }
    } catch (err: any) {
      if (err.response) {
        setErrorMessage(err.response.data.message);
      } else if (err.request) {
        if (navigator.onLine) {

        } else {
          console.log("Seems like you're not online");
        }
      }
      console.log(err);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="flex items-center justify-center px-4 py-10 md:py-4 md:h-[550px] w-full">
      <form onSubmit={setupPassword} className="grow max-w-md">
        <h1 className="text-3xl font-semibold">Setup new password</h1>
        <p className="text-appGrayText mt-2">Kindly input your new password</p>

        <div className="border-[1px] border-[#cacaca] rounded py-1 px-2 my-4">
          <label htmlFor="password" className="text-xs text-[#999999]">Password</label>
          <input type="password" id="password" value={formDetails.password} onChange={(e) => setFormDetails(prev => ({ ...prev, [e.target.id]: e.target.value }))} className="bg-white block w-full outline-none border-none" />
        </div>
        <div className="border-[1px] border-[#cacaca] rounded py-1 px-2 my-4">
          <label htmlFor="confirm" className="text-xs text-[#999999]">Confirm Password</label>
          <input type="password" id="confirm" value={formDetails.confirm} onChange={(e) => setFormDetails(prev => ({ ...prev, [e.target.id]: e.target.value }))} className="bg-white block w-full outline-none border-none" />
        </div>
        <p className="text-xs text-red-500">{errorMessage}</p>

        <button className="bg-appPurple text-white rounded-md py-2.5 w-full block mt-2 text-sm">
          {!loading ? "Proceed" : <LoadingSpinner className="mx-auto" />}
        </button>
      </form>
    </div>
  );
}

export default Page;
