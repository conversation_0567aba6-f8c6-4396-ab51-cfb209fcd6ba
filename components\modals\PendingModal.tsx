import useStore from "@/store";
import React from "react";

function PendingModal() {
  const { setModal } = useStore();
  return (
    <div className="bg-white rounded-md h-fit max-w-80 md:max-w-96 p-4 grow">
      <svg
        className="mx-auto mb-3"
        xmlns="http://www.w3.org/2000/svg"
        width="60"
        height="60"
        viewBox="0 0 60 60"
        fill="none"
      >
        <path
          d="M37.4995 5C29.0565 5.00355 21.8206 11.0366 20.2988 19.3413C27.5662 15.3644 36.6813 18.0319 40.6582 25.2992C43.1133 29.7858 43.1133 35.2141 40.6582 39.7006C50.1614 37.9598 56.4541 28.8446 54.7132 19.3414C53.191 11.0315 45.9476 4.9968 37.4995 5Z"
          fill="#FCEA4B"
          fillOpacity="0.1"
        />
        <path
          d="M17.5 55C24.4036 55 30 49.4036 30 42.5C30 35.5964 24.4036 30 17.5 30C10.5964 30 5 35.5964 5 42.5C5 49.4036 10.5964 55 17.5 55Z"
          fill="#FCEA4B"
        />
        <path
          d="M27.4992 17.5C19.7907 17.5008 13.3377 23.344 12.5742 31.0145C18.9137 28.2908 26.2609 31.222 28.9847 37.5615C30.3375 40.7101 30.3375 44.2763 28.9847 47.425C37.2279 46.6053 43.2459 39.2584 42.4263 31.0152C41.6634 23.3434 35.2089 17.4993 27.4992 17.5Z"
          fill="#FCEA4B"
          fillOpacity="0.45"
        />
      </svg>
      <div className="text-center">
        <h2 className="text-xl font-semibold text-center">Pending</h2>
        <p className="text-center text-sm mt-4">
          We will notify you when your transaction is completed
        </p>
      </div>
      <button
        onClick={() => {
          window.location.reload();
        }}
        className="mx-auto block text-sm text-center text-appPurple mt-8 mb-3"
      >
        Close
      </button>
    </div>
  );
}

export default PendingModal;
