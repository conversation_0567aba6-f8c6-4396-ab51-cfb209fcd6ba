// @ts-nocheck
import { create } from "zustand";

export interface StoreState {
  token: string;
  user: null | Record<string, any>;
  fiatProvider: string;
  twoFaStatus: boolean;
  currentModal: any;
  collapsedMenu: boolean;
  liveMode: boolean;
  invitationStatus: boolean;
  exchangeData: Record<string, any>;
  setLiveMode: (newVal: boolean) => void;
  setModal: (newModal: any) => void;
  setToken: (newVal) => void;
  setUser: (newUser) => void;
  setInvitationStatus: (newVal: boolean) => void;
  setTwoFaStatus: (newVal: boolean) => void;
  setFiatProvider: (newVal) => void;
  setCollapsedMenu: (newVal: boolean) => void;
  setExchangeData: (newVal: Record<string, any>) => void;
}

const useStore = create<StoreState>((set: any) => ({
  token: "",
  user: null,
  fiatProvider: "",
  twoFaStatus: false,
  currentModal: null,
  collapsedMenu: false,
  liveMode: false,
  invitationStatus: false,
  exchangeData: {},
  setLiveMode: (newVal: boolean) =>
    set((state) => ({ ...state, liveMode: newVal })),
  setModal: (newModal: any) =>
    set((state: any) => ({ ...state, currentModal: newModal })),
  setToken: (newVal) => set((state) => ({ ...state, token: newVal })),
  setUser: (newUser) => set((state) => ({ ...state, user: newUser })),
  setFiatProvider: (newVal) =>
    set((state) => ({ ...state, fiatProvider: newVal })),
  setCollapsedMenu: (newVal: boolean) =>
    set((state) => ({ ...state, collapsedMenu: newVal })),
  setUserDetails: (userDetail) =>
    set((state) => ({ ...state, userDetails: userDetail })),
  setInvitationStatus: (newVal: boolean) =>
    set((state) => ({ ...state, invitationStatus: newVal })),
  setExchangeData: (exchangeDetail) =>
    set((state) => ({ ...state, exchangeData: exchangeDetail })),
  setTwoFaStatus(newVal: boolean) {
    set((state) => ({ ...state, twoFaStatus: newVal }));
  },
}));

export default useStore;
