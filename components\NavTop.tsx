import React, { useState } from "react";
import {
  AddressesIcon,
  ChevronDownIcon,
  CreditIcon,
  Hamburger,
  OverviewIcon,
  SettingsIcon,
  SignoutIcon,
  SupportIcon1,
  TransactionsIcon,
  XMarkIcon,
} from "./icons";
import useStore from "@/store";
import Image from "next/image";
import Link from "next/link";
import { Landmark } from "lucide-react";

function NavTop() {
  const { user } = useStore();
  const [showMenu, setShowMenu] = useState(false);

  const logoutUser = () => {
    localStorage.removeItem("tid");
    window.location.href = "/auth/login";
  };

  return (
    <div>
      <div className="flex gap-3 justify-between items-center py-4 px-4 md:px-6 border-b">
        <Link href={"/dashboard"} className="logo md:hidden">
          <Image
            src={"/images/flincap-logo.svg"}
            alt="."
            width={100}
            height={50}
            priority
          />
        </Link>
        <p className="text-lg md:block hidden font-circularStd">
          Hello,{" "}
          <span className="text-xl">
            {user?.fullName.split(" ")[0] || "Exchange"} 👋🏽
          </span>
        </p>

        <button
          className="md:hidden"
          onClick={() => setShowMenu((prev) => !prev)}
        >
          {showMenu ? (
            <XMarkIcon className="w-8 h-8" />
          ) : (
            <Hamburger className="w-4 h-4 stroke-black" />
          )}
        </button>
      </div>
      {showMenu ? (
        <ul className="space-y-1 absolute block md:hidden border-b top-16 w-full right-0 left-0 bg-white z-10 px-2 pb-5">
          <li>
            <Link
              href="/dashboard"
              onClick={() => setShowMenu(false)}
              className={`pl-3 py-3.5 border border-transparent flex rounded-lg gap-3 items-center`}
            >
              <OverviewIcon className={`sidebar-icon mr-[3.5px]`} />
              <p>Overview</p>
            </Link>
          </li>

          <li>
            <Link
              href="/dashboard/treasury"
              onClick={() => setShowMenu(false)}
              className={`pl-3 py-3.5 border border-transparent flex rounded-lg gap-3 items-center`}
            >
              <Landmark className={`sidebar-icon mr-[3.5px]`} />
              <p>Treasury</p>
            </Link>
          </li>

          <li>
            <Link
              href="/dashboard/ql/transactions"
              onClick={() => setShowMenu(false)}
              className={`pl-3 py-3.5 border border-transparent flex rounded-lg gap-3 items-center`}
            >
              <TransactionsIcon className={`sidebar-icon -ml-0.5`} />
              <p>Transactions</p>
            </Link>
          </li>
          <li>
            <Link
              href="/dashboard/ql/settings"
              onClick={() => setShowMenu(false)}
              className={`pl-3 py-3.5 border border-transparent flex rounded-lg gap-3 items-center`}
            >
              <SettingsIcon className={`sidebar-icon -ml-0.5`} />
              <p>Settings</p>
            </Link>
          </li>
          <li>
            <Link
              href={"#logout"}
              onClick={logoutUser}
              className={`pl-3 py-3.5 w-full border border-transparent flex rounded-lg gap-3 items-center`}
            >
              <SignoutIcon className={`sidebar-icon -ml-0.5`} />
              <p>Logout</p>
            </Link>
          </li>
        </ul>
      ) : null}
    </div>
  );
}

export default NavTop;
