"use client";
import Nav from "@/components/Nav";
import CookieConsent from "@/components/CookieConsent";
import Image from "next/image";
import Link from "next/link";
import Footer from "@/components/Footer";

export default function Page() {
  return (
    <>
      <Nav />
      <CookieConsent />
      <section className="h-screen md:h-[620px] bg-[url(/images/hero-bg.svg)] bg-[#FFF4EA] bg-cover bg-top bg-no-repeat">
        <div className="h-full flex max-w-7xl mx-auto justify-start gap-20 px-4 items-center py-28 md:py-10 md:justify-between flex-col md:flex-row">
          <div className="md:block md:text-left text-center flex justify-center items-center flex-col">
            <h1 className="text-3xl md:text-6xl font-bold text-black">
              Digital Asset Custody
            </h1>
            <p className="my-4 md:my-7 max-w-md text-md md:text-lg">
              Crypto exchanges require a secure wallet infrastructure for safety
              and ease of operations. Secure your cash flow with our digital
              asset custody and settle transactions instantly.
            </p>
            <Link
              href="../auth/get-started"
              className="bg-appPurple text-white px-20 py-2.5 rounded-md block w-fit"
            >
              Start Here
            </Link>
          </div>
          <div className="image md:block">
            <Image
              width={200}
              height={200}
              alt="banner illustration"
              src={"/images/safe.svg"}
              className="bg-blend-darken md:w-[350px] md:h-[350px]"
            />
          </div>
        </div>
      </section>
      <section className="h-auto">
        <div className="max-w-7xl py-10 gap-10 mx-auto flex flex-col px-10 items-center md:gap-16 md:py-20">
          <h2 className="text-left md:text-center text-2xl font-bold text-[#0A2540] md:text-3xl md:w-3/6">
            Keep your digital assets in a secure and easy-to-manage custody
            system!
          </h2>
          <div className="flex flex-col gap-10 max-w-4xl md:flex-row">
            <div className="shadow-xl rounded-xl w-[350px] px-10 pb-20 pt-10 md:w-[400px]">
              <h3 className="font-bold text-lg text-[#0A2540] py-3 font-circularStd">
                Trust Your Digital Assets to a Secure Custody System
              </h3>
              <p className="text-[#425466] font-circularStd pt-4">
                The security of your digital assets is fundamental as you
                transact high volume at your exchange. Our bank-grade security
                ensures all your digital assets are safe in our custody. Trust
                your digital assets to a custody system with the highest
                security encryption possible!
              </p>
            </div>
            <div className="shadow-xl rounded-xl w-[350px] px-10 pt-10 pb-20 md:w-[400px]">
              <h3 className="font-bold text-lg py-3 text-[#0A2540] font-circularStd">
                Manage Your Assets Easily with Our Efficient Asset Management
              </h3>
              <p className="text-[#425466] font-circularStd pt-4">
                Managing your multiple digital assets is no longer a costly
                headache. With our efficient digital asset system, you can
                facilitate asset transfers without stress and at reduced costs.
                Enjoy an efficient digital asset management system with Flincap!
              </p>
            </div>
          </div>
        </div>
      </section>
      <section className="h-auto bg-[#F2EBFF]">
        <div className="max-w-7xl py-10 mx-auto flex flex-col px-10 items-center gap-14 md:gap-14 md:py-16">
          <h2 className="text-center text-3xl font-bold text-[#0A2540]">
            Why Use Our Digital Asset Custody?
          </h2>
          <div className="grid grid-cols-1 md:grid-rows-2 gap-10 max-w-4xl md:gap-y-10 md:grid-cols-2">
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-one.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Multiple Assets and Wallets Supported
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  Our payment APIs are designed to allow easy integration, even
                  if you know almost nothing about coding. With minimal coding,
                  you can integrate your payment APIs into your processes.
                  Whether you sell on a web app, social media, or other
                  platforms, you can integrate our APIs without stress!
                </p>
              </div>
            </div>
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-two.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Lower Transaction Fees
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  Are you tired of trading at a loss with platforms that cut
                  your profit down with exorbitant transaction fees? Maximize
                  your profit margin with a platform that offers the most
                  business-friendly transaction fees.
                </p>
              </div>
            </div>
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-three.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Seamless Integration
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  With Flincap, you can explore a world of unlimited financial
                  interactions without compromising your security. Our
                  simplified and fast processes allow you to access multiple
                  tokens and blockchain protocols.
                </p>
              </div>
            </div>
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-four.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  AML and KYC-compliant
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  Your money never has to hang when you send money across
                  African borders. Our cross-border payment system has
                  bank-grade security to protect you from potential attacks. Do
                  high-volume transactions across Africa without fear!
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <aside className="bg-black">
        <div className="max-w-7xl mx-auto md:px-36 py-24 flex flex-col md:flex-row justify-between items-center">
          <div className="text md:block flex flex-col justify-center items-center mb-5">
            <h2 className="text-3xl leading-8 md:text-5xl font-semibold mb-14 text-white">
              <strong className="text-[#7928FF]">Ready to build your</strong>{" "}
              <br />
              <strong>Crypto business?</strong>
            </h2>
            <Link
              href="#"
              className="bg-appPurple text-white px-7 md:px-12 py-2.5 md:py-3 rounded-md block w-fit mt-2"
            >
              Get started
            </Link>
          </div>
          <div className="w-[200px] h-[200px] mt-10 md:mt-0">
            <Image
              src="/images/illustrations/Group.svg"
              alt="Group"
              width={200}
              height={200}
              className="w-full h-full"
            />
          </div>
        </div>
      </aside>
      <Footer />
    </>
  );
}
