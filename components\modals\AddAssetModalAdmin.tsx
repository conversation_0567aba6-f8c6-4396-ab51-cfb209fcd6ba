import React, { useEffect, useState } from "react";
import { ChevronLeftIcon } from "../icons";
import useStore from "@/store";
import LoadingSpinner from "../LoadingSpinner";
import { adminApi, api } from "@/app/utils";
import CryptoIcon from "../CryptoIcon";

function AddAssetModalAdmin({
  wallets,
  userId,
}: {
  wallets: Record<string, any>[];
  userId: string;
}) {
  const { setModal } = useStore();
  const [loading, setLoading] = useState(true);
  const [assets, setAssets] = useState<Record<any, any>[]>([]);
  const [picked, setPicked] = useState<string[] | undefined>([]);
  const [step, setStep] = useState<"choose" | "fiat" | "crypto">("choose");
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    console.log(picked);
  }, [picked]);

  const addAsset = () => {
    setLoading(true);
    console.log(wallets);
    api
      .post(`/v2/wallets/${userId}`, {
        assetType: "CRYPTO",
      })
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          window.location.reload();
        }
        console.log(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => setLoading(false));
  };

  const generateFiatWallet = async () => {
    // admin can generate fiat wallet with a call here.
    setLoading(true);
    adminApi
      .post(`/v1/admin/users/${userId}/add-fiat-wallet`, {
        assetName: "NGN",
      })
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          window.location.reload();
        } else {
          console.log(res);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    setLoading(true);
    setPicked(wallets.map((wallet) => wallet.symbol));
    api("/v1/wallets/available-assets")
      .then((res) => {
        console.log(res);
        setAssets(res.data);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [wallets]);

  if (step === "choose") {
    return (
      <div>
        <div className="rounded-md h-fit max-w-80 md:max-w-96 grow border bg-white pb-2">
          <div className="bg-[#fcfcfc] rounded-tl-md rounded-tr-md p-4">
            <button
              onClick={() => setModal(null)}
              className="flex items-center"
            >
              <ChevronLeftIcon className="w-4 h-4 stroke-[2px]" />
              <p className="text-sm font-semibold">Go Back</p>
            </button>
          </div>

          <div className="min-w-[300px] px-2">
            <div className="px-4 my-4">
              <p>Select what wallet type to create</p>
            </div>

            <div className="space-y-2 pb-3">
              {loading ? (
                <LoadingSpinner className="mx-auto" />
              ) : (
                <>
                  <button
                    // onClick={() => setStep("crypto")}
                    onClick={addAsset}
                    className="text-left p-1 px-2 text-white bg-appPurple rounded mx-auto block w-full"
                  >
                    <h3 className="text-sm">Crypto Wallets</h3>
                    <p className="text-xs">E.g BTC, ETH, SOL, USDT, etc</p>
                  </button>
                  <button
                    onClick={generateFiatWallet}
                    className="text-left p-1 px-2 text-white bg-appPurple rounded mx-auto block w-full"
                  >
                    <h3 className="text-sm">Fiat Wallets</h3>
                    <p className="text-xs">E.g USD, NGN, ZAR, etc</p>
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (step === "crypto") {
    return (
      <div className="rounded-md h-fit max-w-80 md:max-w-96 grow border bg-white pb-2">
        <div className="bg-[#fcfcfc] rounded-tl-md rounded-tr-md p-4">
          <button onClick={() => setModal(null)} className="flex items-center">
            <ChevronLeftIcon className="w-4 h-4 stroke-[2px]" />
            <p className="text-sm font-semibold">Go Back</p>
          </button>
        </div>

        <div className="overflow-y-auto max-h-[600px]">
          <div className="px-4">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="border-none outline-none rounded-md bg-[#fcfcfc] p-2 mt-2 w-full block"
              placeholder="Search..."
            />
          </div>

          <div className="px-4 my-4">
            <p>You can add as many assets as an admin</p>
          </div>

          {loading ? (
            <div className="py-28">
              <LoadingSpinner className="mx-auto" />
            </div>
          ) : (
            <ul>
              {assets
                .toSorted((a, b) => a.label.localeCompare(b.label))
                .filter((x) => {
                  if (searchTerm) {
                    return (x.label as string)
                      .toLowerCase()
                      .includes(searchTerm.toLowerCase());
                  }
                  return true;
                })
                .map((wallet, idx: number) => (
                  <li key={idx}>
                    <div className="flex justify-between items-center py-2 pl-2 md:pl-4">
                      <div className="flex gap-2.5">
                        <input
                          type="checkbox"
                          disabled={Boolean(
                            wallets.find((w) => w.symbol === wallet.assetType)
                          )}
                          checked={picked?.includes(wallet.assetType)}
                          onChange={(e) =>
                            setPicked((prev: string[] | undefined) => {
                              if (e.target.checked)
                                return [
                                  ...(prev as string[]),
                                  wallet.assetType as string,
                                ];
                              return prev?.filter(
                                (val) => val !== wallet.assetType
                              );
                            })
                          }
                          name={wallet.assetType}
                        />
                        <CryptoIcon assetType={wallet.assetType!} />
                        <div>
                          <p className="">{wallet.label}</p>
                          <p className="text-xs text-appGrayTextLight">
                            {wallet.assetType}
                          </p>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
            </ul>
          )}
        </div>

        <div className="px-2">
          <button
            onClick={addAsset}
            className={`bg-appPurple text-white rounded block w-full py-1.5 mt-2`}
          >
            Add wallets
          </button>
        </div>
      </div>
    );
  }
  return <></>;
}

export default AddAssetModalAdmin;
