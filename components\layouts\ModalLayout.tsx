'use client';
import useStore from "@/store";

function ModalLayout() {
  const { currentModal, setModal } = useStore((state: any) => state);

  const handleOutsideclick = (e: any) => {
    if (e.target.classList.contains("modal-layout")) {
      setModal(null);
    }
  }

  if (!currentModal) return null;

  return (
    <div
      className="modal-layout fixed top-0 left-0 pt-16 w-screen h-screen bg-[black]/[.5] z-[101]
    justify-center flex overflow-x-hidden overflow-y-auto
    inset-0 outline-none focus:outline-none"
      onClick={handleOutsideclick}
    >
      {currentModal}
    </div>
  );
}

export default ModalLayout;