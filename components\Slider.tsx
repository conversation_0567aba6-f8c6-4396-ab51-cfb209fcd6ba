import { useEffect } from "react";
import Image from "next/image";

const Slider = () => {
  useEffect(() => {
    const initSlider = () => {
      const imageList = document.querySelector<HTMLUListElement>(
        ".slider-wrapper .image-list"
      );
      const slideButtons = document.querySelectorAll<HTMLButtonElement>(
        ".slider-wrapper .slide-button"
      );
      const sliderScrollbar = document.querySelector<HTMLDivElement>(
        ".container .slider-scrollbar"
      );
      const scrollbarThumb =
        sliderScrollbar?.querySelector<HTMLDivElement>(".scrollbar-thumb");
      if (!imageList || !slideButtons || !sliderScrollbar || !scrollbarThumb)
        return;

      const maxScrollLeft = imageList.scrollWidth - imageList.clientWidth;

      // Handle scrollbar thumb drag
      scrollbarThumb.addEventListener("mousedown", (e) => {
        const startX = e.clientX;
        const thumbPosition = scrollbarThumb.offsetLeft;
        const maxThumbPosition =
          sliderScrollbar.getBoundingClientRect().width -
          scrollbarThumb.offsetWidth;

        // Update thumb position on mouse move
        const handleMouseMove = (e: MouseEvent) => {
          const deltaX = e.clientX - startX;
          const newThumbPosition = thumbPosition + deltaX;

          // Ensure the scrollbar thumb stays within bounds
          const boundedPosition = Math.max(
            0,
            Math.min(maxThumbPosition, newThumbPosition)
          );
          const scrollPosition =
            (boundedPosition / maxThumbPosition) * maxScrollLeft;

          scrollbarThumb.style.left = `${boundedPosition}px`;
          imageList.scrollLeft = scrollPosition;
        };

        // Remove event listeners on mouse up
        const handleMouseUp = () => {
          document.removeEventListener("mousemove", handleMouseMove);
          document.removeEventListener("mouseup", handleMouseUp);
        };

        // Add event listeners for drag interaction
        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
      });

      // Slide images according to the slide button clicks
      slideButtons.forEach((button) => {
        button.addEventListener("click", () => {
          const direction = button.id === "prev-slide" ? -1 : 1;
          const scrollAmount = imageList.clientWidth * direction;
          imageList.scrollBy({ left: scrollAmount, behavior: "smooth" });
        });
      });

      // Show or hide slide buttons based on scroll position
      const handleSlideButtons = () => {
        if (slideButtons.length === 2) {
          slideButtons[0].style.display =
            imageList.scrollLeft <= 0 ? "none" : "flex";
          slideButtons[1].style.display =
            imageList.scrollLeft >= maxScrollLeft ? "none" : "flex";
        }
      };

      // Update scrollbar thumb position based on image scroll
      const updateScrollThumbPosition = () => {
        const scrollPosition = imageList.scrollLeft;
        const thumbPosition =
          (scrollPosition / maxScrollLeft) *
          (sliderScrollbar.clientWidth - scrollbarThumb.offsetWidth);
        scrollbarThumb.style.left = `${thumbPosition}px`;
      };

      // Call these two functions when image list scrolls
      imageList.addEventListener("scroll", () => {
        updateScrollThumbPosition();
        handleSlideButtons();
      });
    };

    initSlider();

    window.addEventListener("resize", initSlider);

    return () => {
      window.removeEventListener("resize", initSlider);
    };
  }, []);

  return (
    <div className="my-10">
      <div className="slider-wrapper relative w-full">
        <ul className="image-list flex gap-5 overflow-x-auto">
          {Array.from({ length: 8 }, (_, i) => (
            <li key={i} className="relative flex-none">
              <div className="relative w-[325px] h-[400px]">
                <Image
                  src={`/images/img-${i + 1}.jpg`}
                  alt={`img-${i + 1}`}
                  fill
                  sizes="325px"
                  className="image-item rounded-lg object-cover"
                  priority={i < 2} // Load first two images immediately
                  loading={i < 2 ? "eager" : "lazy"}
                />
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default Slider;
