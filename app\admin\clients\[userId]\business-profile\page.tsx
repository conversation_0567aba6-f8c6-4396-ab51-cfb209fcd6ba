"use client";
import Image from 'next/image'
import Link from 'next/link'
import <PERSON><PERSON><PERSON> from "@/assets/images/tick-purple.svg";
import Select from "react-select";
import { FormEvent, useState } from 'react';
import { adminApi } from '@/app/utils';

function BusinessPage({ params }: { params: { userId: string }}) {
  const [formDetails, setFormDetails] = useState({
    kyc_status: "",
    kyc_reason: ""
  });
  console.log(params);

  const updateKYCStatus = (e: FormEvent) => {
    e.preventDefault();

    adminApi.patch("/v1/admin/users/" + params.userId + "/send-kyc-update", {
      status: formDetails.kyc_status,
      reason: formDetails.kyc_reason
    })
    .then(res => {
      console.log(res);
    })
    .catch(err => console.log(err));
  }

  return (
    <div className='py-6'>
      <form className="mt-6 px-2 md:px-4">
        <Image
          src={"https://ui-avatars.com/api/?name=" + "Business Name"}
          width={70}
          height={70}
          alt="user's profile photo"
          className="w-[70px] h-[70px] rounded-full"
        />

        <div className="mt-8 flex flex-col gap-4 max-w-[700px]">
          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Business Name
            </label>
            <input
              name="email"
              onChange={e => e.preventDefault()}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>
          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Company ID
            </label>
            <input
              name="email"
              onChange={e => e.preventDefault()}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>
          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Email Address
            </label>
            <input
              name="email"
              onChange={e => e.preventDefault()}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Business Type
            </label>
            <input
              name="country"
              onChange={e => e.preventDefault()}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Industry
            </label>
            <input
              name="phone"
              onChange={e => e.preventDefault()}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>
          <button className='text-center px-4 py-2 bg-appPurple text-white rounded w-full'>Update</button>
        </div>
      </form>

      <form>
        <div className="border-b border-b-[#eeeeee] px-4 py-4">
          <h2 className='text-black'>Settlement accounts</h2>
          {/* TODO: Probably a link to request account edit help from the support team */}
          <Link href={"#"} className='font-light text-sm text-appPurple'>Edit</Link>
        </div>

        {/* <div className='px-4 py-4 mt-6 space-y-4 max-w-[500px]'>
          <div className='border px-2.5 py-4 rounded flex justify-between w-full'>
            <div>
              <h3>**********  - ACCESS Bank</h3>
              <p className='text-appGrayTextLight font-light text-sm'>Adewale Adedamola</p>
            </div>
            <button>
              <Image src={PurpleTick} alt='selected' className='w-5 stroke-appPurple' />
            </button>
          </div>
          <div className='border px-2.5 py-4 rounded flex justify-between w-full'>
            <div>
              <h3>**********  - ACCESS Bank</h3>
              <p className='text-appGrayTextLight font-light text-sm'>Adewale Adedamola</p>
            </div>
            <button>
              <div className="w-4 h-4 border rounded-full"></div>
            </button>
          </div>
          <button className='text-center px-4 py-2 bg-appPurple text-white rounded w-full'>Update</button>
        </div> */}
      </form>

      <form onSubmit={updateKYCStatus} className="mt-6 px-2 md:px-4">
        <div className="mt-8 flex flex-col gap-4 max-w-[700px]">
          <Select
            options={[{ label: "Approve", value: "SUCCESSFULL" }, { label: "Pending", value: "PENDING" }, { label: "Reject", value: "REJECTED" }]}
            placeholder="KYC status"
            onChange={(e: any) =>
              setFormDetails(prev => ({ ...prev, kyc_status: e.value }))
            }
            styles={{
              control: (baseStyles) => ({
                ...baseStyles,
                border: "1px solid #CACACA",
                boxShadow: "none",
                ":focus-within": {
                  border: "1px solid #7928FF",
                },
                padding: "4px 2px 4px 2px",
                fontSize: "14px",
              }),
            }}
          />
          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Reason
            </label>
            <textarea
              name="kyc_reason"
              value={formDetails.kyc_reason}
              onChange={e => setFormDetails(prev => ({ ...prev, kyc_reason: e.target.value }))}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>
          <button className='text-center px-4 py-2 bg-appPurple text-white rounded w-full'>Update</button>
        </div>
      </form>
    </div>
  )
}

export default BusinessPage