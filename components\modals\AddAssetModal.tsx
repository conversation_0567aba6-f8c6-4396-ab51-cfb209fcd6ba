import React, { useEffect, useState } from "react";
import { ChevronLeftIcon, MagnifyingGlassIcon } from "../icons";
import useStore from "@/store";
import LoadingSpinner from "../LoadingSpinner";
import { api } from "@/app/utils";
import CryptoIcon from "../CryptoIcon";

function AddAssetModal({ wallets }: { wallets: Record<string, any>[] }) {
  const { setModal } = useStore();
  const [loading, setLoading] = useState(true);
  const [assets, setAssets] = useState<Record<any, any>[]>([]);
  const [picked, setPicked] = useState<string[] | undefined>([]);
  const [walletFilterSearch, setWalletFilterSearch] = useState("");

  const MAX_ASSET_COUNT = 5;

  useEffect(() => {
    console.log(picked);
  }, [picked]);

  const addAsset = () => {
    setLoading(true);
    console.log(wallets);
    api
      .post("/v1/wallets/add-assets", {
        assets: picked?.filter(
          (pick) => !Boolean(wallets.find((x) => x.symbol === pick))
        ),
      })
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          window.location.reload();
        }
        console.log(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    setLoading(true);
    setPicked(wallets.map((wallet) => wallet.symbol));
    api("/v1/wallets/available-assets")
      .then((res) => {
        console.log(res);
        setAssets(res.data);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [wallets]);

  return (
    <div className="rounded-md h-fit max-w-80 md:max-w-96 grow border bg-white pb-2">
      <div className="bg-[#fcfcfc] rounded-tl-md rounded-tr-md p-4">
        <button onClick={() => setModal(null)} className="flex items-center">
          <ChevronLeftIcon className="w-4 h-4 stroke-[2px]" />
          <p className="text-sm font-semibold">Go Back</p>
        </button>
      </div>

      <div className="overflow-y-auto max-h-[600px]">
        <div className="px-4 my-4">
          <p>You can only have a maximum of {MAX_ASSET_COUNT} assets</p>
        </div>

        {loading ? (
          <div className="py-28">
            <LoadingSpinner className="mx-auto" />
          </div>
        ) : (
          <>
            <div className="px-4">
              <div className="inp flex items-center gap-2 bg-[#fbfbfb] rounded-md p-2 grow">
                <MagnifyingGlassIcon className="w-4 h-4 text-[#b1b1b1]" />
                <input
                  type="text"
                  onChange={(e) => setWalletFilterSearch(e.target.value.trim())}
                  className="border-none outline-none bg-transparent grow placeholder:text-[#b1b1b1]"
                  placeholder="Search wallets"
                />
              </div>
            </div>
            <ul>
              {assets
                .filter((asset) => {
                  if (walletFilterSearch) {
                    return (asset?.label as string)
                      .toLowerCase()
                      .startsWith(walletFilterSearch.toLowerCase());
                  }
                  return true;
                })
                .toSorted((a: any, b: any) => a?.label.localeCompare(b.label))
                .map((wallet, idx: number) => (
                  <li key={idx}>
                    <div className="flex justify-between items-center py-2 pl-2 md:pl-4">
                      <div className="flex gap-2.5">
                        <input
                          type="checkbox"
                          disabled={Boolean(
                            wallets.find((w) => w.symbol === wallet.assetType)
                          )}
                          checked={picked?.includes(wallet.assetType)}
                          onChange={(e) =>
                            setPicked((prev: string[] | undefined) => {
                              if (e.target.checked)
                                return [
                                  ...(prev as string[]),
                                  wallet.assetType as string,
                                ];
                              return prev?.filter(
                                (val) => val !== wallet.assetType
                              );
                            })
                          }
                          name={wallet.assetType}
                        />
                        <CryptoIcon assetType={wallet.assetType!} />
                        <div>
                          <p className="">{wallet.label}</p>
                          <p className="text-xs text-appGrayTextLight">
                            {wallet.assetType}
                          </p>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
            </ul>
          </>
        )}
      </div>

      <div className="px-2">
        <button
          onClick={addAsset}
          className={`bg-appPurple text-white rounded block w-full py-1.5 mt-2 ${
            picked!.length > MAX_ASSET_COUNT ? "opacity-40" : ""
          }`}
          disabled={(picked?.length || 0) > MAX_ASSET_COUNT}
        >
          Add wallets
        </button>
      </div>
    </div>
  );
}

export default AddAssetModal;
