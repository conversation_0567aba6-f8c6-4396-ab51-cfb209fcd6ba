"use client";
import useStore from '@/store';
import Image from 'next/image';
import Link from 'next/link'
import { usePathname } from 'next/navigation';
import React, { ReactNode } from 'react'
import flincapLogo from "@/assets/images/logo-no-text.svg";
import GoodTick from "@/assets/images/good-tick.svg";

function Layout({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const { user } = useStore(state => state);
  return (
    <div>
      {user?.payoutService ? (
        <>
          <nav className='bg-[#fcfcfc]'>
            <ul className='flex overflow-auto'>
              <li><Link href="/dashboard/products/payments" className={`px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${pathname === "/dashboard/products/payments" ? "border-b-[#71bbff] text-[#71bbff]" : ""}`}>Overview</Link></li>
              <li><Link href="/dashboard/products/payments/transactions" className={`px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${pathname.startsWith("/dashboard/products/payments/transactions") ? "border-b-[#71bbff] text-[#71bbff]" : ""}`}>Transactions</Link></li>
              <li><Link href="/dashboard/products/payments/customers" className={`px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${pathname.startsWith("/dashboard/products/payments/customers") ? "border-b-[#71bbff] text-[#71bbff]" : ""}`}>My Customers</Link></li>
            </ul>
          </nav>

          <main>{children}</main>
        </>
      ) : (
        <section className="py-6 px-4">
          <div className="border border-[#71bbff] rounded-md bg-[#f0f8ff] max-w-xl mx-auto px-4 py-6 text-center">
            <Image src={flincapLogo} alt="." className="mx-auto" />
            {/* <p className="text-black my-2">You need to subscribe first</p> */}
            <p className="text-black my-2">You need to setup your KYC details first</p>
            <p className="font-light text-base leading-7">To activate a bank account for payouts you&apos;ll need to submit details for verification.</p>
            <Link href="/dashboard/ql/settings" className="bg-black text-white px-3 py-2 block w-fit mx-auto rounded-md text-sm mt-2">Start KYC</Link>
          </div>

          <div className='flex gap-1.5 items-center justify-center mt-6'>
            <p className='text-appGrayTextLight text-sm'>You are eligible:</p>
            <Image src={GoodTick} alt='yes' className='w-4' />
          </div>
        </section>
      )}
    </div>
  )
}

export default Layout