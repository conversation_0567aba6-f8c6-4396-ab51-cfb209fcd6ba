"use client";
import { adminApi, api } from "@/app/utils";
import { Data } from "@/types/types";
import { useEffect, useState } from "react";
import { useDashboardData } from "../layout";
import { WalletType } from "@/types/types";
import useStore from "@/store";

export default function Page({ params }: { params: { userId: string } }) {
  const [wallets, setWallets] = useState<WalletType[]>([]);
  const [loading, setLoading] = useState(false);
  const { user } = useStore();

  useEffect(() => {
    if (typeof window !== "undefined") {
      adminApi
        .get("/v1/wallets")
        .then((res) => {
          if (res.status.toString().startsWith("2")) {
            setWallets(res.data.data.wallets);
          } else {
            console.log("Couldn't fetch user");
          }
        })
        .catch((err) => {
          console.log(err);
          if (err.response.data) {
            alert("User BVN or other data may be invalid");
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, []);

  if (!user || user?.role == "Developer") {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <h2 className="text-lg font-semibold mb-2">Not authorized</h2>
        <p className="text-gray-600">
          You do not have permission to view this page.
        </p>
      </div>
    );
  }

  return (
    <div className="font-circularStd">
      <div className="max-w-[90%] p-4">
        <h2 className="text-2xl">Wallets</h2>
        <p className="text-sm mt-3">
          Kindly note that your USDT BEP20 address is EVM-compatible. That
          means, it is the same address for USDT ERC20, POLYGON, and USDC BEP20,
          ERC20, and POLYGON
        </p>
        <table className="w-full mt-10">
          <thead className="text-[14px] text-ash rounded-lg bg-ash1 border-b border-b-ash2">
            <tr className="text-left">
              <th className="p-3 font-normal flex items-center gap-2">
                Name/Chain
              </th>
              <th className="p-3 font-normal">Address/Account Number</th>
            </tr>
          </thead>
          <tbody>
            {wallets
              .filter((wallet: WalletType) => wallet.assetType !== "USD")
              .sort((a: WalletType, b: WalletType) => {
                if (a.walletType === "FIAT" && b.walletType !== "FIAT")
                  return 1;
                if (a.walletType !== "FIAT" && b.walletType === "FIAT")
                  return -1;
                return 0;
              })
              .map((wallet: any, idx: number) => {
                return (
                  <tr
                    className="text-xs text-black hover:text-appPurpleDark border-b border-b-ash2 hover:bg-appWhite"
                    key={idx}
                  >
                    <td className="p-3 flex items-center gap-2 whitespace-nowrap">
                      <span className="font-semibold">{wallet.name}</span>
                    </td>

                    <td className="p-3 whitespace-nowrap">
                      {wallet.walletType === "CRYPTO" ? (
                        <p>{wallet.address}</p>
                      ) : (
                        <>
                          <p>{wallet.account_name}</p>
                          <p className="text-xs font-light mt-1">
                            {wallet.account_num} {wallet.bank_name}
                          </p>
                        </>
                      )}
                    </td>
                  </tr>
                );
              })}
          </tbody>
        </table>
      </div>
    </div>
  );
}
