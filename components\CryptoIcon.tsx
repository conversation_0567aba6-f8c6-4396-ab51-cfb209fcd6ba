import Image from "next/image";
import React from "react";

import NairaIcon from "@/assets/images/wallets/naira.svg";
import DollarIcon from "@/assets/images/wallets/dollar.svg";
import BitcoinIcon from "@/assets/images/wallets/bitcoin.svg";
import EthereumIcon from "@/assets/images/wallets/ethereum.svg";
import LitecoinIcon from "@/assets/images/wallets/litecoin-ltc-logo.svg";
import BitcoinCash from "@/assets/images/wallets/bitcoin-cash-bch-logo.svg";
import EthcoinIcon from "@/assets/images/wallets/eth.svg";
import AvaxcoinIcon from "@/assets/images/wallets/avax.svg";
import BUSDcoinIcon from "@/assets/images/wallets/busd.svg";
import XLMcoinIcon from "@/assets/images/wallets/xlm.svg";
import SOLcoinIcon from "@/assets/images/wallets/sol.svg";
import BasecoinIcon from "@/assets/images/wallets/base.svg";
import Matic from "@/assets/images/wallets/matic.svg";
import TronIcon from "@/assets/images/wallets/tron.svg";
import ARBcoinIcon from "@/assets/images/wallets/arb.svg";
import BSCcoinIcon from "@/assets/images/wallets/bnb.svg";
import CELOcoinIcon from "@/assets/images/wallets/celo.svg";
import USDTIcon from "@/assets/images/wallets/usdt.svg";
import USDCIcon from "@/assets/images/wallets/usdc.svg";
import CUSDCIcon from "@/assets/images/wallets/cusdc.svg";
import USDCXLMIcon from "@/assets/images/wallets/usdc_xlm.svg";
import OPIcon from "@/assets/images/wallets/op.svg";
import EurCIcon from "@/assets/images/wallets/euroc.svg";
import { iWalletSymbol } from "@/types/utils";

// https://cryptofonts.com/icons.html

function CryptoIcon({
  assetType,
  className,
  showChain,
}: {
  assetType: iWalletSymbol;
  className?: string;
  showChain?: boolean;
}) {
  if (showChain && assetType.includes("_")) {
    const [tokenSym, chainSym] = assetType.split("_");
    return (
      <div className="relative">
        <Image
          src={
            {
              NGN: NairaIcon,
              USD: DollarIcon,
              BTC: BitcoinIcon,
              ETH: EthereumIcon,
              TRON: TronIcon,
              LTC: LitecoinIcon,
              BCH: BitcoinCash,
              MATIC: Matic,
              AVAX: AvaxcoinIcon,
              XLM: XLMcoinIcon,
              SOL: SOLcoinIcon,
              ARB: ARBcoinIcon,
              BSC: BSCcoinIcon,
              BUSD: BUSDcoinIcon,
              CELO: CELOcoinIcon,
              BASE: BasecoinIcon,
              ETH_ARB: EthcoinIcon,
              USDT_TRON: USDTIcon,
              USDC: USDCIcon,
              CUSD: CUSDCIcon,
              USDC_XLM: USDCXLMIcon,
              USDT_BSC: USDTIcon,
              ETH_OP: OPIcon,
              CEUR: EurCIcon,
              USDT_MATIC: Matic,
              OP: OPIcon,
              USDT_SOL: Matic,
              USDC_MATIC: USDCIcon,
              EUROC: EurCIcon,
              BUSD_BSC: BUSDcoinIcon,
              USDC_BSC: BSCcoinIcon,
              USDT: USDTIcon,
              USDC_TRON: USDCIcon,
              USDT_ARB: USDTIcon,
              USDC_ARB: USDCIcon,
              USDC_SOL: USDCIcon,
              CLDX_ARB: ARBcoinIcon,
              CLDXT_ARB: ARBcoinIcon,
              EURC_XLM: EurCIcon,
              USDCe_ARB: USDCIcon,
            }[tokenSym]
          }
          alt="wallet icon"
          className={className ? className : "w-8 h-8 rounded-full"}
        />
        <Image
          src={
            {
              NGN: NairaIcon,
              USD: DollarIcon,
              BTC: BitcoinIcon,
              ETH: EthereumIcon,
              TRON: TronIcon,
              LTC: LitecoinIcon,
              BCH: BitcoinCash,
              MATIC: Matic,
              AVAX: AvaxcoinIcon,
              XLM: XLMcoinIcon,
              SOL: SOLcoinIcon,
              ARB: ARBcoinIcon,
              BSC: BSCcoinIcon,
              BUSD: BUSDcoinIcon,
              CELO: CELOcoinIcon,
              BASE: BasecoinIcon,
              ETH_ARB: EthcoinIcon,
              USDT_TRON: USDTIcon,
              USDC: USDCIcon,
              CUSD: CUSDCIcon,
              USDC_XLM: USDCXLMIcon,
              USDT_BSC: USDTIcon,
              ETH_OP: OPIcon,
              CEUR: EurCIcon,
              USDT_MATIC: Matic,
              OP: OPIcon,
              USDT_SOL: Matic,
              USDC_MATIC: USDCIcon,
              EUROC: EurCIcon,
              BUSD_BSC: BUSDcoinIcon,
              USDC_BSC: BSCcoinIcon,
              USDT: USDTIcon,
              USDC_TRON: USDCIcon,
              USDT_ARB: USDTIcon,
              USDC_ARB: USDCIcon,
              USDC_SOL: USDCIcon,
              CLDX_ARB: ARBcoinIcon,
              CLDXT_ARB: ARBcoinIcon,
              EURC_XLM: EurCIcon,
              USDCe_ARB: USDCIcon,
            }[chainSym]
          }
          alt="wallet icon"
          className={
            className
              ? className
              : "w-5 h-5 rounded-full absolute translate-x-4 -translate-y-4"
          }
        />
      </div>
    );
  }

  return (
    <Image
      src={
        {
          NGN: NairaIcon,
          USD: DollarIcon,
          BTC: BitcoinIcon,
          ETH: EthereumIcon,
          TRON: TronIcon,
          LTC: LitecoinIcon,
          BCH: BitcoinCash,
          MATIC: Matic,
          AVAX: AvaxcoinIcon,
          XLM: XLMcoinIcon,
          SOL: SOLcoinIcon,
          ARB: ARBcoinIcon,
          BSC: BSCcoinIcon,
          BUSD: BUSDcoinIcon,
          CELO: CELOcoinIcon,
          BASE: BasecoinIcon,
          ETH_ARB: EthcoinIcon,
          USDT_TRON: USDTIcon,
          USDC: USDCIcon,
          CUSD: CUSDCIcon,
          USDC_XLM: USDCXLMIcon,
          USDT_BSC: USDTIcon,
          ETH_OP: OPIcon,
          CEUR: EurCIcon,
          USDT_MATIC: Matic,
          OP: OPIcon,
          USDT_SOL: Matic,
          USDC_MATIC: USDCIcon,
          EUROC: EurCIcon,
          BUSD_BSC: BUSDcoinIcon,
          USDC_BSC: BSCcoinIcon,
          USDT: USDTIcon,
          USDC_TRON: USDCIcon,
          USDT_ARB: USDTIcon,
          USDC_ARB: USDCIcon,
          USDC_SOL: USDCIcon,
          CLDX_ARB: ARBcoinIcon,
          CLDXT_ARB: ARBcoinIcon,
          EURC_XLM: EurCIcon,
          USDCe_ARB: USDCIcon,
          "USDT TRC20": USDTIcon,
          "USDT BEP20": USDTIcon,
        }[assetType]
      }
      alt="wallet icon"
      className={className ? className : "w-8 h-8 rounded-full"}
    />
  );
}

export default CryptoIcon;
