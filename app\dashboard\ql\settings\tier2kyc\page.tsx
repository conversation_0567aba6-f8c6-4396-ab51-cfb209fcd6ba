"use client";
import { api, CONSTANTS, getToken } from "@/app/utils";
import Select from "react-select";
import React, { FormEvent, useEffect, useRef, useState } from "react";
import LoadingSpinner from "@/components/LoadingSpinner";
import axios from "axios";

function Page() {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [formDetails, setFormDetails] = useState({
    identityType: "",
    identityNumber: "",
    identityDocument: null,
    photo: null,
  });
  const [kycData, setKycData] = useState<any>(null);
  const identityDocument: any = useRef(null);
  const photo: any = useRef(null);

  useEffect(() => {
    // Fetch user data
    // setLoading(true);
    api
      .get("/v1/settings/business")
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          const { photo, identityDocument, identityType, identityNumber } =
            res.data.data.kyc;
          setKycData(res.data.data.kyc);
          setFormDetails({
            identityType,
            identityNumber,
            identityDocument,
            photo,
          });
        } else {
          console.log("Couldn't fetch user");
        }
      })
      .catch((err) => {
        console.log(err);
        if (err.response.data) {
          alert("User data fetch failed");
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormDetails((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const editProfile = async (e: FormEvent) => {
    e.preventDefault();

    setLoading(true);

    try {
      const res = await axios.post(
        `${CONSTANTS.SERVER_URL}/v1/settings/kyc`,
        {
          ...formDetails,
          photo: photo.current.files[0],
          identityDocument: identityDocument.current.files[0],
        },
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: "Bearer " + getToken(),
          },
        }
      );

      if (res.status.toString().startsWith("2")) {
        setLoading(false);
        console.log("response", res);
        window.location.reload();
      } else {
        setErrorMessage(res.data.message);
      }
    } catch (err) {
      console.log(err);
      setErrorMessage("Failed to update profile.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="md:px-6 px-4 pb-10 pt-4">
      <section>
        {kycData ? (
          <div>
            <h2>You are now on Tier 2</h2>
          </div>
        ) : (
          <form
            onSubmit={editProfile}
            className="flex flex-col gap-4 max-w-[700px]"
          >
            <p className="text-sm text-red-500">{errorMessage}</p>
            <Select
              options={[
                "NIN",
                "Passport",
                "Voter's Card",
                "Drivers License",
              ].map((type) => ({
                value: type,
                label: type,
              }))}
              placeholder="Select Identity Type"
              isSearchable={true}
              isDisabled={formDetails.identityType === "true"}
              onChange={(e: any) =>
                setFormDetails({ ...formDetails, identityType: e.value })
              }
              value={{
                label: formDetails.identityType,
                value: formDetails.identityType,
              }}
              styles={{
                control: (baseStyles) => ({
                  ...baseStyles,
                  border: "1px solid #CACACA",
                  boxShadow: "none",
                  ":focus-within": {
                    border: "1px solid #7928FF",
                  },
                  padding: "4px 2px 4px 2px",
                  fontSize: "14px",
                }),
              }}
            />

            <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
              <label className="text-[#999999] text-xs font-light">
                Identity Number
              </label>
              <input
                name="identityNumber"
                value={formDetails.identityNumber}
                onChange={handleChange}
                className="w-full bg-transparent outline-none text-sm"
              />
            </div>

            <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
              <label className="text-[#999999] text-xs font-light">
                Identity Document
              </label>
              <input
                name="identityDocument"
                type="file"
                ref={identityDocument}
                onChange={(e: any) => (identityDocument.current = e.target)}
                className="w-full bg-transparent outline-none text-sm"
              />
            </div>

            <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
              <label className="text-[#999999] text-xs font-light">Photo</label>
              <input
                name="photo"
                type="file"
                ref={photo}
                onChange={(e: any) => (photo.current = e.target)}
                className="w-full bg-transparent outline-none text-sm"
              />
            </div>

            <button className="bg-appPurple text-white px-4 py-2 rounded">
              {!loading ? "Update" : <LoadingSpinner className="mx-auto" />}
            </button>
          </form>
        )}
      </section>
    </div>
  );
}

export default Page;
