import useStore from "@/store";
import React from "react";

function SuccessModal({ message }: { message: string }) {
  return (
    <div className="bg-white rounded-md h-fit max-w-80 md:max-w-96 p-4 grow">
      <svg className="mx-auto mb-3" width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="60" height="60" rx="30" fill="#27AE60" />
        <path d="M22 31.5106L28.3492 37L37.5069 23" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
      <div className="text-center">
        <h2 className="text-xl font-semibold text-center">Success</h2>
        <p className="text-center text-sm mt-4">
          {message}
        </p>
      </div>
      <button
        onClick={() => {
          window.location.href = "/dashboard";
        }}
        className="mx-auto block text-sm text-center text-appPurple mt-8 mb-3"
      >
        Close
      </button>
    </div>
  );
}

export default SuccessModal;
