"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import NotransactionIcon from "@/assets/images/no-data.svg";
import { adminApi, getTokenName } from "@/app/utils";
import { toast, ToastContainer } from "react-toastify";
import { exportReceiptAsImage, generatePDF } from "@/lib/utils";
import { useUserDetailsContext } from "@/app/layout";
import Analytics from "@/components/admin/Analytics";

import RecentTransactionsTable from "@/components/dashboard/RecentTransactionsTable";

function Page({ params }: { params: { userId: string } }) {
  const [loading, setLoading] = useState(false);
  const [transactions, setTransactions] = useState([]);
  const [transactionLoading, setTransactionLoading] = useState<any>({});
  const { bankData } = useUserDetailsContext();

  useEffect(() => {
    setLoading(true);
    adminApi
      .get("/v1/admin/users/" + params.userId + "/transactions")
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          console.log(res.data.data);
          setTransactions(res.data.data);
          console.log(res.data.data);
        } else {
          console.log("Couldn't fetch user");
        }
      })
      .catch((err) => {
        console.log(err);
        if (err.response.data) {
        }
      })
      .finally(() => {});
  }, [params.userId]);

  return (
    <>
      <ToastContainer />
      <section className="">
        <Analytics userId={params.userId} />
        <RecentTransactionsTable
          transactions={transactions}
          fiatBankData={bankData}
          exportReceiptAsImage={exportReceiptAsImage}
          generatePDF={generatePDF}
        />

        {!transactions.length ? (
          <div className="text-center flex flex-col justify-center items-center min-h-80 w-full">
            <Image
              src={NotransactionIcon}
              alt="."
              className="block mx-auto grayscale"
            />
            <p className="my-2.5 text-black">No transactions</p>
            <p className="font-light">
              Once you start trading transactions, they <br /> will appear here.{" "}
              <span className="text-appPurple">Start now</span>
            </p>
          </div>
        ) : null}
      </section>
    </>
  );
}

export default Page;
