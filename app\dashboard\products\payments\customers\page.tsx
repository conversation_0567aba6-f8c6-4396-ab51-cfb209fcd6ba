import Image from 'next/image'
import React from 'react'
import creditIcon from "@/assets/images/creditIcon.svg";
import NotransactionIcon from "@/assets/images/no-data.svg";
import { UpDownIcon } from '@/components/icons';

function Page() {
  return (
    <div className='px-4'>
      <article className="mt-8">
        <div className="text-sm flex flex-wrap gap-2 justify-between items-center mb-2">
          <h3 className="font-semibold">History</h3>
          <div className="filters flex gap-1">
            <div className="flex gap-1">
              <p className="text-appGrayText2">Showing:</p>
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"All Time"}>All Time</option>
              </select>
            </div>
            <div className="flex gap-1">
              <p className="text-appGrayText2">Filter:</p>
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"None"}>None</option>
              </select>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap justify-between gap-6 py-4 max-w-[1250px] h-auto overflow-x-auto">
          <table className="w-full">
            <thead className="text-[14px] text-ash rounded-lg bg-ash1 border-b border-b-ash2">
              <tr className="text-left">
                <th className="p-3 font-normal flex items-center gap-2">
                  Type <UpDownIcon className="w-3 h-3" />
                </th>
                <th className="p-3 font-normal">Amount</th>
                <th className="p-3 font-normal">Trans ID</th>
                <th className="p-3 font-normal">Receiver</th>
                <th className="p-3 font-normal">Fee</th>
                <th className="p-3 font-normal">Status</th>
                <th className="p-3 font-normal flex items-center gap-2">
                  Date <UpDownIcon className="w-3 h-3" />
                </th>
              </tr>
            </thead>
            <tbody>
              {!null && Array(0).fill(0).map((_, idx) => {
                return (
                  <tr
                    className="text-xs text-black hover:text-appPurpleDark border-b border-b-ash2 cursor-pointer hover:translate-x-2 transition-transform duration-300 ease-in-out hover:bg-appWhite"
                    key={idx}
                  >
                    <td className="p-3 flex items-center gap-2 whitespace-nowrap">
                      <Image
                        src={creditIcon}
                        alt="credit icon"
                        width={28}
                        height={28}
                      />
                      <span className="font-semibold">Credit</span>
                    </td>
                    <td className="p-3 font-semibold whitespace-nowrap">
                      $432
                    </td>
                    <td className="p-3 whitespace-nowrap">*********...</td>
                    <td className="p-3 whitespace-nowrap">
                      <p>John Doe</p>
                      <p className='text-xs font-light mt-1'>********* Access Bank</p>
                    </td>
                    <td className="p-3 whitespace-nowrap">$10</td>
                    <td className="p-3 text-[#13BF62] font-semibold whitespace-nowrap">
                      <div className="flex gap-1 items-center">
                        <span className="bg-[#13BF62] w-1 h-1"></span>
                        <span>Completed</span>
                      </div>
                    </td>
                    <td className="p-3 font-semibold whitespace-nowrap">
                      15th Aug, 2022, 5.00pm
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
          {!null ? (
            <div className="text-center flex flex-col justify-center items-center min-h-80 w-full">
              <Image src={NotransactionIcon} alt="." className="block mx-auto grayscale" />
              <p className="my-2.5 text-black">No customers yet</p>
              <p className="font-light">Once transactions are done on your api or payment links, they <br /> will appear here. <span className="text-appPurple">Start now</span></p>
            </div>
          ) : null}
        </div>
      </article>
    </div>
  )
}

export default Page