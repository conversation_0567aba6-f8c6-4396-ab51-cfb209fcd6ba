import Link from "next/link";
import { usePathname } from "next/navigation";
import { SignoutIcon } from "@/components/icons";
import Image from "next/image";
import { Home, Landmark, Settings, Wallet } from "lucide-react";
import useStore from "@/store";

export function SideNavigationBar({ logoutUser }: { logoutUser: () => void }) {
  const { user } = useStore();
  const pathname = usePathname();
  const sidebarItems = [
    { href: "/dashboard", label: "Overview", icon: <Home /> },
    user?.role !== "Developer" && {
      href: "/dashboard/treasury",
      label: "Treasury",
      icon: <Landmark />,
    },
    {
      href: "/dashboard/transactions",
      label: "Transactions",
      icon: <Wallet />,
    },
    {
      href: "/dashboard/ql/settings",
      label: "Settings",
      icon: <Settings />,
    },
    { href: "#logout", label: "Logout", icon: <SignoutIcon /> },
  ].filter(Boolean) as {
    href: string;
    label: string;
    icon: JSX.Element;
  }[];
  return (
    <nav
      className={`min-w-64 hidden md:flex flex-col justify-between last:self-end text-black text-[13px] font-normal overflow-y-hidden h-screen px-4 py-4 bg-white`}
    >
      <div>
        <Link href={"/"} className="logo pl-3 w-full block pt-4">
          <Image
            src={"/images/flincap-logo.svg"}
            alt="."
            width={100}
            className=""
            height={50}
            priority
          />
        </Link>

        <div className="flex flex-col gap-6 mt-8">
          <div>
            <ul className="max-w-56 space-y-1">
              {sidebarItems.map((item) => {
                const isActive = pathname === item.href;
                return (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className={`group pl-3 font-circularStd py-3.5 border text-[#8592AD] focus:text-[#7928FF] border-transparent flex rounded-lg gap-3 items-center ${
                        isActive ? "bg-[#EDE2FF]" : "hover:text-[#7928FF]"
                      }`}
                    >
                      {item.icon && (
                        <span
                          className={`mr-[3.5px] ${
                            isActive
                              ? "text-[#7928FF]"
                              : "group-hover:text-[#7928FF]"
                          }`}
                        >
                          {item.icon}
                        </span>
                      )}
                      <p>{item.label}</p>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </div>

      <Link
        href={"#logout"}
        onClick={logoutUser}
        className={`pl-3 py-3.5 w-full border border-transparent flex rounded-lg gap-3 items-center hover:bg-appPurple hover:text-white`}
      >
        <SignoutIcon className={`sidebar-icon -ml-0.5`} />
        <p>Logout</p>
      </Link>
    </nav>
  );
}
