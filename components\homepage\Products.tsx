import Link from "next/link";
import Image from "next/image";
import { Reveal } from "../animations/animations";

export default function Products() {
  return (
    <section className="py-16">
      {/* <h2 className="text-center text-3xl font-extrabold text-[#0A2540]">
        Everything Your OTC Crypto Exchange Needs
      </h2>
      <p className="text-center mb-2 py-3 text-[#425466]">
        Flincap addresses the extensive needs <br /> of growing OTC crypto
        exchanges
      </p> */}
      <Reveal>
        <div className="max-w-5xl mx-auto gap-4 grid grid-cols-1 md:grid-cols-10 md:grid-rows-2 md:max-h-[450px]">
          <Link
            href="/"
            className="bg-[#288BFF1A] w-11/12 px-7 py-6 mx-auto rounded-md md:px-10 md:w-full md:col-span-4 md:col-start-2"
          >
            <Image
              src={"/images/paper-plane.svg"}
              width={80}
              height={42}
              alt="."
            />
            <h3 className="font-extrabold text-lg py-3 font-circularStd">
              Flincap Sendar
            </h3>
            <p className="text-sm font-circularStd lg:max-w-64">
              Facilitates global money transfers.
            </p>
          </Link>
          <Link
            href="/"
            className="bg-[#FFF4EA] w-11/12 px-7 mx-auto rounded-md md:px-10 md:w-full md:col-span-4"
          >
            <Image
              src={"/images/exchange.svg"}
              width={70}
              height={32}
              alt="."
              className="mt-4"
            />
            <h3 className="font-extrabold text-lg py-3 font-circularStd">
              Flincap Exchange
            </h3>

            <p className="text-sm font-circularStd lg:max-w-64">
              Provides instant stablecoins onramp and offramp..
            </p>
          </Link>
          <Link
            href="/"
            className="bg-[#F2EBFF] w-11/12 px-7 py-6 mx-auto rounded-md md:px-10 md:w-full md:col-span-4 md:col-start-4"
          >
            <Image
              src={"/images/otc/hero-img.svg"}
              width={70}
              height={60}
              alt="."
            />
            <h3 className="font-extrabold text-lg py-3 font-circularStd">
              Flincap Payments
            </h3>
            <p className="text-sm font-circularStd lg:max-w-64">
              Enables businesses to make collections and payouts in local
              African currencies.
            </p>
          </Link>
        </div>

        <div className="max-w-6xl mx-auto gap-4 flex"></div>
      </Reveal>
    </section>
  );
}
