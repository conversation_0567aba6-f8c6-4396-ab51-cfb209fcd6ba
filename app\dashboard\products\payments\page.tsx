"use client";
import { api } from "@/app/utils";
import { XMarkIcon } from "@/components/icons";
import useStore from "@/store";
import { <PERSON><PERSON><PERSON> } from "@tremor/react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

const chartdata = [
  {
    date: 'Jan 23',
    Running: 167,
  },
  {
    date: 'Feb 23',
    Running: 125,
  },
  {
    date: 'Mar 23',
    Running: 156,
  },
  {
    date: 'Apr 23',
    Running: 165,
  },
  {
    date: 'May 23',
    Running: 153,
  },
  {
    date: 'Jun 23',
    Running: 124,
  },
  {
    date: 'Jul 23',
    Running: 164,
  },
  {
    date: 'Aug 23',
    Running: 123,
  },
  {
    date: 'Sep 23',
    Running: 132,
  },
];

function Page() {
  const { user } = useStore();
  const [showAlert, setShowAlert] = useState<boolean>(false);
  const [copiedBankNumber, setCopiedBankNumber] = useState<boolean>(false);
  const [data, setData] = useState<Record<string, any>>({});

  useEffect(() => {    
    // fetch and set payments data
    api("/v1/wallets/payments")
      .then(res => {
        setData(res.data.data);
      })
      .catch(err => {
        console.log(err);
      })
  }, []);

  useEffect(() => {
    if(typeof window !== 'undefined' && user) {
      if (localStorage.getItem("hideAlert1")) {
        setShowAlert(false);
      } else {
        !user?.bvn && setShowAlert(true);
      }
    }
  }, [user]);

  return (
    <div className="">
      <article className="mt-8 px-4">

        <div className="text-sm flex flex-wrap gap-2 justify-between items-center mb-2">
          <h3 className="font-semibold">Transactions</h3>
          <div className="filters flex gap-1">
            <div className="flex gap-1">
              <p className="text-appGrayText2">Showing:</p>
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"All Time"} className="p-0">All Time</option>
              </select>
            </div>
            <div className="flex gap-1">
              <p className="text-appGrayText2">Filter:</p>
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"None"}>None</option>
              </select>
            </div>
          </div>
        </div>

        <div className="cards grid md:grid-cols-3 gap-4  my-4">
          <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
            <h3 className="font-semibold">Total Inflow</h3>
            <p className="text-[#999999] mt-1">
              Showing:{" "}
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"USD"}>NGN</option>
              </select>
            </p>

            <h1 className="font-semibold text-xl mt-6">₦{((data?.balance / 100) || 0).toLocaleString() || 0}</h1>
          </div>
          <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
            <h3 className="font-semibold">Total Outflow</h3>
            <p className="text-[#999999] mt-1">
              Showing:{" "}
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"All"}>All</option>
              </select>
            </p>

            <h1 className="font-semibold text-xl mt-6">{data?.transactions?.length}</h1>
          </div>
          <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm" onClick={() => {
            navigator.clipboard.writeText(data?.account_num)
            .then(() => {
              setCopiedBankNumber(true);
              setTimeout(() => {
                setCopiedBankNumber(false);
              }, 3000);
            })
          }}>
            <h3 className="font-semibold">{data?.account_name}</h3>
            <p className="text-[#999999] mt-1">
              Bank:{" "}
              <span className="text-appPurple">
                {data?.bank_name}
              </span>
            </p>

            <h1 className="font-semibold text-xl mt-6">{data?.account_num}</h1>
            <span className={`text-xs ${!copiedBankNumber ? "text-appPurple" : "text-green-500"}`}>{copiedBankNumber ? "Copied" : "Click to copy"}</span>
          </div>
        </div>
      </article>
      <article className="mt-8 px-4">
        <div className="text-sm flex flex-wrap gap-2 justify-between items-center mb-2">
          <h3 className="font-semibold">Payouts</h3>
          <div className="filters flex gap-1">
            <div className="flex gap-1">
              <p className="text-appGrayText2">Showing:</p>
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"All Time"} className="p-0">All Time</option>
              </select>
            </div>
            <div className="flex gap-1">
              <p className="text-appGrayText2">Filter:</p>
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"None"}>None</option>
              </select>
            </div>
          </div>
        </div>

        <div className="cards grid md:grid-cols-2 gap-4 my-4">
          <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
            <h3 className="font-semibold">Total Balance</h3>
            <p className="text-[#999999] mt-1">
              Showing:{" "}
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"NGN"}>NGN</option>
              </select>
            </p>

            <h1 className="font-semibold text-xl mt-6">₦0</h1>

            <BarChart
              className="mt-4 h-72"
              data={chartdata}
              index="date"
              categories={['Running']}
              colors={["neutral"]}
              color="red"
              style={{ height: "130px" }}
              yAxisWidth={0}
            />
          </div>
          <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm grow">
            <h3 className="font-semibold">Pending Payout</h3>
            <p className="text-2xl font-semibold my-14">₦0</p>

            <p className="font-semibold text-sm mt-6 mb-3">Pending</p>
            <div className="flex flex-wrap gap-2 items-center justify-between bg-[#fdfdfd] border py-2 px-2 rounded-md">
              <div className="flex gap-1 items-center">
                <div className="w-8 h-8 rounded-full bg-red-500 text-zinc-200 flex justify-center items-center">0</div>
                {/* <p className="text-[10px] md:text-xs leading-3">You have some pending payout we are resolving soon</p> */}
                <p className="text-[10px] md:text-xs leading-3">You have no pending payout to be resolved</p>
              </div>
              {/* <button className="bg-black text-white rounded-md text-sm px-4 py-1.5 md:grow-0 grow">Resolve</button> */}
            </div>
          </div>
        </div>
      </article>
    </div>
  );
}

export default Page;