import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import AutoScroll from "embla-carousel-auto-scroll";
import AutoPlay from "embla-carousel-autoplay";
import Image from "next/image";

export default function ImagesCarousel() {
  return (
    <Carousel plugins={[AutoPlay({ delay: 3000 })]}>
      <CarouselContent className="md:-ml-4">
        {Array.from({ length: 8 }, (_, i) => (
          <CarouselItem key={i} className="md:pl-4 max-h-[400px]">
            <Image
              src={`/images/img-${i + 1}.jpg`}
              alt={`img-${i + 1}`}
              width={325}
              height={400}
              className="rounded-lg object-cover"
              priority={i === 0} // Load first image immediately
            />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  );
}
