import { Menu, Transition } from "@headlessui/react";
import { ChevronDownIcon } from "../icons";
import { Fragment } from "react";
import DropdownItem from "./DropdownItem";

type DropdownItemsType = {
  label: string;
  items?: {
    name: string;
    current: boolean;
  }[];
  children?: React.ReactNode;
  className?: string;
  size?: string;
};

export default function Dropdown({
  label,
  items,
  children,
  className,
  size,
}: DropdownItemsType) {
  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <Menu.Button className="group inline-flex items-center gap-x-2 text-sm font-medium text-primary hover:text-secondary">
          {label}
          <ChevronDownIcon
            width={12}
            height={12}
            strokeWidth={3}
            className="opacity-60"
            aria-hidden={true}
          />
        </Menu.Button>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items
          className={`absolute right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none ${size}`}
        >
          {items ? (
            <DropdownItem items={items} />
          ) : (
            <div className={className}>{children}</div>
          )}
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
