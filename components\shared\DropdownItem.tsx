import { Menu } from "@headlessui/react";

type DropdownItemsType = {
  items: {
    name: string;
    current: boolean;
  }[];
};

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function DropdownItem({ items }: DropdownItemsType) {
  return (
    <div className="py-1 cursor-pointer">
      {items.map((item) => (
        <Menu.Item key={item.name}>
          {({ active }) => (
            <div
              className={classNames(
                item.current ? "font-medium text-gray-900" : "text-gray-500",
                active ? "bg-gray-100" : "",
                "block px-4 py-2 text-sm"
              )}
            >
              {item.name}
            </div>
          )}
        </Menu.Item>
      ))}
    </div>
  );
}
