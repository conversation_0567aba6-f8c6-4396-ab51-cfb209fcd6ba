import axios, { InternalAxiosRequestConfig } from "axios";

import CUR<PERSON><PERSON>IES from "../currency.json";
import { toast } from "react-toastify";

export function isAllowed(
  userRole: string | null,
  allowedRoles: string[]
): Boolean {
  return !!userRole && allowedRoles.includes(userRole);
}

export const currencySymbol = (selectedFiat: string) => {
  const currency = CURRENCIES.find((curr) => curr.code === selectedFiat);
  return currency ? currency.symbol : "";
};

export const CONSTANTS = {
  SERVER_URL:
    process.env.NODE_ENV === "production"
      ? process.env.NEXT_PUBLIC_FLINCAP_SERVER_URL
      : process.env.NODE_ENV === "test"
      ? process.env.NEXT_PUBLIC_FLINCAP_TEST_SERVER_URL
      : (process.env.NEXT_PUBLIC_FLINCAP_SERVER_URL as string),
};

export function validEmail(email: string): boolean {
  return /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/.test(
    email
  );
}

export function validPassword(pass: string): boolean {
  return pass.length >= 6;
}

export const getToken = () => {
  if (typeof window !== "undefined") {
    // Client-side-only code
    return localStorage.getItem("tid") as string;
  } else {
    return "";
  }
};

// Same with `api` but gives the expected error handling behaviour
export const apiClient = axios.create({
  baseURL: CONSTANTS.SERVER_URL,
  headers: { "Content-Type": "application/json" },
});

apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig<any>) => {
    if (typeof window !== "undefined") {
      config.headers.Authorization = "Bearer " + getToken();
    }
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

export const api = axios.create({
  baseURL: CONSTANTS.SERVER_URL,
  headers: { "Content-Type": "application/json" },
});

api.interceptors.request.use(
  (config: InternalAxiosRequestConfig<any>) => {
    if (typeof window !== "undefined") {
      config.headers.Authorization = "Bearer " + getToken();
    }
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(null, (error: any) => {
  // console.log(error);
  if (error.response?.status === 403) {
    // Client-side-only code
    if (typeof window !== "undefined") {
      // localStorage.removeItem("tid");
      // location.href = "/auth/login?redirect_uri=" + window.location.origin + window.location.pathname;
      // toast.info("Seems you're not logged in");
    }
  }
  return error.response;
});

export const adminApi = axios.create({
  baseURL: CONSTANTS.SERVER_URL,
  headers: { "Content-Type": "application/json" },
});

adminApi.interceptors.request.use(
  (config: InternalAxiosRequestConfig<any>) => {
    if (typeof window !== "undefined") {
      config.headers.Authorization = "Bearer " + getToken();
    }
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

// adminApi.interceptors.response.use(null, (error: any) => {
//   // console.log(error);
//   if (error.response.status === 403) {
//     // Client-side-only code
//     if (typeof window !== "undefined") {
//       location.href = "/auth/login";
//     }
//   }
// });

export const developerApi = axios.create({
  baseURL: CONSTANTS.SERVER_URL,
  headers: { "Content-Type": "application/json" },
});

const tokenNameMapping: Record<string, { name: string; symbol: string }> = {
  BTC: { name: "Bitcoin", symbol: "BTC" },
  USDT_TRON: { name: "Tether", symbol: "USDT TRC20" },
  USDT_ARB: { name: "Tether", symbol: "USDT ARB" },
  USDT_BSC: { name: "Tether", symbol: "USDT BEP20" },
  USDC_ARB: { name: "USD Coin", symbol: "USDC ARB" },
  USDT_SOL: { name: "Tether", symbol: "USDT SOL" },
  USDC_BSC: { name: "USD Coin", symbol: "USDC BEP20" },
  USDC_XLM: { name: "USD Coin", symbol: "USDC XLM" },
  USDC_TRON: { name: "USD Coin", symbol: "USDC TRC20" },
  USDC_SOL: { name: "USD Coin", symbol: "USDC SOL" },
  BUSD_BSC: { name: "Binance USD", symbol: "BUSD BEP20" },
  ARB: { name: "ARBITRUM", symbol: "ARB" },
  ETH: { name: "ETHEREUM", symbol: "ETH" },
  BSC: { name: "Binance Coin", symbol: "BNB BEP20" },
  TRON: { name: "TRON", symbol: "TRX" },
  SOL: { name: "SOLANA", symbol: "SOL" },
};

export function getTokenName(symbol: string): { symbol: string; name: string } {
  if (Reflect.has(tokenNameMapping, symbol)) {
    return tokenNameMapping[symbol];
  } else if (symbol === "NGN") {
    return {
      symbol: symbol,
      name: "NGN",
    };
  } else {
    return {
      symbol: symbol,
      name: symbol,
    };
  }
}
