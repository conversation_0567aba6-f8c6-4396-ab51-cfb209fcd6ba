import Link from "next/link";
import { useEffect, useState, useRef } from "react";
import {
  ChevronRightIcon,
  CopyAccNumberIcon,
  CopyIcon,
  EyeIcon,
  EyeSlashIcon,
  NairaIcon,
  SwapCryptoIcon,
} from "./icons";
import { toast } from "react-toastify";
import WithdrawCryptoModal from "./modals/WithdrawCryptoModal";
import WithdrawFiatModal from "./modals/WithdrawFiatModal";
import useStore from "@/store";
import SwapModal from "./modals/SwapModal";

type BankData = {
  account_name: string;
  account_number: string;
  bank_code: string;
  bank_name: string;
  balance: number;
};

export default function DataItem({
  title,
  userId,
  amount,
  symbol,
  bankData,
  wallets,
  fiat,
}: {
  title: string;
  userId: string;
  symbol: string;
  amount: number;
  bankData?: BankData;
  wallets: any;
  fiat: boolean;
}) {
  const [showCryptoBalance, setShowCryptoBalance] = useState(false);
  const [showFiatBalance, setShowFiatBalance] = useState(false);
  const { setModal, user } = useStore();

  function handleShowCryptoBalance() {
    setShowCryptoBalance((prev) => {
      if (prev) {
        localStorage.removeItem("showCryptoBalance");
      } else {
        localStorage.setItem("showCryptoBalance", "true");
      }
      return !prev;
    });
  }

  function handleShowFiatBalance() {
    setShowFiatBalance((prev) => {
      if (prev) {
        localStorage.removeItem("showFiatBalance");
      } else {
        localStorage.setItem("showFiatBalance", "true");
      }
      return !prev;
    });
  }

  return (
    <div className="lg:p-6 p-4 rounded-xl bg-white border border-zinc-200 min-h-32 font-circularStd">
      <h3 className="font-medium text-sm text-[#475467]">{title}</h3>
      <div className="flex items-center justify-between"></div>
      {fiat ? (
        bankData ? (
          <div className="font-semibold sm:text-2xl text-xl my-3 text-zinc-800">
            <span className="mr-2">
              {symbol}{" "}
              {showFiatBalance ? (amount | 0)?.toLocaleString() : "****"}
            </span>
            <button onClick={handleShowFiatBalance}>
              {showFiatBalance ? (
                <EyeIcon className="w-4" />
              ) : (
                <EyeSlashIcon className="w-4" />
              )}
            </button>
          </div>
        ) : (
          ""
        )
      ) : (
        <div className="font-semibold sm:text-2xl text-xl my-3 text-zinc-800">
          <span className="mr-2">
            {symbol}{" "}
            {showCryptoBalance
              ? symbol === "BTC"
                ? amount?.toLocaleString(undefined, {
                    minimumFractionDigits: 8,
                    maximumFractionDigits: 8,
                  })
                : Math.floor(amount * 100) / 100
              : "****"}
          </span>
          <button onClick={handleShowCryptoBalance}>
            {showCryptoBalance ? (
              <EyeIcon className="w-4" />
            ) : (
              <EyeSlashIcon className="w-4" />
            )}
          </button>
        </div>
      )}

      {!fiat &&
        (user?.role === "Admin" ||
          user?.role === "Owner" ||
          user?.organisation == null) && (
          <div className="flex gap-2">
            <button
              onClick={() =>
                setModal(
                  <WithdrawCryptoModal
                    wallets={wallets}
                    baseCurrency={wallets[0].symbol?.split(/[_\s]/)[0]}
                  />
                )
              }
              className="border border-[#7928FF] rounded flex gap-2 items-center px-4 py-2.5"
            >
              <p className="text-[#7928FF] text-sm">Withdraw</p>
            </button>
            <SwapModal amount={amount} />
          </div>
        )}

      {fiat &&
      (user?.role === "Admin" ||
        user?.role === "Owner" ||
        user?.organisation == null) ? (
        bankData ? (
          <div className="flex lg:flex-row flex-col justify-between lg:items-center">
            <button
              onClick={() =>
                setModal(<WithdrawFiatModal wallet={wallets[0]} />)
              }
              className="border border-[#7928FF] rounded flex self-start gap-2 items-center px-4 py-2.5"
            >
              <p className="text-[#7928FF] text-sm">Withdraw</p>
            </button>
          </div>
        ) : (
          <p>
            <Link href="/dashboard/ql/settings" className="text-blue-500">
              Complete profile
            </Link>
          </p>
        )
      ) : (
        ""
      )}
    </div>
  );
}
