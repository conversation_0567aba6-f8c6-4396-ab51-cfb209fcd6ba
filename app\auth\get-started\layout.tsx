"use client";

import { useEffect } from "react";
import axios from "axios";
import { CONSTANTS, getToken } from "../../utils";
import Link from "next/link";
import Image from "next/image";
import arrow from "@/assets/images/arrow.svg";
import Testimonials from "@/components/Testimonials";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  useEffect(() => {
    async function getAccount() {
      const api = axios.create({
        baseURL: CONSTANTS.SERVER_URL,
        headers: { Authorization: `Bearer ${getToken()}` },
      });

      try {
        const res = await api.get("/v1/auth");
        if (res.data.status === 403) return;
        else window.location.href = "/dashboard";
      } catch (err: any) {
        console.log(err);
      }
    }

    getAccount();
  }, []);

  return (
    <div className="bg-white min-h-screen">
      <div className="border-b w-full">
        <Link href={"/"} className="logo py-4 px-4 flex">
          <Image
            src={"/images/flincap-logo.svg"}
            alt="."
            width={100}
            height={50}
          />
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-10">
        <div className="bg-[#4F0EB8] col-span-4 min-h-[calc(100vh-65px)] hidden md:block">
          <div className="h-full bg-[url(/images/flincapauthlogo.svg)] bg-no-repeat flex justify-center items-center">
            <Testimonials />
          </div>
        </div>
        <main className="col-span-6 flex flex-col-reverse md:flex-col py-2 md:px-4">
          <p className="self-center md:self-end flex items-center md:pt-6 px-4">
            Already have an account?{" "}
            <Link href="/auth/login" className="text-appPurple ml-2 flex gap-1">
              Sign in
              <Image src={arrow} alt="" />
            </Link>
          </p>
          <div>{children}</div>
        </main>
      </div>
    </div>
  );
}
