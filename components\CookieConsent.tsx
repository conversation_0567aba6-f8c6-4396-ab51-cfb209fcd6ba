import Link from "next/link";
import React, { useEffect, useState } from "react";

function CookieConsent() {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (typeof window != "undefined") {
      if (!localStorage.getItem("acceptedConscent")) {
        setShow(true);
      }
    }
  }, []);

  return (
    <div
      className={`${
        show ? "" : "hidden"
      } max-w-[269px] z-50 rounded-lg bg-white border border-zinc-300 roudned-2xl p-3 shadow-sm fixed bottom-3 right-2`}
    >
      <p className="text-sm">
        We use cookies to improve your experience. You can opt out of certain
        cookies. Find out more in our privacy policy.
      </p>

      <div className="grid grid-cols-2 gap-2 mt-4">
        <button
          onClick={() => {
            localStorage.setItem("acceptedConscent", "true");
            setShow(false);
          }}
          className="text-sm bg-black text-white border rounded-lg px-4 py-2"
        >
          Accept
        </button>
        <Link
          href="#"
          target="_blank"
          className="text-sm bg-white text-black border rounded-lg px-4 py-2"
        >
          Learn more
        </Link>
      </div>
    </div>
  );
}

export default CookieConsent;
