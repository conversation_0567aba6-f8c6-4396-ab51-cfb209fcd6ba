import React, { useEffect, useState, useRef } from "react";
import { ChevronDownIcon } from "../icons";

type DropdownType = {
  label: string;
  theme?: string;
  children?: React.ReactNode;
};

export default function DropdownMenu({ label, theme, children }: DropdownType) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdown = useRef<null | HTMLDivElement>(null);

  function handleClickOutside(event: MouseEvent) {
    if (dropdown.current && !dropdown.current.contains(event.target as Node)) {
      setIsOpen(false);
    }
  }

  useEffect(() => {
    document.documentElement.addEventListener("keydown", (e) => {
      if (e.key === "Escape") {
        setIsOpen(false);
      }
    });
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdown}>
      <button
        onClick={() => setIsOpen((prev) => !prev)}
        className={`flex items-center gap-x-2 ${
          theme === "dark" ? "text-zinc-700" : "text-primary"
        }`}
      >
        <span>{label}</span>
        <ChevronDownIcon
          width={12}
          height={12}
          strokeWidth={3}
          className="opacity-60"
        />
      </button>

      {isOpen && (
        <div className="absolute top-7 right-0 min-w-52 min-h-full shadow-card bg-white border border-zinc-100 rounded-md p-4 duration-300">
          {children}
        </div>
      )}
    </div>
  );
}
