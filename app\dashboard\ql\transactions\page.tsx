"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import creditIcon from "@/assets/images/creditIcon.svg";
import { UpDownIcon } from "@/components/icons";
import { api, getTokenName } from "@/app/utils";
import NotransactionIcon from "@/assets/images/no-data.svg";
import moment from "moment";
import useStore from "@/store";
import TransactionDetailsModal from "@/components/modals/TransactionDetails";

function Page() {
  const [transactions, setTransactions] = useState<Record<string, any>[]>([]);
  const { setModal } = useStore();

  console.log(transactions);

  useEffect(() => {
    if (typeof window != "undefined") {
      api
        .get("/v1/wallets/transactions?otherType=" + "PAYOUT")
        // api.get("/v1/wallets/transactions")
        .then((res) => {
          setTransactions(res.data.data);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, []);

  return (
    <div className="px-4">
      <article className="mt-8">
        {/* <div className="text-sm flex flex-wrap gap-2 justify-between items-center mb-2">
          <h3 className="font-semibold">History</h3>
          <div className="filters flex gap-1">
            <div className="flex gap-1">
              <p className="text-appGrayText2">Showing:</p>
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"All Time"}>All Time</option>
              </select>
            </div>
            <div className="flex gap-1">
              <p className="text-appGrayText2">Filter:</p>
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"None"}>None</option>
              </select>
            </div>
          </div>
        </div> */}

        {!transactions.length ? (
          <div className="flex flex-wrap justify-between py-4 h-auto">
            <div className="text-center flex flex-col justify-center items-center w-full min-h-80">
              <Image
                src={NotransactionIcon}
                alt="."
                className="block mx-auto grayscale"
              />
              <p className="my-2.5 text-black">No transactions</p>
              <p className="font-light">
                Once you start trading transactions, they <br /> will appear
                here. <span className="text-appPurple">Start now</span>
              </p>
            </div>
          </div>
        ) : (
          <div className="">
            <table className="w-full table-auto">
              <thead className="text-[14px] text-ash rounded-lg bg-ash1 border-b border-b-ash2">
                <tr className="text-left">
                  <th className="p-1 md:p-3 font-normal">Coin</th>
                  <th className="p-1 md:p-3 font-normal md:block hidden">
                    Amount
                  </th>
                  <th>Rate</th>
                  <th>Total</th>
                  <th className="p-1 md:p-3 font-normal">Receiver</th>
                  <th className="p-1 md:p-3 font-normal items-center gap-2 md:flex hidden">
                    Date <UpDownIcon className="w-3 h-3" />
                  </th>
                </tr>
              </thead>
              <tbody>
                {transactions?.length
                  ? transactions.map((transaction) => {
                      return (
                        <tr
                          className="text-xs text-black hover:text-appPurpleDark border-b border-b-ash2 cursor-pointer hover:translate-x-2 transition-transform duration-300 ease-in-out hover:bg-appWhite"
                          key={transaction.id}
                          onClick={() =>
                            setModal(
                              <TransactionDetailsModal
                                transaction={transaction}
                              />
                            )
                          }
                        >
                          <td className="p-1 md:p-3 font-semibold md:whitespace-nowrap">
                            {transaction.assetType !== "FIAT"
                              ? getTokenName(transaction.cryptoWallet?.symbol)
                                  .symbol
                              : transaction.fiatWallet?.symbol ?? "NGN"}
                          </td>

                          {/* <td className='md:flex hidden'>{transaction.cryptoWalletId ? transaction.cryptoWallet?.symbol == "BTC" ? transaction.cryptoAmount : transaction.cryptoAmount.toFixed(0) : ((Number(transaction.fiatAmount || 0))).toLocaleString()}</td> */}
                          <td>
                            {transaction.cryptoWalletId
                              ? transaction.cryptoWallet?.symbol == "BTC"
                                ? transaction.cryptoAmount
                                : transaction.cryptoAmount.toLocaleString()
                              : transaction.transactionType === "DEPOSIT" &&
                                transaction.transactionHash
                              ? Number(
                                  transaction.fiatAmount / 100 || 0
                                ).toLocaleString()
                              : Number(
                                  transaction.fiatAmount || 0
                                ).toLocaleString()}
                          </td>
                          <td>
                            {transaction.cryptoWallet?.symbol == "BTC"
                              ? Number(
                                  transaction.conversionRate
                                ).toLocaleString()
                              : Number(transaction.conversionRate).toFixed(0)}
                          </td>

                          <td>
                            {transaction.transactionType === "PAYOUT"
                              ? transaction.fiatAmount !== 0
                                ? Number(
                                    transaction.fiatAmount
                                  ).toLocaleString()
                                : (
                                    Number(transaction.conversionRate) *
                                    transaction.cryptoAmount
                                  ).toLocaleString()
                              : (
                                  Number(transaction.conversionRate) *
                                  transaction.cryptoAmount
                                ).toLocaleString()}
                          </td>

                          <td className="p-1 md:p-3 md:whitespace-nowrap">
                            {transaction.transactionType === "PAYOUT" ? (
                              <>
                                <p>{transaction.toAccName}</p>
                                <p className="text-xs font-light mt-1">
                                  {transaction.toAccNum}{" "}
                                  {transaction.toBankName}
                                </p>
                              </>
                            ) : transaction.cryptoWalletId ? (
                              <>{transaction.wallet_address}</>
                            ) : (
                              <>
                                {transaction.toAccName} <br />
                                <span className="text-xs font-light mt-1">
                                  {transaction.toAccNum}{" "}
                                  {transaction.toBankName}
                                </span>
                              </>
                            )}
                          </td>
                          <td className="p-1 md:p-3 font-semibold md:whitespace-nowrap md:flex hidden">
                            {new Date(transaction.createdAt).toLocaleString(
                              undefined,
                              {
                                day: "2-digit",
                                month: "2-digit",
                                year: "numeric",
                                hour: "2-digit",
                                minute: "2-digit",
                                second: "2-digit",
                              }
                            )}
                          </td>
                        </tr>
                      );
                    })
                  : null}
              </tbody>
            </table>
          </div>
        )}
      </article>
    </div>
  );
}

export default Page;
