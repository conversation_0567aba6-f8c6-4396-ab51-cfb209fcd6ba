import useStore from '@/store'
import React, { FormEvent, useState } from 'react'
import { XMarkIcon } from '../icons';
import { adminApi } from '@/app/utils';
import { useRouter } from 'next/navigation';

function DeleteUserModal() {
  const { setModal } = useStore(store => store);
  const navigator = useRouter();
  const [formDetails, setFormDetails] = useState<Record<string, any>>({
    approval: ""
  })

  const deleteUser = async (e: FormEvent) => {
    e.preventDefault();
    const path = location.pathname;
    const clientID = path.split("/")[3];
    adminApi.delete("/v1/admin/users/" + clientID)
    .then(res => {
      window.location.href = "/admin/clients";
    })
    .catch(err => {
      console.log(err);
    });
  }

  const handleChange = (e: any) => {
    setFormDetails({ ...formDetails, [e.target.name]: e.target.value });
  };

  return (
    <div className='bg-white rounded-md h-fit max-w-80 md:max-w-96 p-4 grow'>
      <div className="flex justify-between items-center">
        <h3 className="font-semibold">Delete User</h3>
        <button onClick={() => setModal(null)}><XMarkIcon className="w-6 h-6" /></button>
      </div>

      <p className='text-sm mt-2'>Are you sure you want to delete user? Type <code className='italic'>delete</code> in the box below</p>

      <form onSubmit={deleteUser} className="space-y-3 mt-3">
        <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
          <input
            name="approval"
            value={formDetails.approval}
            onChange={handleChange}
            className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
          />
        </div>
        <button
          type="submit"
          disabled={formDetails.approval !== "delete"}
          className={`bg-appPurple text-white py-2 rounded-md font-semibold mt-5 w-full ${formDetails.approval !== "delete" ? "opacity-40" : "opacity-100"}`}
        >
          Delete
        </button>
      </form>
    </div>
  )
}

export default DeleteUserModal