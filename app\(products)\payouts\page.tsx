"use client";
import Nav from "@/components/Nav";
import CookieConsent from "@/components/CookieConsent";
import Image from "next/image";
import Link from "next/link";
import Footer from "@/components/Footer";

export default function Page() {
  return (
    <>
      <Nav />
      <CookieConsent />
      <section className="h-screen md:h-[620px] bg-[url(/images/hero-bg.svg)] bg-[#F2EBFF] bg-cover bg-top bg-no-repeat">
        <div className="h-full flex max-w-7xl mx-auto justify-start gap-20 px-4 items-center py-28 md:py-10 md:justify-between flex-col md:flex-row">
          <div className="md:block md:text-left text-center flex justify-center items-center flex-col">
            <h1 className="text-3xl md:text-6xl font-bold text-black">
              Cross-border Payment
            </h1>
            <p className="my-4 md:my-7 max-w-md text-md md:text-lg">
              Flincap processes cross-border payments across Africa faster and
              easier using stablecoins. Leverage our infrastructure for
              programmable, rapid payouts across several countries and
              currencies to cater to your global needs.
            </p>
            <Link
              href="../auth/get-started"
              className="bg-appPurple text-white px-20 py-2.5 rounded-md block w-fit"
            >
              Start Here
            </Link>
          </div>
          <div className="image md:block">
            <Image
              width={200}
              height={200}
              alt="banner illustration"
              src={"/images/paper-plane.svg"}
              className="bg-blend-darken md:w-[350px] md:h-[350px]"
            />
          </div>
        </div>
      </section>
      <section className="h-auto">
        <div className="max-w-7xl py-10 gap-10 mx-auto flex flex-col px-10 items-center md:gap-16 md:py-20">
          <h2 className="text-left md:text-center text-2xl font-bold text-[#0A2540] md:text-3xl md:w-3/6">
            Send money across Africa without hassle!
          </h2>
          <div className="flex flex-col gap-10 max-w-4xl md:flex-row">
            <div className="shadow-xl rounded-xl w-[350px] px-10 pb-20 pt-10 md:w-[400px]">
              <h3 className="font-bold text-lg text-[#0A2540] py-3 font-circularStd">
                Send and Receive Money Across Africa with Ease
              </h3>
              <p className="text-[#425466] font-circularStd pt-4">
                Flincap has made it easy for you to send and receive money
                wherever you may be in Africa. Our cross-border stablecoins
                payments allow you to send and receive payments without worrying
                about conversions and rates.
              </p>
            </div>
            <div className="shadow-xl rounded-xl w-[350px] px-10 pt-10 pb-20 md:w-[400px]">
              <h3 className="font-bold text-lg py-3 text-[#0A2540] font-circularStd">
                Experience a True Borderless Payment System
              </h3>
              <p className="text-[#425466] font-circularStd pt-4">
                Flincap is using stablecoins to fix cross-border payments across
                Africa! You don’t have to search for matching agents when
                sending money across countries and currencies. We have created
                all the connections you need. With our borderless system, your
                fintech or B2B can carry out all transactions in one place.
              </p>
            </div>
          </div>
        </div>
      </section>
      <section className="h-auto bg-[#E8F4FF]">
        <div className="max-w-7xl py-10 mx-auto flex flex-col px-10 items-center gap-14 md:gap-14 md:py-16">
          <h2 className="text-center text-3xl font-bold text-[#0A2540]">
            Why Use Our Cross-Border Payment System?
          </h2>
          <div className="grid grid-cols-1 md:grid-rows-2 gap-10 max-w-4xl md:gap-y-10 md:grid-cols-2">
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-one.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Simplified Transaction Processes
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  Sending or receiving money across borders can be complicated
                  with several intermediaries. However, with Flincap, you never
                  have to worry about understanding the whole science behind the
                  process. We have designed a system to help you send or receive
                  money without any hassle!
                </p>
              </div>
            </div>
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-two.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Lightning-Fast Transactions
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  You never have to wait days and hours before accessing the
                  funds sent to you again. At Flincap, our high-speed systems
                  have enabled lightning-fast transactions for users across
                  Africa. Experience a world of quick and efficient payments
                  with Flincap!
                </p>
              </div>
            </div>
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-three.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Fair Transfer Fees
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  Say no to exorbitant transfer fees because you send money
                  outside your country. With Flincap, you can enjoy reasonable
                  transfer fees when sending or receiving money from anywhere in
                  Africa.
                </p>
              </div>
            </div>
            <div className="flex align-top gap-5 md:px-7">
              <div className="w-[40px] h-[40px] bg-[#71BBFF] grid place-items-center rounded-md flex-shrink-0">
                <Image
                  src="/images/paymentapis/icon-four.svg"
                  width={22}
                  height={22}
                  alt=""
                />
              </div>
              <div>
                <h3 className="font-bold text-lg text-[#0A2540] font-circularStd">
                  Secure Payment System
                </h3>
                <p className="text-[#425466] font-circularStd pt-2">
                  Your money never has to hang when you send money across
                  African borders. Our cross-border payment system has
                  bank-grade security to protect you from potential attacks. Do
                  high-volume transactions across Africa without fear!
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <aside className="bg-black">
        <div className="max-w-7xl mx-auto md:px-36 py-24 flex flex-col md:flex-row justify-between items-center">
          <div className="text md:block flex flex-col justify-center items-center mb-5">
            <h2 className="text-3xl leading-8 md:text-5xl font-semibold mb-14 text-white">
              <strong className="text-[#7928FF]">Ready to build your</strong>{" "}
              <br />
              <strong>Crypto business?</strong>
            </h2>
            <Link
              href="#"
              className="bg-appPurple text-white px-7 md:px-12 py-2.5 md:py-3 rounded-md block w-fit mt-2"
            >
              Get started
            </Link>
          </div>
          <div className="w-[200px] h-[200px] mt-10 md:mt-0">
            <Image
              src="/images/illustrations/Group.svg"
              alt="Group"
              width={200}
              height={200}
              className="w-full h-full"
            />
          </div>
        </div>
      </aside>
      <Footer />
    </>
  );
}
