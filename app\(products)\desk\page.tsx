"use client";

import Nav from "@/components/Nav";
import Footer from "@/components/Footer";
import CookieConsent from "@/components/CookieConsent";
import Link from "next/link";
import Image from "next/image";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export default function Page() {
  return (
    <>
      <Nav />
      <CookieConsent />
      <section className="h-screen md:h-[620px] bg-[url(/images/hero-bg.svg)] bg-[#E8F9F0] bg-cover bg-top bg-no-repeat">
        <div className="h-full flex max-w-7xl mx-auto justify-start gap-20 px-4 items-center py-28 md:py-10 md:justify-between flex-col md:flex-row">
          <div className="md:block md:text-left text-center flex justify-center items-center flex-col">
            <h1 className="text-3xl md:text-6xl font-bold text-black">
              Flincap Trading Desk
            </h1>
            <p className="my-4 md:my-7 max-w-md text-md md:text-lg">
              Access liquidity in stablecoin and fiat currencies across Africa.
              Get connected to several crypto OTC desks across Africa using our
              P2P matching mechanism.
            </p>
            <Link
              href="https://forms.gle/fgMrEBneqPJE2zL39"
              className="bg-appPurple text-white px-20 py-2.5 rounded-md block w-fit"
            >
              Apply
            </Link>
          </div>
          <div className="image md:block">
            <Image
              width={250}
              height={250}
              alt="banner illustration"
              src={"/images/otc/hero-img.svg"}
              className="bg-blend-darken md:w-[400px] md:h-[400px]"
            />
          </div>
        </div>
      </section>
      <section className="bg-[#F4F7FF]">
        <div className="max-w-7xl py-10 gap-10 mx-auto flex flex-col px-10 items-center md:gap-20 md:py-20">
          <h2 className="text-center text-3xl font-extrabold text-[#0A2540]">
            Why trade on Flincap?
          </h2>
          <div className="w-full flex flex-col gap-10 md:gap-40 md:flex-row justify-center">
            <div className="flex flex-col justify-center items-center">
              <Image
                width={100}
                height={100}
                alt="banner illustration"
                src={"/images/otc/speed-icon.svg"}
                className="bg-blend-darken md:w-[200px] md:h-[200px]"
              />
              <h3 className="font-extrabold text-lg py-6">Speed</h3>
              <p className="text-center">
                Execute trades seamlessly in real-time.
              </p>
            </div>
            <div className="flex flex-col justify-center items-center">
              <Image
                width={100}
                height={100}
                alt="banner illustration"
                src={"/images/otc/liquidity-icon.svg"}
                className="bg-blend-darken md:w-[200px] md:h-[200px]"
              />
              <h3 className="font-extrabold text-lg py-6">Liquidity</h3>
              <p className="text-center">
                Access liquidity in multiple currencies and digital assets per
                time.
              </p>
            </div>
            <div className="flex flex-col justify-center items-center">
              <Image
                width={100}
                height={100}
                alt="banner illustration"
                src={"/images/otc/support-icon.svg"}
                className="bg-blend-darken md:w-[200px] md:h-[200px]"
              />
              <h3 className="font-extrabold text-lg py-6">24/7 Support</h3>
              <p className="text-center">
                Access support round-the-clock as your trade.
              </p>
            </div>
          </div>
        </div>
      </section>
      <section className="h-screen bg-[url(/images/otc/aside-icon.svg)] bg-bottom bg-no-repeat">
        <div className="max-w-7xl mx-auto flex flex-col px-7 items-center md:items-start md:flex-row md:justify-between md:pt-24">
          <div className="pt-16 md:pt-20">
            <h2 className="text-3xl font-extrabold text-[#0A2540] mb-6">
              How to start trading
            </h2>
            <div className="flex items-center py-4 gap-4 md:gap-6">
              <div className="bg-[#71BBFF] text-white flex justify-center items-center w-[40px] h-[30px] md:w-[50px] md:h-[40px] rounded-md">
                1
              </div>
              <p className="text-lg md:text-xl">
                <Link
                  href="https://forms.gle/fgMrEBneqPJE2zL39"
                  className="underline decoration-[#0A2540]"
                >
                  Signup
                </Link>{" "}
                by filling this{" "}
                <Link
                  href="https://forms.gle/fgMrEBneqPJE2zL39"
                  className="underline decoration-[#0A2540]"
                >
                  form
                </Link>
              </p>
            </div>
            <div className="flex items-center py-4 gap-4 md:gap-6">
              <div className="bg-[#71BBFF] text-white flex justify-center items-center w-[40px] h-[30px] md:w-[50px] md:h-[40px] rounded-md">
                2
              </div>
              <p className="text-lg md:text-xl">
                Submit required verification documents
              </p>
            </div>
            <div className="flex items-center py-4 gap-4 md:gap-6">
              <div className="bg-[#71BBFF] text-white flex justify-center items-center w-[40px] h-[30px] md:w-[50px] md:h-[40px] rounded-md">
                3
              </div>
              <p className="text-lg md:text-xl">
                Get onboarded into our pool of OTC traders
              </p>
            </div>
          </div>
          <div>
            <Image
              width={250}
              height={250}
              alt="banner illustration"
              src={"/images/otc/start-trading-icon.svg"}
              className="bg-blend-darken hidden md:block md:w-[400px] md:h-[400px]"
            />
          </div>
        </div>
      </section>
      <section className="bg-[#F2EBFF] py-20 md:py-40">
        <div className="max-w-7xl gap-20 md:gap-44 flex h-1/2 flex-col items-center mx-auto md:flex-row">
          <h2 className="text-center text-4xl md:text-6xl font-bold text-[#0A2540]">
            Frequently <br /> Asked Questions
          </h2>
          <div className="px-3 md:w-[700px]">
            <Accordion type="single" collapsible>
              <AccordionItem value="item-1">
                <AccordionTrigger className="text-sm md:text-lg">
                  <span className="w-[25px] h-[25px] flex items-center justify-center rounded-full text-[#7928FF] bg-[#DBC5FF] group-hover:bg-white">
                    1
                  </span>
                  What is Flincap Trading Desk?
                </AccordionTrigger>
                <AccordionContent className="text-sm pt-2 md:text-lg w-5/6 mx-auto">
                  Flincap Trading Desk is a platform for OTC crypto businesses
                  that trade across different African currencies. It connects
                  OTC traders with each other using a P2P matching mechanism.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2">
                <AccordionTrigger className="rounded-lg text-left text-sm md:text-lg">
                  <span className="w-[25px] h-[25px] flex items-center justify-center rounded-full text-[#7928FF] bg-[#DBC5FF] group-hover:bg-white">
                    2
                  </span>
                  What trading pairs/assets are currently being supported?
                </AccordionTrigger>
                <AccordionContent className="text-sm pt-2 md:text-lg w-5/6 mx-auto">
                  We support trading USDT, USDC, BTC against NGN, XAF, GHS, &
                  KES.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3">
                <AccordionTrigger className="rounded-lg text-left text-sm md:text-lg">
                  <span className="w-[25px] h-[25px] flex items-center justify-center rounded-full text-[#7928FF] bg-[#DBC5FF] group-hover:bg-white">
                    3
                  </span>
                  What are the requirements for trading with Flincap?
                </AccordionTrigger>
                <AccordionContent className="text-sm pt-2 md:text-lg w-5/6 mx-auto">
                  The trading desk is open to African businesses that have
                  signed up and completed their KYB process on Flincap.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>
      <Footer />
    </>
  );
}
