"use client";
import useStore from "@/store";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { ReactNode, use } from "react";
import { isAllowed } from "@/app/utils";

function Layout({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const { user } = useStore();

  console.log(user);

  return (
    <div className="font-circularStd">
      <nav className="bg-[#fcfcfc]">
        <ul className="flex overflow-auto">
          <li>
            <Link
              href="/dashboard/ql/settings"
              className={`md:px-6 px-2 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${
                pathname === "/dashboard/ql/settings"
                  ? "border-b-[#71bbff] text-[#71bbff]"
                  : ""
              }`}
            >
              Profile
            </Link>
          </li>

          {(isAllowed(user?.role, ["Owner", "Admin", "Developer"]) ||
            user?.organisation == null) && (
            <li>
              <Link
                href="/dashboard/ql/settings/developers"
                className={`md:px-6 px-2 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${
                  pathname.startsWith("/dashboard/ql/settings/developers")
                    ? "border-b-[#71bbff] text-[#71bbff]"
                    : ""
                }`}
              >
                Developers
              </Link>
            </li>
          )}
          {isAllowed(user?.role, ["Owner"]) && (
            <li>
              <Link
                href="/dashboard/ql/settings/team-members"
                className={`md:px-6 px-2 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${
                  pathname.startsWith("/dashboard/ql/settings/team-members")
                    ? "border-b-[#71bbff] text-[#71bbff]"
                    : ""
                }`}
              >
                Team Members
              </Link>
            </li>
          )}

          <li>
            <Link
              href="/dashboard/ql/settings/security"
              className={`md:px-6 px-2 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${
                pathname.startsWith("/dashboard/ql/settings/security")
                  ? "border-b-[#71bbff] text-[#71bbff]"
                  : ""
              }`}
            >
              Security
            </Link>
          </li>
        </ul>
      </nav>

      <main>{children}</main>
    </div>
  );
}

export default Layout;
