import Image from 'next/image'
import React from 'react'
import NotransactionIcon from "@/assets/images/no-data.svg";

function AdminPage() {
  return (
    <div>
      <div className="min-w-full mt-5 overflow-x-auto px-4">
        <table className="w-full">
          <thead className="text-[14px] text-ash rounded-lg bg-ash1 border-b border-b-ash2">
            <tr className="text-left">
              <th className="p-3 font-normal">
                Name
              </th>
              <th className="p-3 font-normal">Email</th>
              <th className="p-3 font-normal">Status</th>
              <th className="p-3 font-normal flex items-center gap-2">
                Access
              </th>
              <th className="p-3 font-normal">
                Last login
              </th>
            </tr>
          </thead>
          <tbody>
            {/* <tr
              className="text-xs text-black hover:text-appPurpleDark border-b border-b-ash2 cursor-pointer hover:bg-appWhite"
            >
              <td className="p-3 flex items-center gap-2 whitespace-nowrap">
                <Image
                  src={"https://ui-avatars.com/api/?name=<PERSON>"}
                  alt="bitcoin up"
                  width={30}
                  height={30}
                  className='rounded-full'
                />
                <span className="font-semibold">John Doe</span>
              </td>
              <td className="p-3 font-semibold whitespace-nowrap">
                <EMAIL>
              </td>
              <td className="p-3 text-[#13BF62] font-semibold">
                Active
              </td>
              <td className="p-3 text-[#13BF62] font-semibold">
                All Access
              </td>
              <td className="p-3 font-semibold whitespace-nowrap">
                15th Aug, 2022, 5:00pm
              </td>
            </tr> */}
          </tbody>
        </table>
        {!null ? (
          <div className="text-center flex flex-col justify-center items-center min-h-80">
            <Image src={NotransactionIcon} alt="." className="block mx-auto grayscale" />
            <p className="my-2.5 text-black">No admins</p>
            <p className="font-light">You have no team member yet <br /><button className="text-appPurple">click here</button> to invite someone.</p>
          </div>
        ) : null}
      </div>
    </div>
  )
}

export default AdminPage