import { ArrowLeft, CheckCircle, ChevronDown, RefreshCw } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { useState, useEffect } from "react";
import { SwapCryptoIcon } from "../icons";
import { Skeleton } from "../ui/skeleton";
import LoadingSpinner from "../LoadingSpinner";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { z } from "zod";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import debounce from "lodash/debounce";
import { api } from "@/app/utils";
import { DialogClose } from "@radix-ui/react-dialog";

const swapSchema = z
  .object({
    amountFrom: z
      .union([z.string(), z.number()]) // Accept both string and number
      .transform((value) => {
        // Convert the value to a number
        const numericValue =
          typeof value === "string" ? parseFloat(value) : value;
        return isNaN(numericValue) ? null : numericValue;
      })
      .refine((value) => value !== null && value > 0, {
        message: "Amount must be greater than 0.",
      }),

    amountTo: z
      .union([z.string(), z.number()]) // Accept both string and number
      .transform((value) => {
        // Convert the value to a number
        const numericValue =
          typeof value === "string" ? parseFloat(value) : value;
        return isNaN(numericValue) ? null : numericValue;
      })
      .refine((value) => value !== null && value > 0, {
        message: "Amount must be greater than 0.",
      }),

    currencyFrom: z.enum(["USD", "NGN"]),
    currencyTo: z.enum(["USD", "NGN"]),
  })
  .refine((data) => data.currencyFrom !== data.currencyTo, {
    message: "You cannot swap the same currency",
    path: ["currencyTo"],
  })
  .refine(
    (data) => !(data.currencyFrom === "NGN" && data.currencyTo === "USD"),
    {
      message: "NGN to USD swaps are currently disabled",
      path: ["currencyFrom"],
    }
  );

type SwapFormValues = z.infer<typeof swapSchema>;

type ConversionRates = {
  sellRate: number;
  buyRate: number;
};

export default function SwapModal({ amount }: { amount: number }) {
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    trigger,
    reset,
    formState: { errors },
  } = useForm<SwapFormValues>({
    resolver: zodResolver(swapSchema),
    defaultValues: {
      amountFrom: null,
      amountTo: null,
      currencyFrom: "USD",
      currencyTo: "NGN",
    },
    mode: "onSubmit",
  });

  const [rates, setRates] = useState<ConversionRates>();
  const [isLoadingRates, setIsLoadingRates] = useState(false);
  const [isSwapping, setIsSwapping] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [stage, setStage] = useState(1);
  const [isOpen, setIsOpen] = useState(false);

  const handleClose = () => {
    reset();
    setStage(1);
    window.location.reload();
  };

  const fetchRates = async () => {
    setIsLoadingRates(true);

    try {
      const res = await api.get("/get-conversion-rates");
      setRates(res.data);
    } catch (err) {
      console.error("Error fetching rates:", err);
    } finally {
      setIsLoadingRates(false);
    }
  };

  useEffect(() => {
    fetchRates();
  }, []);

  const handleRefreshRates = () => {
    fetchRates();
  };

  const onSubmit = async (data: SwapFormValues) => {
    console.log("Submitted");
    setErrorMessage("");

    const { amountFrom, currencyFrom, currencyTo } = data;

    const requestBody = {
      amount: amountFrom,
      currency: currencyFrom,
    };

    try {
      setIsSwapping(true);
      let endpoint = "";
      if (currencyFrom === "USD" && currencyTo === "NGN") {
        endpoint = "/v1/wallets/swap-to-ngn";
      } else if (currencyFrom === "NGN" && currencyTo === "USD") {
        endpoint = "/v1/wallets/swap-to-usd";
      }

      const res = await api.post(endpoint, requestBody);

      if (!res || res.data?.status !== "SUCCESS") {
        throw new Error(res?.data?.message || "Swap failed. Please try again.");
      }
      setStage(2);
    } catch (error) {
      console.log(error);
    } finally {
      setIsSwapping(false);
    }
  };

  const formatNumber = (num: number | null) => {
    if (num === undefined || num === null || isNaN(num)) return "";
    return new Intl.NumberFormat().format(num);
  };

  const handleAmountChange = async (
    rawValue: string | null,
    isFromAmount: boolean
  ) => {
    if (!rawValue || rawValue === "") {
      setValue(isFromAmount ? "amountFrom" : "amountTo", null);
      setValue(isFromAmount ? "amountTo" : "amountFrom", null);
    }

    // Update the form state with the raw value
    setValue(isFromAmount ? "amountFrom" : "amountTo", rawValue as any, {
      shouldValidate: true,
    });

    const numericValue = Number(rawValue?.replace(/,/g, "")) || 0;
    if (isNaN(numericValue)) return;
    console.log("Numeric value:", numericValue, typeof numericValue);

    const currencyFrom = watch("currencyFrom");
    const currencyTo = watch("currencyTo");
    const sellRate = rates?.sellRate ?? 0;
    const buyRate = rates?.buyRate ?? 0;

    const conversionObject: Record<string, (v: number) => number> = {
      USD_NGN: (v: number) => (isFromAmount ? v * sellRate : v / sellRate),
      NGN_USD: (v: number) => (isFromAmount ? v / buyRate : v * buyRate),
    };
    const key = `${currencyFrom}_${currencyTo}`;
    const converted = conversionObject[key]?.(numericValue) ?? numericValue;

    if (isFromAmount) {
      setValue("amountTo", (Math.floor(converted * 100) / 100) as any, {
        shouldValidate: true,
      });
    } else {
      setValue("amountFrom", (Math.floor(converted * 100) / 100) as any, {
        shouldValidate: true,
      });
    }

    // Trigger validation
    await trigger(["amountFrom", "amountTo"]);
  };

  const handleCurrencyChange = (
    field: "currencyFrom" | "currencyTo",
    value: string
  ) => {
    setValue(field, value as "USD" | "NGN");
    const amountFrom = watch("amountFrom");
    const currencyFrom = watch("currencyFrom");
    const currencyTo = watch("currencyTo");

    if (!amountFrom) {
      setValue("amountTo", null);
      return;
    }

    const conversionRates = {
      USD_NGN: rates?.sellRate ?? 0,
      NGN_USD: 1 / (rates?.buyRate ?? 1),
      USD_USD: 1,
      NGN_NGN: 1,
    };

    const conversionKey = `${currencyFrom}_${currencyTo}` as const;
    const rate = conversionRates[conversionKey];

    setValue("amountTo", amountFrom * rate, { shouldValidate: true });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-32">
          Swap
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] p-0 gap-0 font-circularStd">
        {stage === 1 && (
          <div className="p-4 space-y-6">
            <div className="flex justify-between items-center">
              <button
                onClick={() => setIsOpen(false)}
                className="flex items-center text-sm text-gray-600"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Go Back
              </button>
              <div className="text-sm">
                Balance: {Math.floor(amount * 100) / 100} USD
              </div>
            </div>

            <div className="text-center">
              <h2 className="text-xl font-medium text-purple-600">Swap</h2>
              <div className="h-0.5 w-[60%] bg-purple-600 mx-auto mt-1"></div>
            </div>

            {/* Swap Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Swap From */}
              <div className="bg-gray-50 rounded-lg px-4 py-2 border-[#CACACA] border-2">
                <label className="text-sm text-right text-gray-500 block mb-2">
                  Swap from
                </label>
                <div className="flex justify-between items-center">
                  <Controller
                    name="amountFrom"
                    control={control}
                    render={({ field }) => {
                      console.log(field.value, typeof field.value);

                      return (
                        <input
                          {...field}
                          type="text"
                          value={field.value ?? ""} // Display the raw value
                          onChange={(e) => {
                            const rawValue = e.target.value.replace(/,/g, ""); // Remove commas for processing
                            if (/^\d*\.?\d{0,2}$/.test(rawValue)) {
                              field.onChange(rawValue); // Store the raw value in the form state
                              handleAmountChange(rawValue, true);
                            }
                          }}
                          placeholder="Enter amount"
                          className="bg-transparent text-lg font-medium outline-none w-2/3"
                        />
                      );
                    }}
                  />
                  <Controller
                    name="currencyFrom"
                    control={control}
                    render={({ field }) => {
                      const currencyTo = watch("currencyTo");
                      return (
                        <Select
                          value={field.value}
                          onValueChange={(value) => {
                            field.onChange(value);
                            handleCurrencyChange("currencyFrom", value);
                          }}
                        >
                          <SelectTrigger className="w-[100px]">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                          </SelectContent>
                        </Select>
                      );
                    }}
                  />
                </div>
              </div>
              {errors.amountFrom && (
                <span className="text-red-500 text-sm">
                  {errors.amountFrom.message}
                </span>
              )}
              {/* Swap Arrow */}
              <div className="flex justify-center">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <SwapCryptoIcon />
                </div>
              </div>
              {/* Swap To */}
              <div className="bg-gray-50 rounded-lg px-4 py-2 border-[#CACACA] border-2">
                <label className="text-sm text-gray-500 block mb-2">
                  You&apos;ll get
                </label>
                <div className="flex justify-between items-center">
                  <Controller
                    name="amountTo"
                    control={control}
                    render={({ field }) => {
                      return (
                        <input
                          {...field}
                          type="text"
                          value={field.value ?? ""} // Display the raw value
                          onChange={(e) => {
                            const rawValue = e.target.value.replace(/,/g, ""); // Remove commas for processing
                            if (/^\d*\.?\d{0,2}$/.test(rawValue)) {
                              field.onChange(rawValue); // Store the raw value in the form state
                              handleAmountChange(rawValue, false);
                            }
                          }}
                          placeholder="Enter amount"
                          className="bg-transparent text-lg font-medium outline-none w-2/3"
                        />
                      );
                    }}
                  />
                  <Controller
                    name="currencyTo"
                    control={control}
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                          handleCurrencyChange("currencyTo", value);
                        }}
                      >
                        <SelectTrigger className="w-[100px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="NGN">NGN</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>
              {errors.amountTo && (
                <span className="text-red-500 text-sm">
                  {errors.amountTo?.message}
                </span>
              )}
              {errors.currencyTo && (
                <span className="text-red-500 text-sm mx-auto text-center">
                  {errors.currencyTo?.message}
                </span>
              )}
              {/* Exchange Rate */}
              <div className="text-sm text-gray-500 flex gap-2 justify-center">
                {isLoadingRates ? (
                  <>
                    <p className="font-bold flex gap-2 items-center">
                      Sell: <Skeleton className="inline-block w-16 h-4" />
                    </p>
                  </>
                ) : (
                  <>
                    <p className="font-bold">
                      Sell:{" "}
                      <span className="font-normal">
                        {Math.floor((rates?.sellRate ?? 0) * 100) / 100}
                      </span>
                    </p>
                  </>
                )}
              </div>
              {watch("currencyFrom") === "NGN" &&
                watch("currencyTo") === "USD" && (
                  <div className="text-sm text-yellow-600 text-center p-2 bg-yellow-50 rounded">
                    NGN to USD swaps are temporarily disabled
                  </div>
                )}
              {/* Proceed Button */}
              <button
                type="button"
                onClick={handleRefreshRates}
                disabled={isLoadingRates}
                className="hover:text-gray-700 disabled:cursor-not-allowed transition-colors flex gap-2 items-center mx-auto mt-32"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Refresh rates</span>
              </button>
              <button
                type="submit"
                className="w-full bg-purple-600 text-white py-4 rounded-lg font-medium hover:bg-purple-700 transition-colors"
              >
                {isSwapping ? <LoadingSpinner className="mx-auto" /> : "Swap"}
              </button>
            </form>
          </div>
        )}
        {stage === 2 && (
          <div className="p-8 text-center space-y-4">
            <div className="flex justify-center">
              <CheckCircle className="w-16 h-16 text-green-500" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-800">
              Swap Successful!
            </h2>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600 space-y-2">
                <p>
                  From: {formatNumber(watch("amountFrom"))}{" "}
                  {watch("currencyFrom")}
                </p>
                <p>
                  To: {formatNumber(watch("amountTo"))} {watch("currencyTo")}
                </p>
                <p>
                  Rate:{" "}
                  {watch("currencyFrom") === "USD"
                    ? rates?.sellRate
                    : rates?.buyRate}
                </p>
              </div>
            </div>
            <DialogClose asChild>
              <button
                onClick={handleClose}
                className="mt-6 w-full bg-purple-600 text-white py-4 rounded-lg font-medium hover:bg-purple-700 transition-colors"
              >
                Done
              </button>
            </DialogClose>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
