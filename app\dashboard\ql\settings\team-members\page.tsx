"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON>it<PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AlertCircle, SquarePen } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm, Controller, useFieldArray } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { apiClient } from "@/app/utils";
import axios from "axios";
import useStore, { StoreState } from "@/store";

type User = {
  fullName: string;
  id: string;
  email: string;
  role: UserRole;
  lastLogin: string;
};

type InvitedUser = {
  id: string;
  email: string;
  role: UserRole;
  accepted: boolean;
};

type UserRole = "Owner" | "Admin" | "Developer" | "Support";

type OrganisationResponse = {
  id: string;
  organisationName: string;
  users: User[];
  invitedUsers: InvitedUser[];
};

const organisationSchema = z.object({
  organisationName: z.string().min(1, "Organisation name is required"),
  invitedUsers: z.array(
    z.object({
      email: z.string().email("Invalid email address"),
      role: z.enum(["Admin", "Developer", "Support", "SuperAdmin", "Owner"], {
        required_error: "Please select role",
      }),
    })
  ),
});

const addUserSchema = z.object({
  email: z.string().email("Invalid email address"),
  role: z.enum(["Admin", "Developer", "Support", "SuperAdmin", "Owner"], {
    required_error: "Please select role",
  }),
});

type OrganisationFormValues = z.infer<typeof organisationSchema>;
type AddUserFormValues = z.infer<typeof addUserSchema>;

// Function to format last login date
function formatLastLogin(isoString: string | undefined): string {
  if (!isoString) return "Never logged in";

  const date = new Date(isoString);
  if (isNaN(date.getTime())) return "Invalid date";

  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
    timeZoneName: "short",
  };

  return date.toLocaleString("en-US", options);
}

// Alternative: Format as relative time
function formatRelativeTime(isoString: string | undefined): string {
  if (!isoString) return "Never logged in";

  const date = new Date(isoString);
  if (isNaN(date.getTime())) return "Invalid date";

  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) return "Just now";
  if (diffMin < 60) return `${diffMin} minute${diffMin !== 1 ? "s" : ""} ago`;
  if (diffHour < 24) return `${diffHour} hour${diffHour !== 1 ? "s" : ""} ago`;
  if (diffDay < 7) return `${diffDay} day${diffDay !== 1 ? "s" : ""} ago`;

  // For older dates, use the standard format
  return formatLastLogin(isoString);
}

export default function Page() {
  const [loading, setLoading] = useState(false);
  const [isFetchingOrganisation, setIsFetchingOrganisation] = useState(false);
  const [organisation, setOrganisation] = useState<OrganisationResponse>();

  const { user } = useStore((state: StoreState) => state);

  useEffect(() => {
    fetchOrganisation();
  }, []);

  if (!user || user.role !== "Owner") {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <h2 className="text-lg font-semibold mb-2">Not authorized</h2>
        <p className="text-gray-600">
          You do not have permission to view this page.
        </p>
      </div>
    );
  }

  const fetchOrganisation = async () => {
    try {
      setIsFetchingOrganisation(true);
      const response = await apiClient.get("/v1/settings/get-organisation");

      setOrganisation(response.data.data);
    } catch (error) {
      console.log(error);
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        console.log("Organisation not found");
        setOrganisation(undefined);
      } else {
        console.error("Unexpected error:", error);
      }
    } finally {
      setIsFetchingOrganisation(false);
    }
  };

  function DeleteConfirmationDialog() {
    const [isDeleteOrgDialogOpen, setIsDeleteOrgDialogOpen] = useState(false);

    const [isDeletingOrg, setIsDeletingOrg] = useState(false);

    async function handleDeleteOrganisation() {
      try {
        setIsDeletingOrg(true);
        const response = await apiClient.delete(
          "/v1/settings/delete-organisation"
        );
        if (response.status === 200) {
          toast.success("Organisation deleted successfully");
          setIsDeleteOrgDialogOpen(false);
          await fetchOrganisation();
        } else {
          toast.error("Failed to delete organisation");
        }
      } catch (error) {
        console.log(error);
        toast.error("Failed to delete organisation");
      } finally {
        setIsDeletingOrg(false);
      }
    }

    return (
      <Dialog
        open={isDeleteOrgDialogOpen}
        onOpenChange={setIsDeleteOrgDialogOpen}
      >
        <DialogTrigger>
          <button
            onClick={() => setIsDeleteOrgDialogOpen(true)}
            className="bg-gray-100 px-2 py-1 hover:bg-red-200 text-xs rounded inline-flex items-center gap-1 text-red-600"
          >
            <Trash2 size={12} />
          </button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              Confirm Deletion
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this organisation? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-start">
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteOrganisation}
              disabled={isDeletingOrg}
            >
              {isDeletingOrg ? <Loader2 className="animate-spin" /> : ""}
              Delete
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteOrgDialogOpen(false)}
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  function CreateOrganisationDialog() {
    const [orgDialogOpen, setOrgDialogOpen] = useState(false);
    const [isCreatingOrg, setIsCreatingOrg] = useState(false);

    const form = useForm<OrganisationFormValues>({
      resolver: zodResolver(organisationSchema),
      defaultValues: {
        organisationName: "",
        invitedUsers: [],
      },
    });

    const addUserForm = useForm<AddUserFormValues>({
      resolver: zodResolver(addUserSchema),
      defaultValues: {
        email: "",
        role: undefined,
      },
    });

    const { fields, append, remove } = useFieldArray({
      control: form.control,
      name: "invitedUsers",
    });

    async function onSubmit(data: OrganisationFormValues) {
      try {
        setIsCreatingOrg(true);
        const response = await apiClient.post(
          "/v1/settings/create-organisation",
          data
        );
        if (response.status === 201 && response.data.data) {
          toast.success("Organisation created successfully");
          setOrgDialogOpen(false);
          await fetchOrganisation();
          form.reset();
        }
      } catch (error) {
        console.error("Error creating organisation:", error);
      } finally {
        setIsCreatingOrg(false);
      }
    }

    function addInvitedUser() {
      const { email, role } = addUserForm.getValues();

      if (!email || !role) {
        toast.error("Please enter both email and role");
      }

      if (addUserForm.formState.isValid) {
        append({ email, role });
        addUserForm.reset();
      } else {
        addUserForm.trigger();
      }
    }
    return (
      <Dialog open={orgDialogOpen} onOpenChange={setOrgDialogOpen}>
        <DialogTrigger>
          <button
            onClick={() => setOrgDialogOpen(true)}
            className="text-xs bg-white border-[#d3d3d5] border-solid border-2 px-4 py-2 rounded-md text-[#09090B] hover:bg-gray-100 transition-colors flex items-center gap-2"
          >
            <span className="text-[#7928FF]">+</span> Create Organisation
          </button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogDescription>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="organisationName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Organisation Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter organisation name"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <div>
                    <h3>Invite Team Members (Optional)</h3>

                    <div className="flex gap-2 mt-2">
                      <Form {...addUserForm}>
                        <div className="flex gap-2">
                          <FormField
                            control={addUserForm.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="email"
                                    placeholder="Email address"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={addUserForm.control}
                            name="role"
                            render={({ field }) => (
                              <FormItem>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="Select role" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="Admin">Admin</SelectItem>
                                    <SelectItem value="Developer">
                                      Developer
                                    </SelectItem>
                                    <SelectItem value="Support">
                                      Support
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <Button
                            type="button"
                            onClick={addInvitedUser}
                            className="bg-gray-200 text-black hover:bg-gray-300"
                          >
                            Add
                          </Button>
                        </div>
                      </Form>
                    </div>

                    {fields.length > 0 && (
                      <div className="mt-4 border rounded-md p-2">
                        <h4 className="text-xs font-medium mb-2">
                          Invited Users:
                        </h4>
                        <ul>
                          {fields.map((field, index) => (
                            <li
                              key={field.id}
                              className="flex justify-between items-center text-xs"
                            >
                              <span>
                                {field.email} ({field.role})
                              </span>
                              <button
                                type="button"
                                onClick={() => remove(index)}
                                className="text-red-500 hover:text-red-700"
                              >
                                Remove
                              </button>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  <Button type="submit" disabled={isCreatingOrg}>
                    {isCreatingOrg ? <Loader2 className="animate-spin" /> : ""}
                    Create Organisation
                  </Button>
                </form>
              </Form>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  function AddNewMemberDialog() {
    const [dialogOpen, setDialogOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const form = useForm<AddUserFormValues>({
      resolver: zodResolver(addUserSchema),
      defaultValues: {
        email: "",
        role: undefined,
      },
    });

    async function onSubmit(data: AddUserFormValues) {
      try {
        setIsLoading(true);
        const response = await apiClient.post("/v1/settings/add-to-team", {
          invitedUsers: [
            {
              email: data.email,
              role: data.role,
            },
          ],
        });

        if (response.status === 201) {
          toast.success("Invitation sent successfully");
          setDialogOpen(false);
          form.reset();
          await fetchOrganisation(); // Refresh the organisation data
        }
      } catch (error) {
        if (axios.isAxiosError(error)) {
          toast.error(
            error.response?.data.message || "Failed to send invitation"
          );
        } else {
          toast.error("An unexpected error occurred");
        }
        console.error("Error inviting member:", error);
      } finally {
        setIsLoading(false);
      }
    }

    return (
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <div className="flex justify-end">
          <DialogTrigger>
            <button className="text-xs border-[#d3d3d5] border-solid border-2 px-2 py-1 rounded-md text-[#09090B]">
              + New member
            </button>
          </DialogTrigger>
        </div>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-sm text-[#7928FF] my-6 border-b border-[#7928FF] w-1/2 mx-auto pb-2">
              Add New Member
            </DialogTitle>
            <DialogDescription className="flex flex-col gap-6 max-w-md">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Role</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select team member role..." />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Admin">Admin</SelectItem>
                            <SelectItem value="Developer">Developer</SelectItem>
                            <SelectItem value="Support">Support</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    className="w-full bg-[#7928FF]"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="animate-spin mr-2" />
                    ) : null}
                    Send Invite
                  </Button>
                </form>
              </Form>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  function DeleteTeamMemberDialog({ id }: { id: string }) {
    const [isDeleteTeamMemberDialogOpen, setIsDeleteTeamMemberDialogOpen] =
      useState(false);

    const [isLoading, setIsLoading] = useState(false);

    async function handleDeleteTeamMember() {
      try {
        setIsLoading(true);
        const response = await apiClient.post(
          "/v1/settings/delete-team-member",
          {
            removedUserId: id,
          }
        );
        if (response.status === 201) {
          toast.success("Team member deleted successfully");
          setIsDeleteTeamMemberDialogOpen(false);
          await fetchOrganisation();
        } else {
          toast.error("Failed to delete team member");
        }
      } catch (error) {
        console.log(error);
        toast.error("Failed to delete team member");
      } finally {
        setIsLoading(false);
      }
    }

    return (
      <Dialog
        open={isDeleteTeamMemberDialogOpen}
        onOpenChange={setIsDeleteTeamMemberDialogOpen}
      >
        <DialogTrigger>
          <button
            onClick={() => setIsDeleteTeamMemberDialogOpen(true)}
            className="bg-gray-100 px-2 py-1 hover:bg-red-200 text-xs rounded inline-flex items-center gap-1 text-red-600"
          >
            <Trash2 size={14} />
          </button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              Confirm Deletion
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this team member? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-start">
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteTeamMember} // Add your delete function here, e.g., handleDeleteTeamMember()
              disabled={isLoading}
            >
              {isLoading ? <Loader2 className="animate-spin" /> : ""}
              Delete
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteTeamMemberDialogOpen(false)}
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  function DeleteInviteDialog({ id }: { id: string }) {
    const [isDeleteInviteDialogOpen, setIsDeleteInviteDialogOpen] =
      useState(false);

    const [isLoading, setIsLoading] = useState(false);

    async function handleDeleteInvite() {
      try {
        setIsLoading(true);
        const response = await apiClient.post(
          "/v1/settings/delete-team-invite",
          {
            removedUserId: id,
          }
        );
        if (response.status === 201) {
          toast.success("Invite deleted successfully");
          setIsDeleteInviteDialogOpen(false);
          await fetchOrganisation();
        } else {
          toast.error("Failed to delete invite");
        }
      } catch (error) {
        console.log(error);
        toast.error("Failed to delete invite");
      } finally {
        setIsLoading(false);
      }
    }

    return (
      <Dialog
        open={isDeleteInviteDialogOpen}
        onOpenChange={setIsDeleteInviteDialogOpen}
      >
        <DialogTrigger>
          <button
            onClick={() => setIsDeleteInviteDialogOpen(true)}
            className="bg-gray-100 px-2 py-1 hover:bg-red-200 text-xs rounded inline-flex items-center gap-1 text-red-600"
          >
            <Trash2 size={12} />
          </button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              Confirm Deletion
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this invite? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-start">
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteInvite}
              disabled={isLoading}
            >
              {isLoading ? <Loader2 className="animate-spin" /> : ""}
              Delete
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteInviteDialogOpen(false)}
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  function EditRoleDialog({
    name,
    email,
    role,
  }: {
    name: string;
    email: string;
    role: UserRole;
  }) {
    const [dialogOpen, setDialogOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedRole, setSelectedRole] = useState<UserRole>(role);

    const form = useForm<AddUserFormValues>({
      resolver: zodResolver(addUserSchema),
      defaultValues: {
        email,
        role,
      },
    });

    async function onSubmit(data: AddUserFormValues) {
      try {
        setIsLoading(true);
        const response = await apiClient.post("/v1/settings/change-user-role", {
          memberEmail: email,
          role: data.role,
        });
        if (response.status === 201) {
          setDialogOpen(false);
          toast.success("User role updated.");
          await fetchOrganisation();
        }
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    }

    return (
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <div className="flex justify-end">
          <DialogTrigger>
            <button
              onClick={() => setDialogOpen(true)}
              className="text-xs border-[#d3d3d5] px-2 py-1 rounded text-blue-600 hover:bg-blue-200"
            >
              <SquarePen size={14} />
            </button>
          </DialogTrigger>
        </div>
        <DialogContent className="max-w-md font-circularStd">
          <DialogHeader>
            <DialogTitle className="text-center text-base text-[#7928FF] my-6 border-b border-[#7928FF] w-1/2 mx-auto pb-2">
              Edit Role
            </DialogTitle>
            <DialogDescription className="flex flex-col gap-6 max-w-md">
              {selectedRole !== role && (
                <p className="text-sm text-black mb-4">
                  Change {name}&apos;s role from <strong>{role}</strong> to{" "}
                  <strong>{selectedRole}</strong>
                </p>
              )}
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Role</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            setSelectedRole(value as UserRole);
                            field.onChange(value);
                          }}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select team member role..." />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Admin">Admin</SelectItem>
                            <SelectItem value="Developer">Developer</SelectItem>
                            <SelectItem value="Support">Support</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    className="w-full bg-[#7928FF]"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="animate-spin mr-2" />
                    ) : null}
                    Update
                  </Button>
                </form>
              </Form>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <>
      <section className="font-circularStd">
        <main className="max-w-[90%] mx-auto py-10">
          <h2 className="mb-4 text-sm">Organisation Details</h2>
          {organisation ? (
            <table className="w-full table-fixed text-left text-sm">
              <thead>
                <tr className="text-[#617889] text-left border-b border-b-black/10 text-xs lg:text-md bg-[#F7F7F9] font-circularStd">
                  <th>Name</th>
                  <th>ID</th>
                  <th>No of members</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody className="text-xs">
                <tr>
                  <td className="py-2">{organisation?.organisationName}</td>
                  <td className="py-2">{organisation?.id}</td>
                  <td className="py-2">{organisation?.users.length}</td>
                  <td className="py-2">
                    <DeleteConfirmationDialog />
                  </td>
                </tr>
              </tbody>
            </table>
          ) : (
            <div className="flex flex-col items-center justify-center py-8 px-4 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              <p className="text-gray-600 mb-4 text-center text-sm">
                You don&apos;t have an organisation yet. Create one to manage
                your team members.
              </p>
              <CreateOrganisationDialog />
            </div>
          )}

          {organisation && (
            <>
              <div className="my-8">
                <h2 className="mb-2 text-sm">Team Details</h2>
                <AddNewMemberDialog />
                <div className="h-full overflow-auto rounded-xs scrollbar-thin w-[95%]">
                  <table className="w-full lg:table-fixed">
                    <thead className="text-sm bg-muted sticky top-0">
                      <tr className="text-[#617889] text-left border-b border-b-black/10 text-xs lg:text-xs bg-[#F7F7F9] font-circularStd">
                        <th className="py-2">Members</th>
                        <th className="py-2">Email</th>
                        <th className="py-2">Role</th>
                        <th className="py-2">Last Login</th>
                        <th className="py-2">Action</th>
                      </tr>
                    </thead>
                    <tbody className="text-xs">
                      {organisation &&
                        organisation.users.map((user) => (
                          <tr key={user.id}>
                            <td className="py-2">{user.fullName}</td>
                            <td className="py-2">{user.email}</td>
                            <td className="py-2">{user.role}</td>
                            <td className="py-2">
                              {formatRelativeTime(user.lastLogin)}
                            </td>
                            <td>
                              {user.role !== "Owner" ? (
                                <div className="flex gap-2 items-center">
                                  <DeleteTeamMemberDialog id={user.id} />
                                  <EditRoleDialog
                                    name={user.fullName}
                                    email={user.email}
                                    role={user.role}
                                  />
                                </div>
                              ) : (
                                ""
                              )}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {organisation.invitedUsers.length !== 0 ? (
                <div>
                  <h2 className="mb-2 text-sm">Invited Members</h2>
                  <div className="h-full overflow-auto rounded-xs scrollbar-thin">
                    <table className="w-full table-auto">
                      <thead className="text-sm bg-muted sticky top-0">
                        <tr className="text-[#617889] text-left border-b border-b-black/10 text-xs lg:text-xs bg-[#F7F7F9] font-circularStd">
                          <th className="py-2">Email</th>
                          <th className="py-2">Role</th>
                          <th className="py-2">Status</th>
                          <th className="py-2">Action</th>
                        </tr>
                      </thead>
                      <tbody className="text-xs">
                        {organisation &&
                          organisation.invitedUsers.map((invitedUser) => (
                            <tr key={invitedUser.id}>
                              <td className="py-2">{invitedUser.email}</td>
                              <td className="py-2">{invitedUser.role}</td>
                              <td>
                                {invitedUser.accepted ? "Accepted" : "Pending"}
                              </td>
                              <td>
                                <DeleteInviteDialog id={invitedUser.id} />
                              </td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : null}
            </>
          )}
        </main>
      </section>
    </>
  );
}

// TODO:
// 1. Finalize delete invite
