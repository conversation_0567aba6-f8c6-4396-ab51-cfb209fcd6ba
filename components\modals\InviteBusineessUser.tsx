import useStore from "@/store"
import { XMarkIcon } from "../icons"
import Select from "react-select";
import COUNTRIES from "@/countries.json";
import { FormEvent, useState } from "react";
import { adminApi } from "@/app/utils";
import LoadingSpinner from "../LoadingSpinner";

function InviteBusineessUser() {
  const { setModal } = useStore();
  const [loading, setLoading] = useState(false);
  const [formDetails, setFormDetails] = useState<Record<string, any>>({
    fullName: "",
    businessName: "",
    email: "",
    country: "",
    phone: "",
  });
  const countries = COUNTRIES.map((country) => ({
    value: country,
    label: country,
  }));

  const handleChange = (e: any) => {
    setFormDetails({ ...formDetails, [e.target.name]: e.target.value });
  };

  const sendInvite = (e: FormEvent) => {
    e.preventDefault();

    setLoading(true);
    // send user an invite
    adminApi.post('/v1/admin/users/send-invite', {
      fullName: formDetails.fullName,
      email: formDetails.email,
      // businessName: formDetails.businessName,
      // country: formDetails.country,
      // phone: formDetails.phone,
    })
    .then(res => {
      if(res.status.toString().startsWith("2")) {
        window.location.reload();
      } else {
        // Handle error
      }
    })
    .catch(err => {
      // handle the errors
      console.log(err);
    }).finally(() => {
      setLoading(false);
    });
  }

  return (
    <div className='bg-white rounded-md h-fit max-w-80 md:max-w-96 p-4 grow'>
      <div className="flex justify-between items-center">
        <h3 className="font-semibold">Invite User</h3>
        <button onClick={() => setModal(null)}><XMarkIcon className="w-6 h-6" /></button>
      </div>

      <form onSubmit={sendInvite} className="space-y-3 mt-3">
        <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
          <label className="text-[#999999] text-xs font-light">
            Full Name
          </label>
          <input
            name="fullName"
            value={formDetails.fullName}
            onChange={handleChange}
            className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
          />
        </div>
        <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
          <label className="text-[#999999] text-xs font-light">
            Email Address
          </label>
          <input
            name="email"
            value={formDetails.email}
            onChange={handleChange}
            type="email"
            className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
          />
        </div>
      
        <button
          type="submit"
          className="bg-appPurple text-white py-2 rounded-md font-semibold mt-5 w-full"
        >
          {!loading ? "Send invite" : <LoadingSpinner className="mx-auto" />}
        </button>
      </form>
    </div>
  )
}

export default InviteBusineessUser