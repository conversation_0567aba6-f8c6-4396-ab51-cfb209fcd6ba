export interface BankData {
  account_name: string;
  account_number: string;
  bank_code: string;
  bank_name: string;
  balance: number;
  currency: string;
}

export interface CryptoBalanceCardProps {
  wallets: Record<string, string | number | null>[];
  showCryptoBalance: boolean;
  handleShowCryptoBalance: () => void;
}

// export interface CryptoWalletType {
//   accountId: string | null;
//   account_name: string;
//   account_num: string;
//   address: string | null;
//   addressRef: string | null;
//   addressType: string | null;
//   assetId: string | null;
//   assetType: string;
//   balance: number;
//   bank_code: string | null;
//   bank_name: string | null;
//   chain: string | null;
//   createdAt: string;
//   fixedRate: number;
//   guid: string;
//   id: string;
//   isActive: boolean;
//   isDefaultRate: boolean;
//   mode: string | null;
//   name: string;
//   network: string;
//   organizationId: string | null;
//   pendingDebit: number;
//   sellMargin: number;
//   spread: number;
//   subAccountId: string | null;
//   symbol: string;
//   uid: string;
//   updatedAt: string;
//   userId: string;
//   walletType: string;
// }

// export interface TransactionType {
//   assetType: string;
//   autoPayout: boolean;
//   conversionRate: number;
//   createdAt: string;
//   cryptoAmount: number;
//   cryptoBalance: number;
//   cryptoWallet: CryptoWalletType;
//   cryptoWalletId: string | null;
//   customerEmail: string | null;
//   customerPhone: string | null;
//   done: boolean;
//   feeAmount: number;
//   fiatAmount: number;
//   fiatBalance: number;
//   fiatWalletId: string;
//   fromAccName: string | null;
//   fromAccNum: string | null;
//   fromBankCode: string | null;
//   fromBankName: string | null;
//   id: string;
//   narration: string | null;
//   network: string | null;
//   status: string | null;
//   transactionID: string | null;
//   transactionType: "DEPOSIT" | "WITHDRAW" | "SWAP";
//   updatedAt: string;
//   userId: string;
//   usdEquivalent: number;
//   wallet_address: string;
//   toAccName: string | null;
//   toAccNum: string | null;
//   toBankCode: string | null;
//   toBankName: string | null;
//   fiatBalanceBefore: number;
// }

export interface UserType {
  id: string;
  fullName: string;
  email: string;
  phone: string;
}

export interface CryptoWalletType {
  id: string;
  symbol: string;
  chain: string | null;
}

export interface TransactionType {
  id: string;
  createdAt: string;
  updatedAt: string;
  done: boolean;
  wallet_address: string | null;
  transactionType: "DEPOSIT" | "WITHDRAW" | "SWAP";
  assetType: string;
  userId: string;
  transactionHash: string | null;
  conversionRate: number;
  cryptoAmount: number;
  cryptoBalance: number;
  cryptoBalanceBefore: number;
  cryptoWalletId: string | null;
  customerEmail: string | null;
  customerPhone: string | null;
  fiatAmount: number;
  fiatBalance: number;
  fiatBalanceBefore: number;
  fiatWalletId: string;
  fromAccName: string | null;
  fromAccNum: string | null;
  fromBankCode: string | null;
  fromBankName: string | null;
  transactionID: string | null;
  toAccName: string | null;
  toAccNum: string | null;
  toBankCode: string | null;
  toBankName: string | null;
  status: string | null;
  autoPayout: boolean;
  feeAmount: number;
  network: string | null;
  usdEquivalent: number;
  narration: string | null;
  cryptoWallet: CryptoWalletType | null;
  user: UserType;
}

export interface WalletType {
  accountId: string | null;
  account_name: string;
  account_num: string | null;
  address: string;
  addressRef: string | null;
  addressType: string | null;
  assetId: string | null;
  assetType: string;
  balance: number;
  bank_code: string | null;
  bank_name: string | null;
  chain: string | null;
  createdAt: string;
  fixedRate: number;
  guid: string;
  id: string;
  isActive: boolean;
  isDefaultRate: boolean;
  mode: string | null;
  name: string;
  network: string;
  organizationId: string | null;
  pendingDebit: number;
  sellMargin: number;
  spread: number;
  subAccountId: string | null;
  symbol: string;
  uid: string;
  updatedAt: string;
  userId: string;
  walletType: string;
}

export interface Data {
  recentHistory: TransactionType[];
  totalBalance: number;
  totalDeposits: number;
  totalSent: number;
  wallets: WalletType[];
}

export interface RecentTransactionsTableProps {
  transactions: TransactionType[];
  generatePDF: (transaction: TransactionType) => void;
  exportReceiptAsImage: (transaction: TransactionType) => Promise<void>;
  fiatBankData?: any;
}
