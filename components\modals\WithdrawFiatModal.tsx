import useStore from "@/store";
import { FormEvent, useCallback, useEffect, useState } from "react";
import NairaIcon from "@/assets/images/wallets/naira.svg";
import { XMarkIcon } from "../icons";
import Select from "react-select";
import Image from "next/image";
import banks from "@/banks.json";
import mapleradBanks from "@/mapleradBanks.json";
import swerveBanks from "@/swerveBanks.json";
import { api } from "@/app/utils";
import LoadingSpinner from "../LoadingSpinner";
import { ToastContainer, toast } from "react-toastify";
import ProcessingModal from "./ProcessingModal";

function WithdrawFiatModal({ wallet }: { wallet: Record<string, any> }) {
  const { setModal, fiatProvider } = useStore();
  const [stage, setState] = useState(1);
  const [loading, setLoading] = useState(false);
  const [formDetails, setFormDetails] = useState({
    amount: 0,
    toBankCode: "",
    toBankName: "",
    toAccName: "",
    toAccNumber: "",
    narration: "",
  });
  const [isOtpAvailable, setisOtpAvailable] = useState(false);
  const [otp, setOtp] = useState("");



  const transferFunds = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);

    console.log("Okay");

    if (formDetails.amount < 0) {
      toast.error("Cannot withdraw a negative value");
      setLoading(false);
      return;
    }

    api
      .post("/v1/wallets/fiat-transfer", {
        accountName: formDetails.toAccName,
        accountNumber: formDetails.toAccNumber,
        bankCode: formDetails.toBankCode,
        amount: formDetails.amount, // convert to kobo
        otp,
        narration: formDetails.narration,
      })
      .then((res) => {
        console.log(res.data);
        if (res.status.toLocaleString().startsWith("2")) {
          // setModal(<PendingModal />);
          setModal(
            <ProcessingModal
              message={res.data.message || "Withdrawal in Progress"}
            />
          );
        } else {
          toast.error(res.data.message);
        }
      })
      .catch((err) => {
        console.log("error", err);
        toast.error(err.message);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const sendWithrawalOtp = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (
      !formDetails.amount ||
      !formDetails.toAccNumber ||
      !formDetails.toAccName ||
      !formDetails.toBankName
    ) {
      toast.error("Fill in missing properties");
      setLoading(false);
      return;
    }

    if (formDetails.amount < 0) {
      toast.error("Cannot withdraw a negative value");
      setLoading(false);
      return;
    }

    api
      .post("/v1/wallets/send/fiat-withdrawal-otp", {
        accountName: formDetails.toAccName,
        accountNumber: formDetails.toAccNumber,
        bankCode: formDetails.toBankCode,
        bankName: formDetails.toBankName,
        amount: formDetails.amount, // convert to kobo
      })
      .then((res) => {
        console.log(formDetails);
        if (res?.status?.toString().startsWith("2")) {
          setisOtpAvailable(true);
        } else {
          toast.error(res.data.message);
        }
      })
      .catch((err) => {
        console.log("ërror", err);
        toast.error(err.message);
      })
      .finally(() => {
        console.log("finally");
        setLoading(false);
      });
  };

  useEffect(() => {
    const { toBankCode, toAccNumber } = formDetails;
    if (toBankCode && toAccNumber.length == 10) {
      console.log("fetch bank account name");
      api
        .post("/v1/wallets/resolve-account", {
          accountNumber: formDetails.toAccNumber,
          bankCode: formDetails.toBankCode,
        })
        .then((res) => {
          console.log(res.data);
          if (res.data?.data?.account_name) {
            setFormDetails((prev) => ({
              ...prev,
              toAccName: res.data.data.account_name,
            }));
          }
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, [formDetails.toBankCode, formDetails.toAccNumber]);

  return (
    <div className="bg-white rounded-md h-fit max-w-80 md:max-w-96 p-4 grow">
      <div className="flex justify-between items-center">
        <h3 className="font-semibold">Withdraw</h3>
        <button onClick={() => setModal(null)}>
          <XMarkIcon className="w-6 h-6" />
        </button>
      </div>

      {stage === 1 && (
        <form
          onSubmit={transferFunds}
          className="space-y-1.5 mt-8 max-w-[260px] mx-auto pb-8"
        >
          <div>
            <div className="py-1 px-3 border border-[#CACACA] rounded flex justify-between items-center focus-within:border-appPurple">
              <div className="flex flex-col">
                <label className="text-[#999999] text-xs font-light">
                  How much
                </label>
                <input
                  name="amount"
                  autoComplete="off"
                  type="number"
                  value={formDetails.amount || ""}
                  onChange={(e) =>
                    !isNaN(Number(e.target.value)) &&
                    setFormDetails({
                      ...formDetails,
                      [e.target.name]: Number(e.target.value),
                    })
                  }
                  className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
                />
              </div>
              <div className="flex items-center gap-1">
                <Image src={NairaIcon} alt="." className="w-5 h-5" />
                <span className="text-sm">NGN</span>
              </div>
            </div>
            <div className="fees text-[10px] ml-3 mt-3">
              <p className="text-blue-500">
                Bal: NGN {(wallet?.balance).toLocaleString()}
              </p>
              <p className="text-blue-500">Fee: NGN 0</p>
              {/* <p className='text-red-500'>Bal After: NGN {((wallet?.balance / 100 || 0) - (Number(formDetails.amount) + SERVICE_FEE)).toString().startsWith("-") ? 0 : ((wallet?.balance / 100 || 0) - (Number(formDetails.amount) + SERVICE_FEE)).toLocaleString()}</p> */}
            </div>
          </div>

          <Select
            options={
              fiatProvider === "NOVAC"
                ? banks.map((bank) => ({ label: bank.name, value: bank.code }))
                : mapleradBanks.map((bank) => ({
                    label: bank.name,
                    value: bank.code,
                  }))
            }
            placeholder="Select Bank"
            isSearchable={true}
            onChange={(e: any) =>
              setFormDetails({
                ...formDetails,
                toBankName: e.label,
                toBankCode: e.value,
              })
            }
            styles={{
              control: (baseStyles) => ({
                ...baseStyles,
                border: "1px solid #CACACA",
                boxShadow: "none",
                ":focus-within": {
                  border: "1px solid #7928FF",
                },
                padding: "4px 2px 4px 2px",
                fontSize: "14px",
                marginTop: "8px",
              }),
            }}
          />

          <div>
            <p className="text-[13px] mb-1 font-light text-appGrayTextLight">
              Withdraw to
            </p>
            <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
              <label className="text-[#999999] text-xs font-light">
                Account Number
              </label>
              <input
                name="toAccNumber"
                value={formDetails.toAccNumber}
                onChange={(e) =>
                  e.target.value.length <= 10 &&
                  setFormDetails({
                    ...formDetails,
                    [e.target.name]: e.target.value,
                  })
                }
                className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
              />
            </div>
            <div
              style={{ opacity: formDetails.toAccName ? "100" : "0" }}
              className={`text-[13px] mt-1.5 text-appPurple flex gap-1 items-center`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="25"
                height="24"
                className="w-5 h-5"
                viewBox="0 0 25 24"
                fill="none"
              >
                <path
                  d="M5.6 16C5.3 15.5 4.7 15.4 4.2 15.6C3.7 15.9 3.6 16.5 3.8 17C4.1 17.5 4.7 17.6 5.2 17.4C5.7 17.1 5.8 16.5 5.6 16ZM5.2 6.6C4.7 6.4 4.1 6.5 3.8 7C3.6 7.5 3.7 8.1 4.2 8.4C4.7 8.6 5.3 8.5 5.6 8C5.8 7.5 5.7 6.9 5.2 6.6ZM20.8 8.4C21.3 8.1 21.4 7.5 21.2 7C20.9 6.5 20.3 6.4 19.8 6.6C19.3 6.9 19.2 7.5 19.4 8C19.7 8.5 20.3 8.6 20.8 8.4ZM4.5 12C4.5 11.4 4.1 11 3.5 11C2.9 11 2.5 11.4 2.5 12C2.5 12.6 2.9 13 3.5 13C4.1 13 4.5 12.6 4.5 12ZM7.7 18.8C7.2 18.9 6.8 19.5 7 20C7.1 20.5 7.7 20.9 8.2 20.7C8.7 20.6 9.1 20 8.9 19.5C8.8 19 8.3 18.7 7.7 18.8ZM21.5 11C20.9 11 20.5 11.4 20.5 12C20.5 12.6 20.9 13 21.5 13C22.1 13 22.5 12.6 22.5 12C22.5 11.4 22.1 11 21.5 11ZM20.8 15.6C20.3 15.3 19.7 15.5 19.4 16C19.1 16.5 19.3 17.1 19.8 17.4C20.3 17.7 20.9 17.5 21.2 17C21.4 16.5 21.3 15.9 20.8 15.6ZM17.5 3.3C17 3 16.4 3.2 16.1 3.7C15.8 4.2 16 4.8 16.5 5.1C17 5.4 17.6 5.2 17.9 4.7C18.1 4.2 18 3.6 17.5 3.3ZM17.3 18.8C16.8 18.7 16.2 19 16.1 19.5C16 20 16.3 20.6 16.8 20.7C17.3 20.8 17.9 20.5 18 20C18.1 19.5 17.8 19 17.3 18.8ZM12.5 20C11.9 20 11.5 20.4 11.5 21C11.5 21.6 11.9 22 12.5 22C13.1 22 13.5 21.6 13.5 21C13.5 20.4 13.1 20 12.5 20ZM12.5 2C11.9 2 11.5 2.4 11.5 3C11.5 3.6 11.9 4 12.5 4C13.1 4 13.5 3.6 13.5 3C13.5 2.4 13.1 2 12.5 2Z"
                  fill="#DBC5FF"
                />
              </svg>
              <p>{formDetails.toAccName}</p>
            </div>

            {isOtpAvailable ? (
              <div className="flex flex-col border mt-1 rounded p-2 w-[260px] mx-auto">
                <label className="text-[#999999] text-xs font-light">OTP</label>
                <input
                  name="otp"
                  autoComplete="off"
                  type="text"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
                />
              </div>
            ) : null}

            {!isOtpAvailable ? (
              <button
                onClick={sendWithrawalOtp}
                className={`bg-appPurple text-white py-2 px-4 rounded block w-full mt-1.5`}
              >
                {loading ? <LoadingSpinner className="mx-auto" /> : "Proceed"}
              </button>
            ) : null}

            {isOtpAvailable ? (
              <div className="flex flex-col gap-4">
                <button
                  type="submit"
                  className="bg-appPurple text-white rounded py-2 px-4 mt-2.5 text-sm w-[260px] block mx-auto"
                >
                  {loading ? (
                    <LoadingSpinner className="mx-auto" />
                  ) : (
                    "Continue"
                  )}
                </button>
                <button
                  onClick={() => setModal(null)}
                  className="text-center text-xs mx-auto w-fit block text-appPurple mt-1"
                >
                  Cancel
                </button>
              </div>
            ) : null}
          </div>
        </form>
      )}

      {stage === 2 && (
        <div className="space-y-1.5 mt-8 max-w-[260px] mx-auto pb-8">
          <Image
            src={NairaIcon}
            alt="naira icon"
            className="mx-auto block w-fit"
          />

          <div className="my-3 text-appGrayTextLight text-[13px] border-b pb-2 border-dashed space-y-1">
            <div className="flex justify-between items-center">
              <p>Amount</p>
              <p>₦{formDetails.amount.toLocaleString()}</p>
            </div>
            {/* <div className="flex justify-between items-center">
              <p>Fee:</p>
              <p>₦{SERVICE_FEE}</p>
            </div> */}
          </div>

          <div className="my-3 text-appGrayTextLight text-[13px] space-y-1">
            <div className="flex justify-between items-center">
              <p>Bank Name</p>
              <p>{formDetails.toBankName}</p>
            </div>
            <div className="flex justify-between items-center">
              <p>Account Number:</p>
              <p>{formDetails.toAccNumber}</p>
            </div>
            <div className="justify-between items-center">
              <p>Account Name:</p>
              <p>{formDetails.toAccName}</p>
            </div>
            {formDetails.narration && (
              <div className="my-3 text-appGrayTextLight text-[13px] space-y-1">
                <div className="flex justify-between items-center">
                  <p>Narration:</p>
                  <p>{formDetails.narration}</p>
                </div>
              </div>
            )}
          </div>

          <button
            onClick={transferFunds}
            className={`bg-appPurple text-white py-2 px-4 rounded block w-full mt-1.5`}
          >
            {!loading ? "Continue" : <LoadingSpinner className="mx-auto" />}
          </button>
          <button
            onClick={() => setModal(null)}
            className="text-center text-xs mx-auto w-fit block text-appPurple mt-1"
          >
            Cancel
          </button>
        </div>
      )}
    </div>
  );
}

export default WithdrawFiatModal;
