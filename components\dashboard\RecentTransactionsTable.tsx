import React, { useState, Profiler } from "react";
import { RecentTransactionsTableProps, TransactionType } from "@/types/types";
import { handleExportToCSV } from "@/lib/utils";
import { DepositIcon, WithdrawIcon, ExchangeIcon } from "@/components/icons";

import {
  ChevronLeft,
  ChevronRight,
  Download,
  FileDown,
  ImageDown,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { usePagination } from "@/lib/custom-hooks";

type TransactionTypeKey = TransactionType["transactionType"];

function renderUserDetails(transaction: TransactionType, fiatBankData?: any) {
  if (transaction.assetType === "FIAT") {
    return transaction.transactionType === "DEPOSIT" ? (
      <div className="gap-2">
        <p>{transaction.fromAccName || fiatBankData?.account_name}</p>
        <div className="flex gap-2">
          <p>{transaction.fromAccNum || fiatBankData?.account_number}</p>
          <p className="text-[11px]">
            {transaction.fromBankName || fiatBankData?.bank_name}
          </p>
        </div>
      </div>
    ) : (
      <div className="gap-2">
        <p>{transaction.toAccName}</p>
        <div className="flex gap-2">
          <p>{transaction.toAccNum}</p>
          <p className="text-[11px]">{transaction.toBankName}</p>
        </div>
      </div>
    );
  }
  return <p>{transaction.cryptoWalletId}</p>;
}

function TransactionStatusIndicator({
  transaction,
}: {
  transaction: TransactionType;
}) {
  const status =
    transaction.status === "FAILED"
      ? "FAILED"
      : transaction.status === "SUCCESS"
        ? "SUCCESS"
        : "PROCESSING";

  const statusStyles = {
    FAILED: {
      container: "bg-red-200",
      dot: "bg-red-500",
    },
    SUCCESS: {
      container: "bg-green-200",
      dot: "bg-[#13BF62]",
    },
    PROCESSING: {
      container: "bg-yellow-200",
      dot: "bg-yellow-500",
    },
  };

  return (
    <div
      className={`flex items-center justify-center w-3 h-3 rounded-sm ${statusStyles[status].container}`}
    >
      <span className={`${statusStyles[status].dot} w-1 h-1`} />
    </div>
  );
}

export default function RecentTransactionsTable({
  transactions,
  generatePDF,
  fiatBankData,
  exportReceiptAsImage,
}: RecentTransactionsTableProps) {
  const {
    currentPage,
    rowsPerPage,
    handleNext,
    handlePrev,
    handleRowsPerPageChange,
    paginatedData,
    rowsPerPageOptions,
    indexOfFirstRow,
    indexOfLastRow,
    totalPages,
  } = usePagination<TransactionType>(transactions);

  const emptyTransactions = transactions.length === 0;

  return (
    <>
      <div className="lg:max-w-[95%] px-6 lg:mx-auto lg:mt-12 lg:flex font-circularStd">
        <button
          onClick={() => handleExportToCSV(transactions)}
          className="border-[#E2E8F0] text-sm mb-4 border-solid border-2 px-3 py-1 rounded-md flex items-center ml-auto"
        >
          <Download className="text-[#67667A] w-4 h-4 mr-1" />
          <span className="text-[#807F94]">Export Data</span>
        </button>
      </div>
      <div className="px-10 overflow-x-auto py-10 mb-20 max-w-[25em] sm:max-w-full sm:mx-auto">
        <table className="w-full lg:table-auto sm:table-fixed text-left">
          <thead className="">
            <tr className="text-[#617889] border-b border-b-black/10 text-xs lg:text-md bg-[#F7F7F9] font-circularStd">
              <th className="rounded-tl-md p-3 rounded-bl-md lg:py-1 lg:pl-2">
                Type
              </th>
              <th className="p-3">Asset</th>
              <th className="p-3">Qty</th>
              <th className="p-3">Rate</th>
              <th className="p-3">Total</th>
              <th className="p-3 hidden md:block">User</th>
              <th className="p-3">BalB</th>
              <th className="p-3">BalA</th>
              <th className="p-3 hidden md:block">Date</th>
              <th className="p-3">Status</th>
              <th className="p-3 rounded-tr-md rounded-br-md">Receipt</th>
            </tr>
          </thead>
          <tbody className="font-circularStd">
            {paginatedData.map((transaction: TransactionType, index) => {
              const icons: Record<TransactionTypeKey, React.ReactNode> = {
                DEPOSIT: <DepositIcon />,
                WITHDRAW: <WithdrawIcon />,
                SWAP: <ExchangeIcon />,
              };

              const IconComponent = icons[transaction.transactionType];

              return (
                <tr
                  key={index}
                  className="text-xs lg:text-md border-b border-b-black/10 hover:bg-black/5 transition-all duration-200"
                >
                  <td className="py-3 pl-2">
                    {IconComponent && (
                      <div className="w-6 h-6 flex items-center justify-center">
                        {IconComponent}
                      </div>
                    )}
                  </td>
                  <td className="p-3">
                    {transaction.assetType === "FIAT"
                      ? "NGN"
                      : transaction.assetType}
                  </td>
                  <td className="p-3">
                    {transaction.cryptoWalletId &&
                    transaction.assetType === "FIAT"
                      ? ""
                      : Math.floor(transaction.cryptoAmount * 10) / 10}
                  </td>

                  <td className="p-3">
                    {transaction.cryptoWalletId &&
                    transaction.assetType === "FIAT"
                      ? ""
                      : Math.floor(transaction.conversionRate * 10) / 10}
                  </td>

                  <td className="p-3">
                    {transaction.assetType === "USD" &&
                    transaction.transactionType === "WITHDRAW"
                      ? Math.floor(transaction.cryptoAmount)
                      : Math.floor(transaction.fiatAmount)}
                  </td>

                  <td className="p-3 hidden md:block">
                    {renderUserDetails(transaction, fiatBankData)}
                  </td>

                  <td className="p-3">
                    {transaction.assetType === "USD"
                      ? transaction.cryptoBalanceBefore.toFixed(1)
                      : Math.floor(transaction.fiatBalanceBefore)}
                  </td>
                  <td className="p-3">
                    {transaction.assetType === "USD"
                      ? transaction.cryptoBalance.toFixed(1)
                      : Math.floor(transaction.fiatBalance)}
                  </td>

                  <td className="p-3 hidden md:block">
                    {new Date(transaction.createdAt).toLocaleString(undefined, {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </td>
                  <td className="p-3">
                    <TransactionStatusIndicator transaction={transaction} />
                  </td>

                  <td className="p-3">
                    {transaction.transactionType === "WITHDRAW" &&
                    transaction.done ? (
                      <>
                        <div className="flex gap-1">
                          <FileDown
                            onClick={() => generatePDF(transaction)}
                            className="w-4 h-4 text-appPurple hover:text-appPurpleDark"
                          />
                          <ImageDown
                            onClick={() => exportReceiptAsImage(transaction)}
                            className="w-4 h-4 text-appPurple hover:text-appPurpleDark"
                          />
                        </div>
                      </>
                    ) : null}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>

        <div className="flex gap-28 items-center justify-between lg:text-sm font-circularStd mt-14">
          <div className="flex gap-2 text-xs lg:text-sm items-center text-[#807F94]">
            <span>Rows per page:</span>
            <Select
              value={String(rowsPerPage)}
              onValueChange={(value) => handleRowsPerPageChange(Number(value))}
            >
              <SelectTrigger className="w-[60px] h-[30px]">
                <SelectValue placeholder={rowsPerPage} />
              </SelectTrigger>
              <SelectContent className="text-[#807F94]">
                <SelectGroup>
                  {rowsPerPageOptions.map((option) => (
                    <SelectItem key={option} value={String(option)}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="flex lg:gap-2 gap-2 text-xs lg:text-sm">
            <button
              className="border-[#E2E8F0] border-solid border-2 px-3 py-1 rounded-md flex items-center"
              onClick={handlePrev}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="text-[#67667A] w-4 h-4" />
              <span className="text-[#807F94]">Prev</span>
            </button>
            <button
              className="border-[#E2E8F0] border-solid border-2 px-3 py-1 rounded-md flex items-center"
              onClick={handleNext}
              disabled={currentPage === totalPages}
            >
              <span className="text-[#807F94]">Next</span>
              <ChevronRight className="text-[#67667A] w-4 h-4" />
            </button>
          </div>
          <div className="text-[#807F94] text-xs lg:text-sm">
            <p>
              {`Showing ${
                indexOfFirstRow === 0 ? 1 : indexOfFirstRow
              } - ${indexOfLastRow} of ${transactions.length}`}
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
