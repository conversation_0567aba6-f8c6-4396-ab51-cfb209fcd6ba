import useStore from "@/store";
import { XMarkIcon } from "../icons";
import { getTokenName } from "@/app/utils";
import { useEffect, useState } from "react";

function TransactionDetailsModal({ transaction }: { transaction: Record<string, any> }) {
    const { setModal } = useStore();
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        setIsVisible(true);
    }, []);

    const closeModal = () => {
        setIsVisible(false);
        setTimeout(() => setModal(null), 300); 
    };

    return (
        <div className={`fixed inset-0 bg-black bg-opacity-50 transition-opacity duration-300 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
            <div 
                className={`fixed top-0 right-0 h-screen w-1/3 md:1/3 bg-[#E9E9E9] shadow-lg overflow-y-auto transition-transform duration-300 ease-in-out transform ${
                    isVisible ? 'translate-x-0' : 'translate-x-full'
                }`}
            >
                <div className="flex justify-between items-center mb-6 bg-white p-5">
                    <h3 className="font-semibold text-lg">Transaction Details</h3>
                    <button onClick={closeModal}>
                        <XMarkIcon className="w-6 h-6" />
                    </button>
                </div>
                <div className="p-5 animate-fadeIn">
                    <div className="flex flex-col gap-2">
                        <div className="flex justify-between items-center">
                            <h4 className="font-medium text-sm text-[#666581]">Total Amount</h4>
                        </div>
                        <div className="flex justify-between items-center">
                            <div className="flex items-center font-semibold">
                                <p className="mr-2">
                                    {transaction.assetType === "CRYPTO"
                                        ? transaction.cryptoAmount
                                        : transaction.fiatAmount}
                                </p>
                                {transaction.assetType !== "FIAT"
                                    ? getTokenName(transaction.cryptoWallet?.symbol)
                                        .symbol
                                    : transaction.fiatWallet?.symbol ?? "NGN"}
                            </div>
                            {
                                transaction.tansactionType === "DEPOSIT" ? (
                                    <span className="text-green-500 font-semibold">
                                        Completed
                                    </span>
                                ) : transaction.done ? (
                                    <span className="text-green-500 font-semibold">
                                        Completed
                                    </span>
                                ) : transaction.status === "INCOMPLETE" ? (
                                    <span className="text-orange-500 font-semibold">
                                        Incomplete
                                    </span>
                                ) : transaction.transactionID ||
                                    transaction.status === "PROCESSING" ? (
                                    <span className="text-yellow-500 font-semibold">
                                        Processing
                                    </span>
                                ) : transaction.status === "FAILED" ? (
                                    <span className="text-red-500 font-semibold">
                                        Failed
                                    </span>
                                ) : (
                                    <span className="text-red-500 font-semibold">
                                        Processing
                                    </span>
                                )}
                        </div>
                        <div className="flex flex-col gap-2 mt-5">
                            <h4 className="font-medium text-sm text-[#666581]">Customer</h4>
                            <p className="w-full break-all">
                                {transaction.transactionType === "PAYOUT" ? (
                                    <>
                                        <p>{transaction.toAccName}</p>
                                        <p className="text-xs font-light mt-1">
                                            {transaction.toAccNum} {transaction.toBankName}
                                        </p>
                                    </>
                                ) : transaction.cryptoWalletId ? (
                                    <>{transaction.wallet_address}</>
                                ) : transaction.transactionType === "DEPOSIT" ? (
                                    <>
                                        <p>{transaction.fromAccName}</p>
                                        <p className="text-xs font-light mt-1">
                                            {transaction.fromAccNum}{" "}
                                            {transaction.fromBankName}
                                        </p>
                                    </>
                                ) : (
                                    <>
                                        <p>{transaction.toAccName}</p>
                                        <p className="text-xs font-light mt-1">
                                            {transaction.toAccNum} {transaction.toBankName}
                                        </p>
                                    </>
                                )}
                            </p>
                        </div>
                        <div className="flex flex-col gap-2 mt-5">
                            <h4 className="font-medium text-sm text-[#666581]">Details</h4>
                            <div className="bg-white rounded-md p-4">
                                <div className="flex flex-col gap-2">
                                    <h4 className="font-medium text-xs text-[#666581]">Date & Time</h4>
                                    <p className="text-sm font-semibold">
                                        {transaction.createdAt}
                                    </p>
                                </div>
                                <div className="flex flex-col gap-2 mt-5">
                                    <h4 className="font-medium text-xs text-[#666581]">Type</h4>
                                    <p className="text-sm font-semibold">
                                        {transaction.transactionType}
                                    </p>
                                </div>
                                <div className="flex flex-col gap-2 mt-5">
                                    <h4 className="font-medium text-xs text-[#666581]">Phone Number</h4>
                                    <p className="text-sm font-semibold">
                                        {transaction.customerPhone || transaction.user?.phoneNumber || "N/A"}
                                    </p>
                                </div>
                                <div className="flex flex-col gap-2 mt-5">
                                    <h4 className="font-medium text-xs text-[#666581]">Email</h4>
                                    <p className="text-sm font-semibold">
                                        {transaction.customerEmail || transaction.user?.email}
                                    </p>
                                </div>
                                <div className="flex flex-col gap-2 mt-5">
                                    <h4 className="font-medium text-xs text-[#666581]">Transaction Reference</h4>
                                    <p className="text-sm font-semibold w-full break-all">
                                        {transaction.transactionID || transaction.transactionHash || "N/A"}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default TransactionDetailsModal;
