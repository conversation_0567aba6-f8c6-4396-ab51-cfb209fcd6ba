"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Landmark } from "lucide-react";

import { Home, Wallet, Settings } from "lucide-react";

import clsx from "clsx";

export function MobileNavigationBar() {
  const pathname = usePathname();

  const navItems = [
    { href: "/dashboard", label: "Home", icon: <Home size={20} /> },
    {
      href: "/dashboard/treasury",
      label: "Treasury",
      icon: <Landmark size={20} />,
    },
    {
      href: "/dashboard/ql/settings",
      label: "Settings",
      icon: <Settings size={20} />,
    },
  ];

  return (
    <nav className="fixed font-circularStd max-w-[95%] rounded-full mx-auto bottom-[3%] left-0 right-0 bg-white border-t border-gray-200 z-50 shadow-md md:hidden">
      <div className="flex justify-between px-4 py-2">
        {navItems.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.href}
              href={item.href}
              className={clsx(
                "flex flex-col items-center flex-1 text-xs transition",
                isActive ? "text-purple-700" : "text-gray-500"
              )}
            >
              <div
                className={clsx(
                  "flex flex-col gap-1 items-center justify-center h-14 rounded-full px-6",
                  isActive && "bg-purple-100"
                )}
              >
                {item.icon}
                {item.label}
              </div>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
