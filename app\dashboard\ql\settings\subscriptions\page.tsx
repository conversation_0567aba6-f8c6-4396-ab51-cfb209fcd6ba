"use client";
import Link from 'next/link'
import React from 'react'
import FlincapIcon from "@/assets/images/logo-no-text.svg";
import Image from 'next/image';
import useStore from '@/store';

function Preferences() {
  const { user } = useStore();
  return (
    <div className='md:px-6 px-4 py-6'>
      <div className="grid md:grid-cols-2 gap-5">
        <div className="border shadow-sm px-4 py-7 rounded-md">
          <div className='flex items-center gap-1.5'>
            <Image src={FlincapIcon} alt='flincap logo' className='w-7' />
            <h2>Wallet Infrastructure</h2>
          </div>

          <p className='font-light text-sm my-4 text-black'>Our wallet infrastructure enables you to set up your crypto exchange without hassles. You can also trade and swap with us.</p>

          <div className="flex justify-between items-center mt-4">
            <Link href={user?.walletService ? "#" : "https://wa.me/17257109345"} target='_blank' className={`bg-black text-white px-3.5 py-1.5 rounded-md shadown-sm border-appGrayText2 text-sm ${user?.walletService ? 'opacity-55 cursor-default' : ''}`}>{user?.walletService ? "Subscribed" : "Subscribe"}</Link>
            <Link href="#" className='border px-3.5 py-1.5 rounded-md shadow-sm text-sm text-appPurple'>View docs</Link>
          </div>
        </div>

        <div className="border shadow-sm px-4 py-7 rounded-md">
          <div className='flex items-center gap-1.5'>
            <Image src={FlincapIcon} alt='flincap logo' className='w-7' />
            <h2>Payout and Disbursement</h2>
          </div>

          <p className='font-light text-sm my-4 text-black'>Making and receiving payments is easy with our payment APIs. You can enable fiat and crypto payments using our payment solutions.</p>

          <div className="flex justify-between items-center mt-4">
            <Link href={user?.payoutService ? "#" : "https://wa.me/17257109345"} target='_blank' className={`bg-black text-white px-3.5 py-1.5 rounded-md shadown-sm border-appGrayText2 text-sm ${user?.payoutService ? 'opacity-55 cursor-default' : ''}`}>{user?.payoutService ? "Subscribed" : "Subscribe"}</Link>
            <Link href="#" className='border px-3.5 py-1.5 rounded-md shadow-sm text-sm text-appPurple'>View docs</Link>
          </div>
        </div>

        <div className="border shadow-sm px-4 py-7 rounded-md">
          <div className='flex items-center gap-1.5'>
            <Image src={FlincapIcon} alt='flincap logo' className='w-7' />
            <h2>Credit</h2>
          </div>

          <p className='font-light text-sm my-4 text-black'>We provide loans to help you keep your business afloat and running. You can take advantage of more opportunities in the market with our credit facilities.</p>

          <div className="flex justify-between items-center mt-4">
            <Link href={"/dashboard/products/credit"} className={`bg-black text-white px-3.5 py-1.5 rounded-md shadown-sm border-appGrayText2 text-sm`}>Apply</Link>
          </div>
        </div>
    
      </div>
    </div >
  )
}

export default Preferences