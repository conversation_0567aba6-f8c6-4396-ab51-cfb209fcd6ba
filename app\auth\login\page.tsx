"use client";
import { <PERSON>, EyeOff } from "lucide-react";
import { CONSTANTS, getToken } from "@/app/utils";
import LoadingSpinner from "@/components/LoadingSpinner";
import axios from "axios";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import OtpInput from "react-otp-input";
import React, { FormEvent, useEffect, useState } from "react";
import { InformationCircleIcon } from "@/components/icons";
import { ToastContainer } from "react-toastify";
import { toast } from "sonner";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import useStore from "@/store";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "@/components/ui/input-otp";

const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().nonempty("Password is required"),
});

type LoginSchemaType = z.infer<typeof LoginSchema>;

interface LoginData {
  message: string;
  statusCode: number;
  success: boolean;
  data: { accessToken: string; active_2fa: boolean };
}

function Page() {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const searchParams = useSearchParams();
  const [errorMessage, setErrorMessage] = useState("");
  const [otp, setOtp] = useState("");
  const [screenWidth, setScreenWidth] = useState(1000);
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState("");

  const [showOtpDialog, setShowOtpDialog] = useState(false);

  const [loginData, setLoginData] = useState<LoginData>();

  const { setTwoFaStatus } = useStore();

  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<LoginSchemaType>({
    resolver: zodResolver(LoginSchema),
  });

  const submitUserLoginDetails = async (data: LoginSchemaType) => {
    const { email: submittedEmail, password } = data;
    setEmail(submittedEmail);
    try {
      setLoading(true);
      setErrorMessage("");

      const uri = searchParams.get("redirect_uri");
      const res = await axios.post(`${CONSTANTS.SERVER_URL}/v1/auth/signin`, {
        email: submittedEmail,
        password: password,
        redirectURL: uri || `${location.origin}/dashboard`,
      });
      console.log(res);
      const accessTokenData = res.data.data;
      setLoginData(res.data);

      if (res.data.data.active_2fa) {
        setShowOtpDialog(true);
        setTwoFaStatus(true);
      } else if (
        "accessToken" in accessTokenData &&
        !res.data.data.active_2fa
      ) {
        localStorage.setItem("tid", accessTokenData.accessToken);
        window.location.href = uri || `${location.origin}/dashboard`;
        return;
      }
      if ("password_setup" in accessTokenData) {
        setStep(2);
        return;
      }
      if ("check_otp" in accessTokenData) {
        setStep(3);
        return;
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.log(error.response?.data.message);
        toast.error(error.response?.data.message);
        setErrorMessage(error.response?.data.message || "Failed to sign in");
      } else {
        setErrorMessage("An unexpected error occurred");
      }
      console.error("Login error:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const resizeHandler = () => {
      setScreenWidth(window.innerWidth);
    };
    window.addEventListener("resize", resizeHandler);

    return () => {
      window.removeEventListener("resize", resizeHandler);
    };
  }, []);

  const verifyOTP = async (e: FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setErrorMessage("");
      const res = await axios.post(
        CONSTANTS.SERVER_URL + "/v1/auth/verify-login",
        {
          auth_token: otp,
        }
      );
      if (res.status.toString().startsWith("2")) {
        localStorage.setItem("tid", res.data.data.accessToken);
        const uri = searchParams.get("redirect_uri");
        window.location.href = uri ? uri : location.origin + "/dashboard";
      } else {
        setErrorMessage("Login failed");
      }
    } catch (err) {
      console.log(err);
      setErrorMessage("Login failed");
    } finally {
      setLoading(false);
    }
  };

  function InputOtpDialog() {
    const [value, setValue] = React.useState("");
    const [verifying, setVerifying] = React.useState(false);

    async function handleSubmit() {
      if (!loginData?.data?.accessToken) {
        setErrorMessage("Missing login data. Please try logging in again.");
        return;
      }
      try {
        setVerifying(true);
        const res = await axios.post(
          `${CONSTANTS.SERVER_URL}/v1/auth/verify-2fa`,
          {
            otp: value,
          },
          {
            headers: {
              Authorization: "Bearer " + loginData.data.accessToken,
            },
          }
        );
        if (res.status.toString().startsWith("2")) {
          toast.success("Login successful");
          localStorage.setItem("tid", loginData.data.accessToken ?? "");
          window.location.href = `${location.origin}/dashboard`;
        } else {
          setErrorMessage("Login failed");
        }
      } catch (err) {
        if (axios.isAxiosError(err) && err.response?.status === 400) {
          toast.error(err.response?.data?.message);
        }
      } finally {
        setVerifying(false);
      }
    }
    return (
      <Dialog open={showOtpDialog} onOpenChange={setShowOtpDialog}>
        <DialogContent className="max-w-md p-6 rounded-lg">
          <DialogHeader>
            <DialogTitle className="text-center text-2xl font-semibold text-[#0A2540] mb-2">
              Verify your account
            </DialogTitle>
            <DialogDescription className="flex flex-col items-center gap-4">
              <p className="text-appGrayText text-center text-base mb-2">
                Enter the 6 digit code from your authenticator app to continue.
              </p>
              <InputOTP
                maxLength={6}
                value={value}
                onChange={(value) => setValue(value)}
                className="flex justify-center gap-2"
              >
                <InputOTPGroup>
                  <InputOTPSlot
                    index={0}
                    className="border rounded-md w-10 h-12 text-center text-lg font-bold"
                  />
                  <InputOTPSlot
                    index={1}
                    className="border rounded-md w-10 h-12 text-center text-lg font-bold"
                  />
                  <InputOTPSlot
                    index={2}
                    className="border rounded-md w-10 h-12 text-center text-lg font-bold"
                  />
                  <InputOTPSlot
                    index={3}
                    className="border rounded-md w-10 h-12 text-center text-lg font-bold"
                  />
                  <InputOTPSlot
                    index={4}
                    className="border rounded-md w-10 h-12 text-center text-lg font-bold"
                  />
                  <InputOTPSlot
                    index={5}
                    className="border rounded-md w-10 h-12 text-center text-lg font-bold"
                  />
                </InputOTPGroup>
              </InputOTP>
              <button
                onClick={handleSubmit}
                className="bg-appPurple text-white rounded-md py-2.5 w-full block mt-2 text-sm font-semibold hover:bg-appPurpleDark transition-colors flex items-center justify-center"
                style={{ maxWidth: 240 }}
                disabled={verifying}
              >
                {verifying ? <LoadingSpinner className="w-5 h-5 mr-2" /> : null}
                {verifying ? "Verifying..." : "Verify"}
              </button>
              {errorMessage && (
                <p className="text-xs text-red-500 text-center mt-2">
                  {errorMessage}
                </p>
              )}
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <>
      <ToastContainer />
      <div className="flex items-center justify-center px-4 pt-10 pb-4 md:py-4 md:h-[550px] w-full">
        {
          {
            1: (
              <>
                <form onSubmit={handleSubmit(submitUserLoginDetails)}>
                  <h1 className="text-3xl font-semibold">Login to Flincap</h1>
                  <p className="text-appGrayText mt-2">
                    Kindly fill the form with correct details
                  </p>
                  <div
                    className={`border-[1px] rounded py-1 px-2 my-1 ${
                      errors.email ? "border-red-500" : "border-[#cacaca]"
                    }`}
                  >
                    <label htmlFor="email" className="text-xs text-[#999999]">
                      Email Address
                    </label>
                    <input
                      id="email"
                      {...register("email")}
                      className="w-full bg-transparent outline-none"
                    />
                  </div>
                  {errors.email && (
                    <p className="text-red-500 text-sm">
                      {errors.email.message}
                    </p>
                  )}
                  <div
                    className={`border-[1px] rounded py-1 px-2 my-1 ${
                      errors.password ? "border-red-500" : "border-[#cacaca]"
                    }`}
                  >
                    <label
                      htmlFor="password"
                      className="text-xs text-[#999999]"
                    >
                      Password
                    </label>
                    <div className="relative">
                      <input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        {...register("password")}
                        className="w-full bg-transparent outline-none"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-2 -translate-y-1/2 top-1/2"
                      >
                        {showPassword ? (
                          <EyeOff size={16} />
                        ) : (
                          <Eye size={16} />
                        )}
                      </button>
                    </div>
                  </div>
                  {errors.password && (
                    <p className="text-red-500 text-sm">
                      {errors.password.message}
                    </p>
                  )}

                  <Link
                    href={"/auth/forgot-password"}
                    className="text-xs text-appPurple"
                  >
                    Reset Password
                  </Link>

                  <button
                    type="submit"
                    className="bg-appPurple text-white rounded-md py-2.5 w-full block mt-2 text-sm"
                  >
                    {!loading ? (
                      "Proceed"
                    ) : (
                      <LoadingSpinner className="mx-auto" />
                    )}
                  </button>
                  <div className="flex gap-1 items-center mt-3">
                    <InformationCircleIcon className="w-4 stroke-[#000000]" />
                    <Link
                      href={"https://calendly.com/flincap/call"}
                      className="text-sm font-semibold"
                      target="_blank"
                    >
                      Kindly click on this link to book a call if your account
                      is not active
                    </Link>
                  </div>
                </form>
              </>
            ),
            2: (
              <div className="grow maw-w-md text-center">
                <Image
                  src={"/images/message-sent.png"}
                  width={80}
                  height={80}
                  className="mx-auto mb-3"
                  alt="."
                />
                <h1 className="text-lg md:text-3xl text-[#0A2540] font-semibold">
                  Setup a new password
                </h1>
                <p className="text-[#424242] text-sm md:text-base mt-3">
                  A password setup link has been sent to{" "}
                  <span className="text-appPurple">{email}</span>. Click the{" "}
                  <br /> link to setup your password and login.
                </p>
                <div className="mt-8 flex flex-col items-center gap-4">
                  <Link
                    href={`mailto:${email}`}
                    className="bg-appPurple text-white py-2 px-3 rounded-md"
                  >
                    Open mail app
                  </Link>
                  <button
                    type="button"
                    onClick={() => setStep(1)}
                    className="text-appPurple py-2 rounded-md w-full"
                  >
                    Resend link
                  </button>
                </div>
              </div>
            ),
            3: (
              <form onSubmit={verifyOTP} className="grow max-w-md">
                <h1 className="text-3xl font-semibold">Verify your account</h1>
                <p className="text-appGrayText mt-2">
                  Enter the 6 digit PIN sent to your email to access your
                  account.
                </p>

                <OtpInput
                  value={otp}
                  onChange={setOtp}
                  numInputs={6}
                  // renderSeparator={<span>-</span>}
                  containerStyle={{
                    gap: screenWidth > 500 ? "1.8rem" : "0.8rem",
                    marginTop: "1rem",
                    marginBottom: "1rem",
                  }}
                  renderInput={(props) => (
                    <input
                      {...props}
                      style={{ width: screenWidth > 500 ? "50px" : "40px" }}
                      className="border rounded outline-none text-center py-1.5"
                    />
                  )}
                />

                <p className="text-xs text-red-500">{errorMessage}</p>
                <p>
                  Can&apos;t access your email?{" "}
                  <Link href={"/#contact"} className="text-xs text-appPurple">
                    Contact us
                  </Link>
                </p>

                <button className="bg-appPurple text-white rounded-md py-2.5 w-full block mt-2 text-sm">
                  {!loading ? (
                    "Proceed"
                  ) : (
                    <LoadingSpinner className="mx-auto" />
                  )}
                </button>
              </form>
            ),
          }[step]
        }
      </div>
      {loginData?.data?.accessToken && <InputOtpDialog />}
    </>
  );
}

export default Page;
