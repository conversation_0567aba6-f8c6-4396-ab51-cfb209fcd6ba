"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import creditIcon from "@/assets/images/creditIcon.svg";
import depositIcon from "@/assets/images/depositIcon.svg";
import { UpDownIcon } from "@/components/icons";
import { api } from "@/app/utils";
import NotransactionIcon from "@/assets/images/no-data.svg";
import { jsPDF } from "jspdf";
import TransactionDetailsModal from "@/components/modals/TransactionDetails";
import useStore from "@/store";
import html2canvas from "html2canvas";
import { FileDown, ImageDown } from "lucide-react";
import { exportReceiptAsImage, generatePDF } from "@/lib/utils";
import RecentTransactionsTable from "@/components/dashboard/RecentTransactionsTable";
import { useDashboardData } from "@/app/dashboard/layout";

function Page() {
  const { bankData } = useDashboardData();
  const [transactions, setTransactions] = useState([]);

  useEffect(() => {
    if (typeof window != "undefined") {
      api
        .get("/v1/wallets/transactions")
        .then((res) => {
          setTransactions(res.data.data);
        })
        .catch((err) => {
          console.log(err);
        });
    }
    console.log(transactions);
  }, [transactions]);

  return (
    <div className="md:px-6 px-4 overflow-y-visible">
      {/* <article className="mt-8">
        <div className="flex flex-wrap justify-between gap-6 py-4 max-w-[25em] lg:max-w-[1250px] h-auto overflow-x-auto">
          <div className="overflow-y-auto max-h-[600px] w-full">
            <table className="w-full">
              <thead className="text-[14px] text-ash rounded-lg bg-ash1 border-b border-b-ash2">
                <tr className="text-left">
                  <th className="p-3 font-normal flex items-center gap-2">
                    Type <UpDownIcon className="w-3 h-3" />
                  </th>
                  <th className="p-3 font-normal">Amount</th>
                  <th className="p-3 font-normal">Trans ID</th>
                  <th className="p-3 font-normal">From/To</th>
                  <th className="p-3 font-normal">Status</th>
                  <th className="p-3 font-normal flex items-center gap-2">
                    Date <UpDownIcon className="w-3 h-3" />
                  </th>
                  <th className="p-3 font-normal">Receipt</th>
                </tr>
              </thead>
              <tbody>
                {transactions.length
                  ? transactions.map((transaction) => {
                      return (
                        <tr
                          className="text-xs text-black hover:text-appPurpleDark border-b border-b-ash2 cursor-pointer hover:bg-appWhite"
                          key={transaction.id}
                        >
                          <td className="p-3 flex items-center gap-2 whitespace-nowrap">
                            {transaction.transactionType === "DEPOSIT" ? (
                              <div className="rounded-full w-8 h-8 bg-[#ede2ff] p-[6px]">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="21"
                                  height="21"
                                  viewBox="0 0 21 21"
                                  fill="none"
                                  className="rotate-180"
                                >
                                  <path
                                    d="M16.1392 10.3609L11.6392 5.86094C11.5536 5.77901 11.4527 5.71478 11.3422 5.67194C11.1231 5.58193 10.8773 5.58193 10.6582 5.67194C10.5477 5.71478 10.4468 5.77901 10.3612 5.86094L5.86118 10.3609C5.77726 10.4449 5.7107 10.5445 5.66529 10.6541C5.61987 10.7638 5.5965 10.8813 5.5965 10.9999C5.5965 11.2396 5.69171 11.4695 5.86118 11.6389C6.03065 11.8084 6.26051 11.9036 6.50018 11.9036C6.73985 11.9036 6.96971 11.8084 7.13918 11.6389L10.1002 8.66894V15.4999C10.1002 15.7386 10.195 15.9676 10.3638 16.1363C10.5326 16.3051 10.7615 16.3999 11.0002 16.3999C11.2389 16.3999 11.4678 16.3051 11.6366 16.1363C11.8054 15.9676 11.9002 15.7386 11.9002 15.4999V8.66894L14.8612 11.6389C14.9448 11.7233 15.0444 11.7903 15.1541 11.8359C15.2637 11.8816 15.3814 11.9052 15.5002 11.9052C15.619 11.9052 15.7366 11.8816 15.8463 11.8359C15.956 11.7903 16.0555 11.7233 16.1392 11.6389C16.2235 11.5553 16.2905 11.4557 16.3362 11.3461C16.3819 11.2364 16.4054 11.1188 16.4054 10.9999C16.4054 10.8811 16.3819 10.7635 16.3362 10.6538C16.2905 10.5442 16.2235 10.4446 16.1392 10.3609Z"
                                    fill="#7928FF"
                                  />
                                </svg>
                              </div>
                            ) : (
                              <div className="rounded-full w-8 h-8 bg-[#ede2ff] p-[5px]">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="21"
                                  height="21"
                                  viewBox="0 0 21 21"
                                  fill="none"
                                >
                                  <path
                                    d="M16.1392 10.3609L11.6392 5.86094C11.5536 5.77901 11.4527 5.71478 11.3422 5.67194C11.1231 5.58193 10.8773 5.58193 10.6582 5.67194C10.5477 5.71478 10.4468 5.77901 10.3612 5.86094L5.86118 10.3609C5.77726 10.4449 5.7107 10.5445 5.66529 10.6541C5.61987 10.7638 5.5965 10.8813 5.5965 10.9999C5.5965 11.2396 5.69171 11.4695 5.86118 11.6389C6.03065 11.8084 6.26051 11.9036 6.50018 11.9036C6.73985 11.9036 6.96971 11.8084 7.13918 11.6389L10.1002 8.66894V15.4999C10.1002 15.7386 10.195 15.9676 10.3638 16.1363C10.5326 16.3051 10.7615 16.3999 11.0002 16.3999C11.2389 16.3999 11.4678 16.3051 11.6366 16.1363C11.8054 15.9676 11.9002 15.7386 11.9002 15.4999V8.66894L14.8612 11.6389C14.9448 11.7233 15.0444 11.7903 15.1541 11.8359C15.2637 11.8816 15.3814 11.9052 15.5002 11.9052C15.619 11.9052 15.7366 11.8816 15.8463 11.8359C15.956 11.7903 16.0555 11.7233 16.1392 11.6389C16.2235 11.5553 16.2905 11.4557 16.3362 11.3461C16.3819 11.2364 16.4054 11.1188 16.4054 10.9999C16.4054 10.8811 16.3819 10.7635 16.3362 10.6538C16.2905 10.5442 16.2235 10.4446 16.1392 10.3609Z"
                                    fill="#7928FF"
                                  />
                                </svg>
                              </div>
                            )}
                            <span className="font-semibold capitalize">
                              {transaction.transactionType.toLowerCase()}
                            </span>
                          </td>

                          <td className="p-3 font-semibold whitespace-nowrap">
                            NGN
                            {transaction.transactionType === "DEPOSIT" &&
                            transaction.transactionHash
                              ? Number(
                                  transaction.fiatAmount / 100 || 0
                                ).toLocaleString()
                              : Number(
                                  transaction.fiatAmount || 0
                                ).toLocaleString()}
                          </td>
                          <td className="p-3 whitespace-nowrap">
                            {transaction.transactionID?.slice(0, 20)}...
                          </td>
                          {transaction.transactionType === "DEPOSIT" ? (
                            <td className="p-3 whitespace-nowrap">
                              <p>{transaction.fromAccName}</p>
                              <p className="text-xs font-light mt-1">
                                {transaction.fromAccNum}{" "}
                                {transaction.fromBankName}
                              </p>
                            </td>
                          ) : (
                            <td className="p-3 whitespace-nowrap">
                              <p>{transaction.toAccName}</p>
                              <p className="text-xs font-light mt-1">
                                {transaction.toAccNum} {transaction.toBankName}
                              </p>
                            </td>
                          )}
                          <td className="p-3 text-[#13BF62] font-semibold whitespace-nowrap">
                            {transaction.done ? (
                              transaction.transactionType === "DEPOSIT" ? (
                                <div className="flex gap-1 items-center text-[#13BF62]">
                                  <span className="bg-[#13BF62] w-1 h-1"></span>
                                  <span>Received</span>
                                </div>
                              ) : (
                                <div className="flex gap-1 items-center text-[#13BF62]">
                                  <span className="bg-[#13BF62] w-1 h-1"></span>
                                  <span>Completed</span>
                                </div>
                              )
                            ) : transaction.status === "FAILED" ? (
                              <div className="flex gap-1 items-center text-red-500">
                                <span className="bg-red-500 w-1 h-1"></span>
                                <span>Failed</span>
                              </div>
                            ) : (
                              <div className="flex gap-1 items-center text-red-500">
                                <span className="bg-yellow-500 w-1 h-1"></span>
                                <span>Processing</span>
                              </div>
                            )}
                          </td>
                          <td className="p-3 font-semibold whitespace-nowrap">
                            {new Date(transaction.createdAt).toLocaleString(
                              undefined,
                              {
                                day: "2-digit",
                                month: "short",
                                hour: "2-digit",
                                minute: "2-digit",
                              }
                            )}{" "}
                          </td>
                          <td className="items-right p-3">
                            {transaction.transactionType === "WITHDRAW" &&
                            transaction.done ? (
                              <>
                                <div className="flex gap-1">
                                  <FileDown
                                    onClick={() => generatePDF(transaction)}
                                    className="w-4 h-4 text-appPurple hover:text-appPurpleDark"
                                  />
                                  <ImageDown
                                    onClick={() =>
                                      exportReceiptAsImage(transaction)
                                    }
                                    className="w-4 h-4 text-appPurple hover:text-appPurpleDark"
                                  />
                                </div>
                              </>
                            ) : null}
                          </td>
                        </tr>
                      );
                    })
                  : null}
              </tbody>
            </table>
            {!transactions.length ? (
              <div className="text-center flex flex-col justify-center items-center w-full min-h-80">
                <Image
                  src={NotransactionIcon}
                  alt="."
                  className="block mx-auto grayscale"
                />
                <p className="my-2.5 text-black">No transactions</p>
                <p className="font-light">
                  Once you start trading transactions, they <br /> will appear
                  here. <span className="text-appPurple">Start now</span>
                </p>
              </div>
            ) : null}
          </div>
        </div>
      </article> */}
      <RecentTransactionsTable
        transactions={transactions}
        fiatBankData={bankData}
        exportReceiptAsImage={exportReceiptAsImage}
        generatePDF={generatePDF}
      />
    </div>
  );
}

export default Page;
