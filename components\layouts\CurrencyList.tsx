import useStore from "@/store";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ttribut<PERSON>, SetStateAction } from "react";
import {
  BitcoinIcon,
  DollarIcon,
  EthereumIcon,
  NairaIcon,
  TronIcon,
  USDTIcon,
  XMarkIcon,
} from "../icons";
import CryptoIcon from "../CryptoIcon";
import { iWalletSymbol } from "@/types/utils";
import { getTokenName } from "@/app/utils";

export default function CurrencyList({
  onSetCurrency,
  assets,
}: {
  onSetCurrency: Dispatch<
    SetStateAction<string>
  >;
  assets: string[]
}) {
  const { setModal } = useStore();
  return (
    <div className="bg-white rounded-xl min-w-80 h-min overflow-clip">
      <header className="flex items-center justify-between bg-[#fafafa] px-6 py-4 border-b border-zinc-300">
        <h3 className="font-medium mb-2">Select Currency</h3>
        <button onClick={() => setModal(null)}>
          <XMarkIcon className="w-6 h-6" />
        </button>
      </header>
      <ul className="flex flex-col divide-y last:border-none">
        {assets.map((option, idx) => (
          <li key={idx}>
            <button
              onClick={() => {
                onSetCurrency(option);
                setModal(null);
              }}
              className="flex items-center gap-x-4 px-4 py-4 w-full font-medium text-zinc-700 hover:bg-zinc-100"
            >
              <CryptoIcon assetType={option as iWalletSymbol} />
              {getTokenName(option).symbol}
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}
