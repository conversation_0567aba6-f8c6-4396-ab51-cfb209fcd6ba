"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";

const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON> O<PERSON>",
    role: "CEO, Circlefunds ",
    text: "“<PERSON>lincap has been my reliable go-to for fast and easy stablecoin exchange, my experience has been amazing!”",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "<PERSON><PERSON><PERSON>",
    text: "“Flincap’s robust and user-friendly infrastructure provided the perfect foundation for launching my crypto-to-fiat platform.”",
  },
];

export default function Testimonials() {
  const [slide, setSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setSlide((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="max-w-sm">
      {testimonials.map(
        (testimonial) =>
          testimonial.id === slide + 1 && (
            <div key={testimonial.id} className="text-white">
              <p className="max-w-[300px]">{testimonial.text}</p>
              <div className="mt-5 flex items-center gap-2">
                <div className="w-12 h-12 bg-white rounded-full overflow-hidden flex items-center justify-center text-appPurple font-bold text-lg">
                  {testimonial.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </div>
                <div className="text-sm">
                  <h3>{testimonial.name}</h3>
                  <p>{testimonial.role}</p>
                </div>
              </div>
            </div>
          )
      )}
      <div className="mt-5 flex gap-2">
        <span
          className={`${
            slide == 0 ? "bg-white" : "bg-[#7F31FF]"
          } w-2 h-2 rounded-full`}
        />
        <span
          className={`${
            slide == 1 ? "bg-white" : "bg-[#7F31FF]"
          } w-2 h-2 rounded-full`}
        />
      </div>
    </div>
  );
}
