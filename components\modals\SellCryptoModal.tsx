import useStore from '@/store'
import React, { useState } from 'react'
import { ChevronLeftIcon, InformationCircleIcon, SendIcon, TagIcon } from '../icons';
import QRCode from "react-qr-code";

function SellCryptoModal({ wallets }: { wallets: Record<string, any>[] }) {
  const { setModal } = useStore();

  return (
    <div className='rounded-md h-fit max-w-80 md:max-w-96 grow border bg-white pb-5'>
      <div className='bg-[#fcfcfc] rounded-tl-md rounded-tr-md p-4'>
        <button onClick={() => setModal(null)} className="flex items-center">
          <ChevronLeftIcon className="w-4 h-4 stroke-[2px]" />
          <p className='text-sm font-semibold'>Go Back</p>
        </button>
      </div>

      <div className='bg-white p-4 rounded-bl-md rounded-br-md'>
        <p className="text-center border-b max-w-fit px-10 mx-auto py-1.5 text-sm">Sell</p>
      </div>

      <div className='flex flex-col gap-3 items-center justify-center mt-3'>
        <div className='min-w-[260px]'>
          <p className='text-center'>Not available yet</p>
          {/* <div>
            <p className="text-xs ">You Buy</p>
          </div> */}
          {/* <Image src={{
            NGN: NairaIcon,
            USD: DollarIcon,
            BTC: BitcoinIcon,
            ETH: EthereumIcon,
            TRX: TronIcon,
            LTC: LitecoinIcon,
            BCH: BitcoinCash,
          }[wallet?.symbol as iWalletSymbol]} alt="wallet icon" className="w-7 h-7" /> */}
        </div>

        {/* <button onClick={() => { }} className='bg-appPurple text-white rounded py-2 px-4 text-sm min-w-[260px] opacity-65' disabled>Continue</button> */}
      </div >
    </div>
  )
}

export default SellCryptoModal;