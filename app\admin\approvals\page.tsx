"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import creditIcon from "@/assets/images/creditIcon.svg";
import NotransactionIcon from "@/assets/images/no-data.svg";
import { UpDownIcon } from "@/components/icons";
import { adminApi, getTokenName } from "@/app/utils";
import moment from "moment";
import LoadingSpinner from "@/components/LoadingSpinner";

function Page() {
  const [transactions, setTransactions] = useState([]);
  const [loadingMarkAsCompleted, setLoadingMarkAsCompleted] = useState("");
  const [loadingDelete, setLoadingDelete] = useState("");

  useEffect(() => {
    adminApi
      .get(`/v1/admin/transactions?done=${false}`)
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          setTransactions(res.data.data);
        } else {
          console.log("Couldn't fetch transactions");
        }
      })
      .catch((err) => {
        console.log(err);
      });
  }, []);

  const updateTransaction = async (id: string) => {
    setLoadingMarkAsCompleted(id); // Set loading state for this specific transaction
    try {
      const res = await adminApi.patch(
        `/v1/admin/users/${id}/update-transaction`
      );
      if (res.status.toString().startsWith("2")) {
        window.location.reload(); // Reload page on success
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoadingMarkAsCompleted(""); // Reset loading state
    }
  };

  const deleteTransaction = async (id: string) => {
    setLoadingDelete(id); // Set loading state for this specific transaction
    try {
      const res = await adminApi.delete(
        `/v1/admin/users/${id}/delete-transaction`
      );
      if (res.status.toString().startsWith("2")) {
        window.location.reload(); // Reload page on success
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoadingDelete(""); // Reset loading state
    }
  };

  return (
    <section className="mt-4">
      <div className="cards grid md:grid-cols-3 gap-4 px-4 my-4">
        <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
          <h3 className="font-semibold">Transaction count</h3>
          <p className="text-[#999999] mt-1">
            Showing:{" "}
            <select className="border-none outline-none text-appPurple">
              <option defaultValue={"All"}>All</option>
            </select>
          </p>
          <h1 className="font-semibold text-xl mt-6">{transactions?.length}</h1>
        </div>
      </div>

      <div className="flex flex-wrap justify-between gap-6 py-4 px-4 h-auto overflow-x-auto">
        <table className="w-full">
          <thead className="text-[14px] text-ash rounded-lg bg-ash1 border-b border-b-ash2">
            <tr className="text-left">
              <th className="p-3 font-normal flex items-center gap-2">Type</th>
              <th className="p-3 font-normal">Coin</th>
              <th className="p-3 font-normal">Amount</th>
              <th>Rate</th>
              <th>Total</th>
              <th className="p-3 font-normal">Receiver</th>
              <th className="p-3 font-normal">Status</th>
              <th className="p-3 font-normal flex items-center gap-2">Date</th>
              <th className="p-3 font-normal">Action</th>
            </tr>
          </thead>
          <tbody className="w-full">
            {transactions?.length ? (
              transactions.map((transaction: any, idx: number) => (
                <tr
                  className="text-xs text-black hover:text-appPurpleDark border-b border-b-ash2 cursor-pointer hover:bg-appWhite"
                  key={idx}
                >
                  <td className="p-3 flex items-center gap-2 whitespace-nowrap">
                     {transaction.transactionType === "DEPOSIT" ? (
                        <div className="rounded-full w-8 h-8 bg-[#ede2ff] p-[6px]">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="21"
                            height="21"
                            viewBox="0 0 21 21"
                            fill="none"
                            className="rotate-180"
                          >
                            <path
                              d="M16.1392 10.3609L11.6392 5.86094C11.5536 5.77901 11.4527 5.71478 11.3422 5.67194C11.1231 5.58193 10.8773 5.58193 10.6582 5.67194C10.5477 5.71478 10.4468 5.77901 10.3612 5.86094L5.86118 10.3609C5.77726 10.4449 5.7107 10.5445 5.66529 10.6541C5.61987 10.7638 5.5965 10.8813 5.5965 10.9999C5.5965 11.2396 5.69171 11.4695 5.86118 11.6389C6.03065 11.8084 6.26051 11.9036 6.50018 11.9036C6.73985 11.9036 6.96971 11.8084 7.13918 11.6389L10.1002 8.66894V15.4999C10.1002 15.7386 10.195 15.9676 10.3638 16.1363C10.5326 16.3051 10.7615 16.3999 11.0002 16.3999C11.2389 16.3999 11.4678 16.3051 11.6366 16.1363C11.8054 15.9676 11.9002 15.7386 11.9002 15.4999V8.66894L14.8612 11.6389C14.9448 11.7233 15.0444 11.7903 15.1541 11.8359C15.2637 11.8816 15.3814 11.9052 15.5002 11.9052C15.619 11.9052 15.7366 11.8816 15.8463 11.8359C15.956 11.7903 16.0555 11.7233 16.1392 11.6389C16.2235 11.5553 16.2905 11.4557 16.3362 11.3461C16.3819 11.2364 16.4054 11.1188 16.4054 10.9999C16.4054 10.8811 16.3819 10.7635 16.3362 10.6538C16.2905 10.5442 16.2235 10.4446 16.1392 10.3609Z"
                              fill="#7928FF"
                            />
                          </svg>
                        </div>
                      ) : (
                        <div className="rounded-full w-8 h-8 bg-[#ede2ff] p-[5px]">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="21"
                            height="21"
                            viewBox="0 0 21 21"
                            fill="none"
                          >
                            <path
                              d="M16.1392 10.3609L11.6392 5.86094C11.5536 5.77901 11.4527 5.71478 11.3422 5.67194C11.1231 5.58193 10.8773 5.58193 10.6582 5.67194C10.5477 5.71478 10.4468 5.77901 10.3612 5.86094L5.86118 10.3609C5.77726 10.4449 5.7107 10.5445 5.66529 10.6541C5.61987 10.7638 5.5965 10.8813 5.5965 10.9999C5.5965 11.2396 5.69171 11.4695 5.86118 11.6389C6.03065 11.8084 6.26051 11.9036 6.50018 11.9036C6.73985 11.9036 6.96971 11.8084 7.13918 11.6389L10.1002 8.66894V15.4999C10.1002 15.7386 10.195 15.9676 10.3638 16.1363C10.5326 16.3051 10.7615 16.3999 11.0002 16.3999C11.2389 16.3999 11.4678 16.3051 11.6366 16.1363C11.8054 15.9676 11.9002 15.7386 11.9002 15.4999V8.66894L14.8612 11.6389C14.9448 11.7233 15.0444 11.7903 15.1541 11.8359C15.2637 11.8816 15.3814 11.9052 15.5002 11.9052C15.619 11.9052 15.7366 11.8816 15.8463 11.8359C15.956 11.7903 16.0555 11.7233 16.1392 11.6389C16.2235 11.5553 16.2905 11.4557 16.3362 11.3461C16.3819 11.2364 16.4054 11.1188 16.4054 10.9999C16.4054 10.8811 16.3819 10.7635 16.3362 10.6538C16.2905 10.5442 16.2235 10.4446 16.1392 10.3609Z"
                              fill="#7928FF"
                            />
                          </svg>
                        </div>
                      )}
                      <span className="font-semibold capitalize">
                        {transaction.transactionType.toLowerCase() === "payout"
                          ? "Exchange"
                          : transaction.transactionType.toLowerCase()}
                      </span>
                    </td>
                  <td className="p-3 font-semibold whitespace-nowrap">
                    {transaction.cryptoWalletId
                      ? getTokenName(transaction.cryptoWallet?.symbol).symbol
                      : transaction.fiatWallet?.symbol ?? "NGN"}
                  </td>

                  <td>
                    {transaction.cryptoWalletId
                      ? transaction.cryptoWallet?.symbol == "BTC"
                        ? transaction.cryptoAmount
                        : transaction.cryptoAmount.toLocaleString()
                      : transaction.transactionType === "DEPOSIT" &&
                        transaction.transactionHash
                      ? Number(
                          transaction.fiatAmount / 100 || 0
                        ).toLocaleString()
                      : Number(transaction.fiatAmount || 0).toLocaleString()}
                  </td>
                  <td>{Number(transaction.conversionRate).toLocaleString()}</td>

                  <td>
                    {(
                      Number(transaction.conversionRate) *
                      transaction.cryptoAmount
                    ).toLocaleString()}
                  </td>

                  <td className="p-3 whitespace-nowrap">
                    {transaction.transactionType === "PAYOUT" ? (
                      <>
                        <p>{transaction.toAccName}</p>
                        <p className="text-xs font-light mt-1">
                          {transaction.toAccNum} {transaction.toBankName}
                        </p>
                      </>
                    ) : transaction.cryptoWalletId ? (
                      <>{transaction.wallet_address}</>
                    ) : transaction.transactionType === "DEPOSIT" ? (
                      <>
                        <p>{transaction.fromAccName}</p>
                        <p className="text-xs font-light mt-1">
                          {transaction.fromAccNum} {transaction.fromBankName}
                        </p>
                      </>
                    ) : (
                      <>
                        <p>{transaction.toAccName}</p>
                        <p className="text-xs font-light mt-1">
                          {transaction.toAccNum} {transaction.toBankName}
                        </p>
                      </>
                    )}
                  </td>
                  <td className="p-3 whitespace-nowrap">
                    {transaction.transactionType === "DEPOSIT" ||
                    transaction.done ? (
                      <span className="text-green-500 font-semibold">
                        Completed
                      </span>
                    ) : (
                      <span className="text-red-500 font-semibold">
                        Pending
                      </span>
                    )}
                  </td>
                  <td className="p-3 font-semibold whitespace-nowrap">
                    {moment(transaction.createdAt).format(
                      "MMMM Do, YYYY, h:mma"
                    )}
                  </td>
                  <td className="p-3 whitespace-nowrap">
                    {transaction.transactionType === "DEPOSIT" ||
                    transaction.done ? null : (
                      <button
                        onClick={() => updateTransaction(transaction.id)}
                        className="mt-4 bg-appPurple text-white rounded py-2 w-full"
                        disabled={loadingMarkAsCompleted === transaction.id}
                      >
                        {!loadingMarkAsCompleted ||
                        loadingMarkAsCompleted !== transaction.id ? (
                          "Mark as Completed"
                        ) : (
                          <LoadingSpinner className="mx-auto" />
                        )}
                      </button>
                    )}
                  </td>
                  <td className="p-3 whitespace-nowrap">
                    {transaction.transactionType === "DEPOSIT" ||
                    transaction.done ? null : (
                      <button
                        onClick={() => deleteTransaction(transaction.id)}
                        className="mt-4 bg-red-500 text-white rounded py-2 w-full"
                        disabled={loadingDelete === transaction.id}
                      >
                        {!loadingDelete || loadingDelete !== transaction.id ? (
                          "Delete"
                        ) : (
                          <LoadingSpinner className="mx-auto" />
                        )}
                      </button>
                    )}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={9} className="text-center py-8">
                  <div className="flex flex-col justify-center items-center min-h-80 w-full">
                    <Image
                      src={NotransactionIcon}
                      alt="No transactions"
                      className="block mx-auto grayscale"
                    />
                    <p className="my-2.5 text-black">No transactions</p>
                    <p className="font-light">
                      Once you start trading transactions, they will appear
                      here. <span className="text-appPurple">Start now</span>
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </section>
  );
}

export default Page;
