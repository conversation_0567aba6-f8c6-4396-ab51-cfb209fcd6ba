import React from "react";
import { RightReveal } from "../animations/animations";
import { LeftReveal } from "../animations/animations";
import Image from "next/image";

export default function TradeSteps() {
  return (
    <section className="flex flex-col justify-center items-center py-5 bg-[#F0EFFF]">
      <div className="header text-center mt-10">
        <h3 className="capitalize text-[#0A2540] text-3xl font-bold leading-8">
          Trade Steps
        </h3>
        <p className="text-[#425466] text-lg leading-8">
          Your customers can trade with you in 4 simple steps!
        </p>
      </div>

      <div className="w-full">
        <LeftReveal delay={0.4}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-md md:max-w-2xl lg:max-w-4xl mx-auto py-8">
            <div className="flex items-center md:w-[600px] relative h-[400px]">
              <Image
                src="/images/illustrations/Exchange.svg"
                alt="Exchange"
                fill
                className="object-contain"
                priority
              />
            </div>
            <div className="flex flex-col justify-center md:ml-32 text-center sm:text-left">
              <h2 className="text-[#7928FF] font-bold text-2xl md:text-3xl">
                Set Exchange
              </h2>
              <p className="text-[#425466] text-lg md:text-xl mt-2">
                Set your exchange link, copy, <br /> and share with your users.
              </p>
            </div>
          </div>
        </LeftReveal>

        <div className="bg-[#F6F6F6] ">
          <RightReveal delay={0.4}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mx-auto max-w-md md:max-w-2xl lg:max-w-4xl py-8">
              <div className="flex flex-col justify-center md:h-screen md:ml-16 text-center sm:text-left">
                <h2 className="text-[#7928FF] font-bold text-2xl md:text-3xl">
                  Input Trade Details
                </h2>
                <p className="text-[#425466] text-lg md:text-xl mt-2">
                  Users visit your exchange and <br /> input trade details.
                </p>
              </div>
              <div className="flex items-center md:w-[600px] relative h-[400px]">
                <Image
                  src="/images/illustrations/Sell.svg"
                  alt="Sell"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </RightReveal>
        </div>

        <div className="bg-[#E8F9F0]">
          <LeftReveal delay={0.4}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-md md:max-w-2xl lg:max-w-4xl mx-auto py-8">
              <div className="flex items-center md:w-[600px] relative h-[400px]">
                <Image
                  src="/images/illustrations/Transafer.png"
                  alt="Transfer"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
              <div className="flex flex-col justify-center md:ml-32 text-center sm:text-left">
                <h2 className="text-[#7928FF] font-bold text-2xl md:text-3xl">
                  Trade Confirmation
                </h2>
                <p className="text-[#425466] text-lg md:text-xl mt-2">
                  Summary of trade is shown for <br /> confirmation.
                </p>
              </div>
            </div>
          </LeftReveal>
        </div>

        <RightReveal delay={0.4}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-md md:max-w-2xl lg:max-w-4xl mx-auto py-8">
            <div className="flex flex-col justify-center md:h-screen md:ml-16 text-center sm:text-left">
              <h2 className="text-[#7928FF] font-bold text-2xl md:text-3xl">
                Payment
              </h2>
              <p className="text-[#425466] text-lg md:text-xl mt-2">
                User sends the crypto <br /> and receives fiat.
              </p>
            </div>
            <div className="flex items-center md:w-[600px] relative h-[400px]">
              <Image
                src="/images/illustrations/Deposit.svg"
                alt="Deposit"
                fill
                className="object-contain"
                priority
              />
            </div>
          </div>
        </RightReveal>
      </div>
    </section>
  );
}
