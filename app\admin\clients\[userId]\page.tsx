"use client";
import { adminApi } from "@/app/utils";
import LoadingSpinner from "@/components/LoadingSpinner";
import { DatePicker } from "antd";
import Image from "next/image";
import React, { FormEvent, useEffect, useState } from "react";
import dayjs from "dayjs";

const isValidDate = (datestr: string) => !isNaN(Date.parse(datestr));

function UserPage({ params }: { params: { userId: string } }) {
  const [user, setUser] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [formDetails, setFormDetails] = useState({
    firstName: "",
    lastName: "",
    email: "",
    country: "",
    phone: "",
    bvn: "",
    dob: "",
    companyID: "",
    isActive: false,
    autoPayout: false,
  });
  const [kycData, setKycData] = useState<any>(null);

  const handleChange = (e: any) => {
    setFormDetails({ ...formDetails, [e.target.name]: e.target.value.trim() });
  };

  useEffect(() => {
    // Fetch user data
    setLoading(true);
    adminApi
      .get("/v1/admin/users/" + params.userId)
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          setUser(res.data.data);
          let dob = "";
          if (res.data.data.dob) {
            const [day, month, year] = res.data.data.dob.split("-");
            dob = month + "/" + day + "/" + year;
          }
          setFormDetails({
            firstName: res.data.data.fullName.split(" ")[0],
            lastName: res.data.data.fullName.split(" ")[1],
            email: res.data.data.email,
            country: res.data.data.country,
            phone: res.data.data.phone,
            bvn: res.data.data.bvn,
            dob: dob,
            isActive: res.data.data.isActive,
            autoPayout: res.data.data.autoPayout,
            companyID: res.data.data.companyID,
          });
          if (res.data.data.kyc) {
            setKycData(res.data.data.kyc);
          }
        } else {
          console.log("Couldn't fetch user");
        }
      })
      .catch((err) => {
        console.log(err);
        if (err.response.data) {
          alert("User BVN or other data may be invalid");
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params.userId]);

  const editUser = async (e: FormEvent) => {
    e.preventDefault();
    let dob = new Date(formDetails.dob)
      .toLocaleDateString("en-GB")
      .replaceAll("/", "-");
    // submit data
    adminApi
      .patch(`/v1/admin/users/${params.userId}/profile`, {
        fullName: formDetails.firstName + " " + formDetails.lastName,
        email: formDetails.email,
        country: formDetails.country,
        phone: formDetails.phone,
        bvn: formDetails.bvn,
        dob: dob,
        isActive: formDetails.isActive,
        companyID: formDetails.companyID,
      })
      .then((res) => {
        console.log(res);
        if (res?.status?.toString()?.startsWith("2")) {
          window.location.reload();
        } else {
          console.log("Sorry an error occured");
          setErrorMessage(res.data.message);
        }
      })
      .catch((err) => {
        if (err.response) {
          console.log(err.response);
          setErrorMessage(err.response.data.message);
        }
        console.log(err);
      });
  };

  return (
    <div className="py-6">
      <section className="mt-6 px-2 md:px-4">
        <Image
          src={
            user?.image
              ? user?.image
              : "https://ui-avatars.com/api/?name=" + user?.fullName
          }
          width={70}
          height={70}
          alt="user's profile photo"
          className="w-[70px] h-[70px] rounded-full"
        />

        <form
          onSubmit={editUser}
          className="mt-8 flex flex-col gap-4 max-w-[700px]"
        >
          <p className="text-sm text-red-500">{errorMessage}</p>
          <div className="grid grid-cols-2 gap-4">
            <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
              <label className="text-[#999999] text-xs font-light">
                First Name
              </label>
              <input
                name="firstName"
                value={formDetails?.firstName}
                onChange={handleChange}
                className="w-full bg-transparent outline-none text-sm"
              />
            </div>
            <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
              <label className="text-[#999999] text-xs font-light">
                Last Name
              </label>
              <input
                name="lastName"
                value={formDetails?.lastName}
                onChange={handleChange}
                className="w-full bg-transparent outline-none text-sm"
              />
            </div>
          </div>

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Contact Email
            </label>
            <input
              name="email"
              value={formDetails?.email}
              onChange={handleChange}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">Country</label>
            <input
              name="country"
              value={formDetails?.country}
              onChange={handleChange}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Contact Phone Number
            </label>
            <input
              name="phone"
              value={formDetails?.phone}
              onChange={handleChange}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Bank Verification Number
            </label>
            <input
              name="bvn"
              value={formDetails?.bvn}
              onChange={handleChange}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Date Of Birth
            </label>
            {formDetails.firstName.length ? (
              <DatePicker
                className="w-full outline-none border-none p-0"
                value={
                  isValidDate(formDetails.dob) ? dayjs(formDetails.dob) : null
                }
                onChange={(_date: any, datestring: any) => {
                  setFormDetails((prev) => ({ ...prev, dob: datestring }));
                }}
              />
            ) : null}
          </div>

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Exchange Username
            </label>
            <input
              name="companyID"
              placeholder="A unique username"
              onChange={handleChange}
              value={formDetails.companyID || ""}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>

          <div className="py-1 rounded flex gap-2">
            <label
              htmlFor="isActive"
              className="text-appGrayText2 text-sm font-light select-none"
            >
              Is Active
            </label>
            <input
              id="isActive"
              name="isActive"
              type="checkbox"
              checked={formDetails?.isActive}
              onChange={(e) =>
                setFormDetails((prev) => ({
                  ...prev,
                  isActive: e.target.checked,
                }))
              }
              className="accent-appPurple bg-transparent outline-none text-sm"
            />
          </div>

          <p>
            Auto payout is {formDetails.autoPayout ? "enabled" : "disabled"}
          </p>

          {kycData ? (
            <div className="mt-8 flex flex-col gap-4 max-w-[700px]">
              <h2 className="font-bold">KYC Information</h2>

              <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
                <label className="text-[#999999] text-xs font-light">
                  Identity Type
                </label>
                <input
                  name="identityType"
                  value={kycData.identityType}
                  disabled
                  className="w-full bg-transparent outline-none text-sm"
                />
              </div>

              <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
                <label className="text-[#999999] text-xs font-light">
                  Identity Number
                </label>
                <input
                  name="identityNumber"
                  value={kycData.identityNumber}
                  disabled
                  className="w-full bg-transparent outline-none text-sm"
                />
              </div>

              <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
                <label className="text-[#999999] text-xs font-light">
                  Identity Document Url
                </label>
                <input
                  name="identityDocument"
                  value={kycData.identityDocument}
                  disabled
                  className="w-full bg-transparent outline-none text-sm"
                />
              </div>

              <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
                <label className="text-[#999999] text-xs font-light">
                  Photo Url
                </label>
                <input
                  name="photo"
                  value={kycData.photo}
                  disabled
                  className="w-full bg-transparent outline-none text-sm"
                />
              </div>
            </div>
          ) : null}

          <button className="text-center px-4 py-2 bg-appPurple text-white rounded w-full">
            {!loading ? "Update" : <LoadingSpinner className="mx-auto" />}
          </button>
        </form>
      </section>
    </div>
  );
}

export default UserPage;
