"use client";
import Link from 'next/link'
import { usePathname } from 'next/navigation';
import React, { ReactNode } from 'react'

function Layout({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  return (
    <div>
      <nav className='bg-[#fcfcfc]'>
        <ul className='flex overflow-auto'>
          <li><Link href="/dashboard/ql/transactions" className={`md:px-6 px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${pathname === "/dashboard/ql/transactions" ? "border-b-[#71bbff] text-[#71bbff]" : ""}`}>Exchange</Link></li>
          <li><Link href="/dashboard/ql/transactions/fiat" className={`md:px-6 px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${pathname.startsWith("/dashboard/ql/transactions/fiat") ? "border-b-[#71bbff] text-[#71bbff]" : ""}`}>Fiat</Link></li>
          <li><Link href="/dashboard/ql/transactions/crypto" className={`md:px-6 px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${pathname.startsWith("/dashboard/ql/transactions/crypto") ? "border-b-[#71bbff] text-[#71bbff]" : ""}`}>Crypto</Link></li>
        </ul>
      </nav>

      <main>{children}</main>
    </div>
  )
}

export default Layout