"use client";

import React, { FormEvent, useState } from "react";
import Select from "react-select";
import { toast } from "react-toastify";
import { InlineWidget } from "react-calendly";
import COUNTRIES from "@/countries.json";
import LoadingSpinner from "@/components/LoadingSpinner";
import { CONSTANTS } from "@/app/utils";
import axios from "axios";
import { useRouter } from "next/navigation";
import { z } from "zod";

const UserSchema = z
  .object({
    firstName: z
      .string()
      .regex(
        /^[a-zA-Z]+$/,
        "First name must contain only alphabetic characters"
      ),
    lastName: z
      .string()
      .regex(
        /^[a-zA-Z]+$/,
        "Last name must contain only alphabetic characters"
      ),
    businessName: z.string().min(3),
    email: z.string().email(),
    phone: z.coerce
      .number()
      .nonnegative()
      .gt(7, { message: "Must be a phone number." })
      .transform((val) => String(val)),
    country: z.string().regex(/^[a-zA-Z]+$/, "Please select a country"),
    password: z.string().min(6),
  })
  .required();
interface FormErrors {
  firstName?: string;
  lastName?: string;
  businessName?: string;
  email?: string;
  phone?: string;
  country?: string;
  password?: string;
}
function Page() {
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [step, setStep] = useState(1);
  const router = useRouter();
  const [formDetails, setFormDetails] = useState({
    firstName: "",
    lastName: "",
    businessName: "",
    email: "",
    phone: "",
    products: [],
    country: "",
    password: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === "businessName") {
      const letterOnlyRegex = /^[A-Za-z]*$/;

      if (!letterOnlyRegex.test(value)) {
        setErrors({
          ...errors,
          businessName: "Business name can only contain letters.",
        });
        return;
      } else {
        setErrors({
          ...errors,
          businessName: "",
        });
      }
    }

    setFormDetails({ ...formDetails, [name]: value });
  };

  const next = () => {
    if (step === 5) {
      return;
    }
    setStep(step + 1);
  };

  const showScheduleCall = async (e: FormEvent) => {
    e.preventDefault();
    const parsedUser = UserSchema.safeParse(formDetails);
    console.log(parsedUser);
    if (!parsedUser.success) {
      const errors = parsedUser.error.flatten().fieldErrors;
      setErrors(errors as FormErrors);
      console.log(errors);
    }

    if (
      // formDetails.firstName &&
      // formDetails.lastName &&
      // formDetails.businessName &&
      // formDetails.email &&
      // formDetails.phone &&
      // formDetails.country &&
      // formDetails.password.length >= 6
      parsedUser.success
    ) {
      // submit data to admin
      console.log("Data submitted");
      setErrors({});
      const payload = {
        fullName: formDetails.firstName + " " + formDetails.lastName,
        email: formDetails.email,
        country: formDetails.country,
        phone: formDetails.phone,
        businessName: formDetails.businessName,
        password: formDetails.password,
      };

      try {
        setLoading(true);
        const res = await axios.post(
          CONSTANTS.SERVER_URL + "/v1/auth/signup",
          payload
        );
        // next();
        const status = res.status.toString();
        if (status.startsWith("2")) {
          toast.success("Signup Successful");
          setTimeout(() => {
            router.push("/auth/verify-email");
          }, 2500);
        } else if (status.startsWith("4")) {
          toast.info(res.data.message);
        }
      } catch (err: any) {
        if (err.response) {
          err.response.status.toString().startsWith("4") &&
            toast.info(err.response.data.message);
          toast.error(
            "Oops! Something went wrong with your signup. Please try again later. If the issue persists, feel free to contact our support team for assistance.",
            { autoClose: 5000 }
          );
        } else {
          console.log(err);
        }
      } finally {
        setLoading(false);
      }
    } else {
      toast.error("Please fill all fields");
    }
  };

  const options = [
    { value: "payments", label: "Payments API" },
    { value: "credit", label: "Credit" },
    { value: "compliance", label: "Compliance" },
    { value: "payouts", label: "Payouts" },
  ];

  const countries = COUNTRIES.map((country) => ({
    value: country,
    label: country,
  }));

  return (
    <div
      className={`flex ${
        step !== 2 ? "items-center" : ""
      } justify-center px-0 md:px-4 pt-10 md:py-4 md:min-h-[550px] w-full`}
    >
      {
        {
          1: (
            <div className="max-w-lg p-4">
              <h1 className="text-xl md:text-3xl text-[#0A2540] font-semibold">
                Welcome to Flincap
              </h1>
              <p className="text-[#424242] text-sm md:text-base mt-1">
                Kindly fill the form with correct details
              </p>
              <form
                onSubmit={showScheduleCall}
                className="mt-8 flex flex-col gap-4 w-full"
              >
                <div className="flex gap-4">
                  <div>
                    <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple max-w-[265px]">
                      <label className="text-[#999999] text-xs font-light">
                        First Name
                      </label>
                      <input
                        name="firstName"
                        value={formDetails.firstName}
                        onChange={handleChange}
                        className="w-full bg-transparent outline-none text-sm"
                      />
                    </div>
                    {errors?.firstName ? (
                      <p className="text-xs text-red-500">{errors.firstName}</p>
                    ) : (
                      ""
                    )}
                  </div>
                  <div>
                    <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple max-w-[265px]">
                      <label className="text-[#999999] text-xs font-light">
                        Last Name
                      </label>
                      <input
                        name="lastName"
                        value={formDetails.lastName}
                        onChange={handleChange}
                        className="w-full bg-transparent outline-none text-sm"
                      />
                    </div>
                    {errors?.lastName ? (
                      <p className="text-xs text-red-500">{errors.lastName}</p>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
                <div>
                  <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
                    <label className="text-[#999999] text-xs font-light">
                      Exchange Name
                    </label>
                    <input
                      name="businessName"
                      value={formDetails.businessName}
                      onChange={handleChange}
                      className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
                    />
                  </div>
                  {errors?.businessName ? (
                    <p className="text-xs text-red-500">
                      {errors.businessName}
                    </p>
                  ) : (
                    ""
                  )}
                </div>
                <div>
                  <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
                    <label className="text-[#999999] text-xs font-light">
                      Email Address
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formDetails.email}
                      onChange={handleChange}
                      className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
                    />
                  </div>
                  {errors?.email ? (
                    <p className="text-xs text-red-500">{errors.email}</p>
                  ) : (
                    ""
                  )}
                </div>
                <div>
                  <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
                    <label className="text-[#999999] text-xs font-light">
                      Password
                    </label>
                    <input
                      type="password"
                      name="password"
                      value={formDetails.password}
                      onChange={handleChange}
                      className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
                    />
                  </div>
                  {errors?.password ? (
                    <p className="text-xs text-red-500">{errors.password}</p>
                  ) : (
                    ""
                  )}
                </div>
                <div>
                  <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
                    <label className="text-[#999999] text-xs font-light">
                      Phone
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formDetails.phone}
                      onChange={handleChange}
                      className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
                    />
                  </div>
                  {errors?.phone ? (
                    <p className="text-xs text-red-500">{errors.phone}</p>
                  ) : (
                    ""
                  )}
                </div>
                <div>
                  <Select
                    options={countries}
                    placeholder="Select Country"
                    onChange={(e: any) =>
                      setFormDetails({ ...formDetails, country: e.value })
                    }
                    styles={{
                      control: (baseStyles) => ({
                        ...baseStyles,
                        border: "1px solid #CACACA",
                        boxShadow: "none",
                        ":focus-within": {
                          border: "1px solid #7928FF",
                        },
                        padding: "4px 2px 4px 2px",
                        fontSize: "14px",
                      }),
                    }}
                  />
                  {errors?.country ? (
                    <p className="text-xs text-red-500">{errors.country}</p>
                  ) : (
                    ""
                  )}
                </div>
                {/* <Select
                  isMulti
                  options={options}
                  onChange={(e: any) =>
                    setFormDetails({ ...formDetails, products: e })
                  }
                  placeholder="Select Products"
                  styles={{
                    control: (baseStyles, state) => ({
                      ...baseStyles,
                      border: "1px solid #CACACA",
                      boxShadow: "none",
                      ":focus-within": {
                        border: "1px solid #7928FF",
                      },
                      padding: "4px 2px 4px 2px",
                      fontSize: "14px",
                    }),
                  }}
                /> */}
                <button
                  type="submit"
                  className="bg-appPurple text-white py-2 rounded-md font-semibold mt-5"
                >
                  {!loading ? (
                    "Proceed"
                  ) : (
                    <LoadingSpinner className="mx-auto" />
                  )}
                </button>
              </form>
            </div>
          ),
          2: (
            <div className="min-h-[620px]">
              <InlineWidget
                url="https://calendly.com/flincap/call"
                styles={{ maxWidth: "620px", width: "100%", height: "620px" }}
                LoadingSpinner={() => <LoadingSpinner className="mx-auto" />}
                pageSettings={{
                  hideEventTypeDetails: true,
                  hideLandingPageDetails: true,
                  hideGdprBanner: true,
                  primaryColor: "#4f0eb8",
                  textColor: "#4d5055",
                }}
                prefill={{
                  email: formDetails.email,
                  firstName: formDetails.firstName,
                  lastName: formDetails.lastName,
                  name: formDetails.firstName + " " + formDetails.lastName,
                  smsReminderNumber: formDetails.phone,
                  customAnswers: {
                    a1:
                      "I am interested in the " +
                      formDetails.products
                        .map((product: any) => product.label)
                        .join(", ") +
                      " service(s).",
                  },
                  guests: ["<EMAIL>", "<EMAIL>"],
                }}
              />
            </div>
          ),
        }[step]
      }
    </div>
  );
}

export default Page;
