"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import creditIcon from "@/assets/images/creditIcon.svg";
import { UpDownIcon } from "@/components/icons";
import { api } from "@/app/utils";
import NotransactionIcon from "@/assets/images/no-data.svg";
import moment from "moment";
import useStore from "@/store";
import TransactionDetailsModal from "@/components/modals/TransactionDetails";

function Page() {
  const [transactions, setTransactions] = useState<Record<string, any>[]>([]);
  const { setModal } = useStore()

  useEffect(() => {
    if (typeof window != "undefined") {
      api
        .get("/v1/wallets/transactions?assetType=" + "CRYPTO")
        .then((res) => {
          setTransactions(res.data.data);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, []);

  return (
    <div className="px-4">
      <article className="mt-8">
        {/* <div className="text-sm flex flex-wrap gap-2 justify-between items-center mb-2">
          <h3 className="font-semibold">History</h3>
          <div className="filters flex gap-1">
            <div className="flex gap-1">
              <p className="text-appGrayText2">Showing:</p>
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"All Time"}>All Time</option>
              </select>
            </div>
            <div className="flex gap-1">
              <p className="text-appGrayText2">Filter:</p>
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"None"}>None</option>
              </select>
            </div>
          </div>
        </div> */}

        <div className="flex flex-wrap justify-between gap-6 py-4 max-w-[1250px] h-auto overflow-x-auto">
          <table className="w-full">
            <thead className="text-[14px] text-ash rounded-lg bg-ash1 border-b border-b-ash2">
              <tr className="text-left">
                <th className="p-3 font-normal flex items-center gap-2">
                  Type <UpDownIcon className="w-3 h-3" />
                </th>
                <th className="p-3 font-normal">Amount</th>
                <th className="p-3 font-normal">Receiver</th>
                <th className="p-3 font-normal">Status</th>
                <th className="p-3 font-normal flex items-center gap-2">
                  Date <UpDownIcon className="w-3 h-3" />
                </th>
              </tr>
            </thead>
            <tbody>
              {transactions.length
                ? transactions.map((transaction) => {
                  return (
                    <tr
                      className="text-xs text-black hover:text-appPurpleDark border-b border-b-ash2 cursor-pointer hover:translate-x-2 transition-transform duration-300 ease-in-out hover:bg-appWhite"
                      key={transaction.id}
                      onClick={() => setModal(<TransactionDetailsModal transaction={transaction} />)}
                    >
                      <td className="p-3 flex items-center gap-2 whitespace-nowrap">
                        <Image
                          src={creditIcon}
                          alt="credit icon"
                          width={28}
                          height={28}
                        />
                        <span className="font-semibold capitalize">
                          {transaction.transactionType.toLowerCase()}
                        </span>
                      </td>
                      <td className="p-3 font-semibold whitespace-nowrap">
                        {transaction.cryptoWallet?.symbol || ""}{" "}
                        {transaction.cryptoAmount.toLocaleString()}
                      </td>
                      <td className="p-3 whitespace-nowrap">
                        {/* <p>{transaction.toAccName}</p>
                          <p className="text-xs font-light mt-1">
                            {transaction.toAccNum} {transaction.toBankName}
                          </p> */}
                        {transaction.wallet_address}
                      </td>
                      <td className="p-3  font-semibold whitespace-nowrap">
                        {transaction.done ? (
                          <div className="flex gap-1 items-center text-[#13BF62]">
                            <span className="bg-[#13BF62] w-1 h-1"></span>
                            <span>Completed</span>
                          </div>
                        ) : (
                          <div className="flex gap-1 items-center text-red-500">
                            <span className="bg-red-500 w-1 h-1"></span>
                            <span>Processing</span>
                          </div>
                        )}
                      </td>
                      <td className="p-3 font-semibold whitespace-nowrap">
                        {new Date(transaction.createdAt).toLocaleString(
                          undefined,
                          {
                            day: "2-digit",
                            month: "2-digit",
                            year: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                            second: "2-digit",
                          }
                        )}
                      </td>
                    </tr>
                  );
                })
                : null}
            </tbody>
          </table>
          {!transactions.length ? (
            <div className="text-center flex flex-col justify-center items-center w-full min-h-80">
              <Image
                src={NotransactionIcon}
                alt="."
                className="block mx-auto grayscale"
              />
              <p className="my-2.5 text-black">No transactions</p>
              <p className="font-light">
                Once you start trading transactions, they <br /> will appear
                here. <span className="text-appPurple">Start now</span>
              </p>
            </div>
          ) : null}
        </div>
      </article>
    </div>
  );
}

export default Page;
