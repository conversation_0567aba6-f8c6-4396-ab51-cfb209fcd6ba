"use client";
import { api } from "@/app/utils";
import Select from "react-select";
import useStore from "@/store";
import Image from "next/image";
import COUNTRIES from "@/countries.json";
import Link from "next/link";
import React, { FormEvent, useEffect, useState } from "react";
import LoadingSpinner from "@/components/LoadingSpinner";
import { DatePicker } from "antd";
import dayjs from "dayjs";

const isValidDate = (datestr: string) => !isNaN(Date.parse(datestr));

function Page() {
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState<any>({});
  const [errorMessage, setErrorMessage] = useState("");
  const [formDetails, setFormDetails] = useState({
    firstName: "",
    lastName: "",
    email: "",
    country: "",
    companyID: "",
    phone: "",
    businessName: "",
    bvn: "",
    dob: "",
    image: "",
  });

  useEffect(() => {
    // Fetch user data
    setLoading(true);
    api
      .get("/v1/auth/")
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          setUser(res.data.data);
          let dob = "";
          if (res.data.data.dob) {
            const [day, month, year] = res.data.data.dob.split("-");
            dob = month + "/" + day + "/" + year;
          }
          setFormDetails({
            firstName: res.data.data.fullName.split(" ")[0],
            lastName: res.data.data.fullName.split(" ")[1],
            email: res.data.data.email,
            country: res.data.data.country,
            phone: res.data.data.phone,
            bvn: res.data.data.bvn.toString(),
            dob: dob,
            image: "",
            businessName: res.data.data.businessName,
            companyID: res.data.data.companyID,
          });
        } else {
          console.log("Couldn't fetch user");
        }
      })
      .catch((err) => {
        console.log(err);
        if (err.response.data) {
          alert("User BVN or other data may be invalid");
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const handleChange = (e: any) => {
    setFormDetails({ ...formDetails, [e.target.name]: e.target.value });
  };

  const editProfile = async (e: FormEvent) => {
    e.preventDefault();

    if (formDetails.bvn === "true") return;
    setLoading(true);
    // submit data
    const dob = new Date(formDetails.dob)
      .toLocaleDateString("en-GB")
      .replaceAll("/", "-");
    api
      .post(`/v1/settings/profile`, {
        fullName:
          formDetails.firstName.trim() + " " + formDetails.lastName.trim(),
        email: formDetails.email.trim(),
        country: formDetails.country.trim(),
        businessName: formDetails.businessName.trim(),
        companyID: formDetails.companyID.trim(),
        phone: formDetails.phone.trim(),
        bvn: formDetails.bvn.trim(),
        dob: dob.trim(),
      })
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          window.location.reload();
        } else {
          // setErrorMessage(res);
          setErrorMessage(res.data.message);
        }
      })
      .catch((err) => {
        console.log(err, "yes");
        if (err.response) {
          setErrorMessage(err.response.data.message);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const countries = COUNTRIES.map((country) => ({
    value: country,
    label: country,
  }));

  return (
    <div className="md:px-6 px-4 pb-10 pt-4">
      <section>
        <form
          onSubmit={editProfile}
          className="flex flex-col gap-4 max-w-[700px]"
        >
          <p className="text-sm text-red-500">{errorMessage}</p>
          <div className="grid grid-cols-2 gap-4">
            <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
              <label className="text-[#999999] text-xs font-light">
                First Name
              </label>
              <input
                disabled={formDetails.bvn === "true"}
                name="firstName"
                value={formDetails?.firstName}
                onChange={handleChange}
                className="w-full bg-transparent outline-none text-sm"
              />
            </div>
            <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
              <label className="text-[#999999] text-xs font-light">
                Last Name
              </label>
              <input
                disabled={formDetails.bvn === "true"}
                name="lastName"
                value={formDetails?.lastName}
                onChange={handleChange}
                className="w-full bg-transparent outline-none text-sm"
              />
            </div>
          </div>

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">Email</label>
            <input
              disabled={formDetails.bvn === "true"}
              name="email"
              value={formDetails?.email}
              onChange={handleChange}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Registered Business Name
            </label>
            <input
              name="businessName"
              onChange={handleChange}
              value={formDetails.businessName || ""}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>

          {formDetails.bvn !== "true" && (
            <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
              <label className="text-[#999999] text-xs font-light">
                Date Of Birth
              </label>
              <DatePicker
                className="w-full outline-none border-none p-0"
                value={
                  isValidDate(formDetails.dob) ? dayjs(formDetails.dob) : null
                }
                onChange={(_date: any, datestring: any) =>
                  setFormDetails((prev) => ({ ...prev, dob: datestring }))
                }
              />
            </div>
          )}

          <Select
            options={countries}
            isDisabled={formDetails.bvn === "true"}
            placeholder="Select Country"
            onChange={(e: any) =>
              setFormDetails({ ...formDetails, country: e.value })
            }
            value={{ label: formDetails.country, value: formDetails.country }}
            styles={{
              control: (baseStyles) => ({
                ...baseStyles,
                border: "1px solid #CACACA",
                boxShadow: "none",
                ":focus-within": {
                  border: "1px solid #7928FF",
                },
                padding: "4px 2px 4px 2px",
                fontSize: "14px",
              }),
            }}
          />

          <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col">
            <label className="text-[#999999] text-xs font-light">
              Contact Phone Number
            </label>
            <input
              disabled={formDetails.bvn === "true"}
              name="phone"
              value={formDetails?.phone}
              onChange={handleChange}
              className="w-full bg-transparent outline-none text-sm"
            />
          </div>

          {formDetails.bvn === "true" ? null : (
            <button className="bg-appPurple text-white px-4 py-2 rounded">
              {!loading ? "Update" : <LoadingSpinner className="mx-auto" />}
            </button>
          )}
        </form>
      </section>
    </div>
  );
}

export default Page;
