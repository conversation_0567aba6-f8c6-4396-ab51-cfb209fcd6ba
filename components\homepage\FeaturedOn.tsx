import React from "react";
import { Reveal } from "../animations/animations";
import Image from "next/image";

export default function FeaturedOn() {
  return (
    <section className="testimonials">
      <div className="max-w-7xl py-10 mx-auto flex flex-col items-center gap-12 md:py-20 md:flex-row">
        <Reveal>
          <h2 className="text-center text-3xl font-extrabold text-[#0A2540]">
            Featured On
          </h2>
        </Reveal>
        <div className="flex md:gap-10 flex-col flex-1">
          <div className="flex gap-7 items-center flex-col md:flex-row md:justify-between md:gap-5">
            <Reveal delay={0.4}>
              <Image
                src={"/images/technext-logo.svg"}
                alt="."
                width={160}
                height={37}
              />
            </Reveal>
            <Reveal delay={0.5}>
              <Image
                src={"/images/cryptodaily-logo.svg"}
                alt="."
                width={179}
                height={43.98}
              />
            </Reveal>
            <Reveal delay={0.6}>
              <Image
                src={"/images/beincrypto-logo.svg"}
                alt="."
                width={185.13}
                height={66.27}
              />
            </Reveal>
            <Reveal delay={0.7}>
              <Image
                src={"/images/nairametrics-logo.svg"}
                alt="."
                width={170.58}
                height={18.36}
              />
            </Reveal>
            <Reveal delay={0.8}>
              <Image
                src={"/images/globaltv-logo.svg"}
                alt="."
                width={75.72}
                height={75.72}
              />
            </Reveal>
          </div>

          <div className="flex gap-7 items-center flex-col md:flex-row md:justify-between md:gap-5">
            <Reveal delay={0.9}>
              <Image
                src={"/images/cointelegraph-logo.svg"}
                alt="."
                width={219.26}
                height={44.17}
              />
            </Reveal>
            <Reveal delay={1}>
              <Image
                src={"/images/bitcoincom-logo.svg"}
                alt="."
                width={170}
                height={25.14}
              />
            </Reveal>
            <Reveal delay={1.1}>
              <Image
                src={"/images/plustv-logo.svg"}
                alt="."
                width={85.74}
                height={56.44}
              />
            </Reveal>
            <Reveal delay={1.2}>
              <Image
                src={"/images/punch-logo.svg"}
                alt="."
                width={91}
                height={33.53}
              />
            </Reveal>
            <Reveal delay={1.3}>
              <Image
                src={"/images/channels-logo.svg"}
                alt="."
                width={68.85}
                height={68.04}
              />
            </Reveal>
          </div>
        </div>
      </div>
    </section>
  );
}
