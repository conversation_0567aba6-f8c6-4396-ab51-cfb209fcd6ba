"use client";
import Image from 'next/image';
import FlincapIcon from "@/assets/images/logo-no-text.svg";
import GoodTick from "@/assets/images/good-tick.svg";
import Link from 'next/link';
import useStore from '@/store';

function Page() {
  const { user } = useStore();

  return (
    <div>
      <div className="grid md:grid-cols-2 gap-5 px-4 py-6">
        <div className="border shadow-sm px-4 py-7 rounded-md">
          <div className='flex items-center gap-1.5'>
            <Image src={FlincapIcon} alt='flincap logo' className='w-7' />
            <h2>Wallet Infrastructure</h2>
          </div>

          <p className='font-light text-sm my-4 text-black'>Our wallet infrastructure enables you to set up your crypto exchange without hassles. You can also trade and swap with us.</p>

          {/* <div className='flex gap-1.5 items-center'>
            <p className='text-appGrayTextLight text-sm'>You are eligible:</p>
            <Image src={GoodTick} alt='yes' className='w-4' />
          </div> */}

          <div className="flex justify-between items-center mt-4">
            <Link href={user?.walletService ? "#" : "https://wa.me/***********"} target='_blank' className={`bg-black text-white px-3.5 py-1.5 rounded-md shadown-sm border-appGrayText2 text-sm ${user?.walletService ? 'opacity-55 cursor-default' : ''}`}>{user?.walletService ? "Subscribed" : "Subscribe"}</Link>
            <Link href="#" className='border px-3.5 py-1.5 rounded-md shadow-sm text-sm text-appPurple'>View docs</Link>
          </div>
        </div>


        {/* <div className="border shadow-sm px-4 py-7 rounded-md">
          <div className='flex items-center gap-1.5'>
            <Image src={FlincapIcon} alt='flincap logo' className='w-7' />
            <h2>Credit</h2>
          </div>

          <p className='font-light text-sm my-4 text-black'>We provide loans to help you keep your business afloat and running. You can take advantage of more opportunities in the market with our credit facilities.</p>

          <div className="flex justify-between items-center mt-4">
            <Link href={user?.creditService ? "#" : "https://wa.me/***********"} target='_blank' className={`bg-black text-white px-3.5 py-1.5 rounded-md shadown-sm border-appGrayText2 text-sm ${user?.creditService ? 'opacity-75 cursor-default' : ''}`}>{user?.creditService ? "Subscribed" : "Subscribe"}</Link>
            <Link href="#" className='border px-3.5 py-1.5 rounded-md shadow-sm text-sm text-appPurple'>View docs</Link>
          </div>
        </div> */}

        <div className="border shadow-sm px-4 py-7 rounded-md">
          <div className='flex items-center gap-1.5'>
            <Image src={FlincapIcon} alt='flincap logo' className='w-7' />
            <h2>Payout and Disbursement</h2>
          </div>

          <p className='font-light text-sm my-4 text-black'>Making and receiving payments is easy with our payment APIs. You can enable fiat and crypto payments using our payment solutions.</p>

          {/* <div className='flex gap-1.5 items-center'>
            <p className='text-appGrayTextLight text-sm'>You are eligible:</p>
            <Image src={GoodTick} alt='yes' className='w-4' />
          </div> */}

          <div className="flex justify-between items-center mt-4">
            <Link href={user?.payoutService ? "#" : "https://wa.me/***********"} target='_blank' className={`bg-black text-white px-3.5 py-1.5 rounded-md shadown-sm border-appGrayText2 text-sm ${user?.payoutService ? 'opacity-75 cursor-default' : ''}`}>{user?.payoutService ? "Subscribed" : "Subscribe"}</Link>
            <Link href="#" className='border px-3.5 py-1.5 rounded-md shadow-sm text-sm text-appPurple'>View docs</Link>
          </div>
        </div>


        {/* <div className="border shadow-sm px-4 py-7 rounded-md">
          <div className='flex items-center gap-1.5'>
            <Image src={FlincapIcon} alt='flincap logo' className='w-7' />
            <h2>Compliance Services</h2>
          </div>

          <p className='font-light text-sm my-4 text-black'>At Flincap, we help fintech businesses stay compliant, reducing the risks to their customers and increasing trust in their companies.</p>

          <div className="flex justify-between items-center mt-4">
            <Link href={user?.complianceService ? "#" : "https://wa.me/***********"} target='_blank' className={`bg-black text-white px-3.5 py-1.5 rounded-md shadown-sm border-appGrayText2 text-sm ${user?.complianceService ? 'opacity-75 cursor-default' : ''}`}>{user?.complianceService ? "Subscribed" : "Subscribe"}</Link>
            <Link href="#" className='border px-3.5 py-1.5 rounded-md shadow-sm text-sm text-appPurple'>View docs</Link>
          </div>
        </div> */}
      </div>
    </div>
  )
}

export default Page