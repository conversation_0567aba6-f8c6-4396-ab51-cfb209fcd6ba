import Link from "next/link";
import Image from "next/image";
import { <PERSON>eal } from "../animations/animations";
import { ScaleButton } from "../animations/animations";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import AutoScroll from "embla-carousel-auto-scroll";
import AutoPlay from "embla-carousel-autoplay";

const images = [
  {
    src: "/images/technext-logo.svg",
    alt: "technext",
    width: 160,
    height: 37,
  },
  {
    src: "/images/cryptodaily-logo.svg",
    alt: "cryptodaily",
    width: 179,
    height: 43.98,
  },
  {
    src: "/images/beincrypto-logo.svg",
    alt: "beincrypto",
    width: 185.13,
    height: 66.27,
  },
  {
    src: "/images/nairametrics-logo.svg",
    alt: "nairametrics",
    width: 170.58,
    height: 18.36,
  },
  {
    src: "/images/channels-logo.svg",
    alt: "channels",
    width: 48.85,
    height: 48.04,
  },
  {
    src: "/images/cointelegraph-logo.svg",
    alt: "cointelegraph",
    width: 219.26,
    height: 44.17,
  },
  {
    src: "/images/bitcoincom-logo.svg",
    alt: "bitcoincom",
    width: 170,
    height: 25.14,
  },
  {
    src: "/images/plustv-logo.svg",
    alt: "plustv",
    width: 85.74,
    height: 56.44,
  },
  {
    src: "/images/punch-logo.svg",
    alt: "punch",
    width: 91,
    height: 33.53,
  },

  {
    src: "/images/globaltv-logo.svg",
    alt: "globaltv",
    width: 75.72,
    height: 75.72,
  },
];
export default function Header({ user }: any) {
  return (
    <section className="bg-appWhite h-screen lg:h-[calc(100vh-100px)] bg-[url(/images/hero-bg.svg)] bg-cover bg-top bg-no-repeat">
      <div className="max-w-6xl mx-auto px-4 flex flex-col lg:flex-row justify-center lg:justify-between items-center h-[90%] py-28">
        <div className="lg:block lg:text-left text-center flex justify-center items-center flex-col">
          <Reveal delay={0.1}>
            <h1 className="text-3xl lg:text-6xl font-bold text-black mt-[-100px]">
              Borderless. Payments. Stablecoins.
            </h1>
            <p className="my-4 lg:my-7 max-w-md lg:max-w-lg text-md lg:text-lg">
              Send and receive payments across African currencies with ease.
            </p>
            <div className="flex flex-col md:flex-row items-center gap-4 my-6">
              <span className="text-sm font-medium text-gray-700">
                Supported stablecoins:
              </span>
              <div className="flex gap-4">
                <Image
                  src="/tether-usdt-logo.svg"
                  alt="Tether USDT"
                  width={42}
                  height={42}
                  className="inline-block"
                />
                <Image
                  src="/usd-coin-usdc-logo.svg"
                  alt="USD Coin USDC"
                  width={42}
                  height={42}
                  className="inline-block"
                />
              </div>
            </div>
          </Reveal>
          {user ? (
            <Link
              href="/dashboard"
              className="bg-appPurple text-white px-6 py-2.5 rounded-md block w-fit"
            >
              Dashboard
            </Link>
          ) : (
            <Reveal>
              <div className="flex gap-1">
                <ScaleButton>
                  <Link
                    href="https://forms.gle/HmaHKiMd2BJ1SGZg7"
                    className="bg-appPurple hover:bg-appPurpleDark text-white px-6 py-2.5 rounded-md block w-fit"
                  >
                    Get Started
                  </Link>
                </ScaleButton>
                {/* <Link
                  href="https://docs.flincap.com"
                  target="_blank"
                  className="border-appPurple text-appPurple px-6 py-2.5 rounded-md block w-fit"
                >
                  View API Docs
                </Link> */}
              </div>
            </Reveal>
          )}
        </div>
        <div className="image lg:inline-flex">
          <Image
            width={250}
            height={250}
            alt="banner illustration"
            src={"/images/banner-image.png"}
            className="bg-blend-darken lg:w-[400px] lg:h-[400px] lg:mt-[-100px]"
          />
        </div>
      </div>
      <Carousel plugins={[AutoScroll({ loop: true })]} className="mt-[-30px]">
        <CarouselContent className="md:-ml-4">
          {Array.from({ length: images.length }, (_, i) => (
            <CarouselItem key={i} className="lg:pl-4 lg:w-[160px] self-center">
              <Image
                src={images[i].src}
                alt={images[i].alt}
                width={images[i].width}
                height={images[i].height}
                className="mr-4"
              />
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
    </section>
  );
}
