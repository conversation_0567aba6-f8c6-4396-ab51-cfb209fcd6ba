import { TransactionType } from "@/types/types";
import { useState, useMemo } from "react";

export function usePagination<T>(
  data: T[],
  defaultRowsPerPage = 20,
  rowsPerPageOptions = [5, 10, 15, 20]
) {
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage);

  const { indexOfFirstRow, indexOfLastRow, totalPages, paginatedData } =
    useMemo(() => {
      const indexOfLastRow = rowsPerPage * currentPage;
      const indexOfFirstRow = indexOfLastRow - rowsPerPage;
      const totalPages = Math.ceil(data.length / rowsPerPage);
      const paginatedData = data.slice(indexOfFirstRow, indexOfLastRow);

      return { indexOfFirstRow, indexOfLastRow, totalPages, paginatedData };
    }, [currentPage, rowsPerPage, data.length]);

  const handleNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage((prev) => prev + 1);
    }
  };
  const handlePrev = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const handleRowsPerPageChange = (value: number) => {
    setRowsPerPage(value);
    setCurrentPage(1);
    console.log(value);
  };

  return {
    currentPage,
    rowsPerPage,
    handleRowsPerPageChange,
    rowsPerPageOptions,
    indexOfFirstRow,
    indexOfLastRow,
    totalPages,
    paginatedData,
    handleNext,
    handlePrev,
  };
}
