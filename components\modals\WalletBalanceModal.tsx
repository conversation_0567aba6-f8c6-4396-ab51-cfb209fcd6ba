import useStore from "@/store";
import React, { useEffect, useState } from "react";
import { XMarkIcon } from "../icons";
import { adminApi, api, getTokenName } from "@/app/utils";
import Select from "react-select";
import { Select as ActionSelect } from "antd";
import LoadingSpinner from "../LoadingSpinner";
import NairaIcon from "@/assets/images/wallets/naira.svg";
import banks from "@/banks.json";
import Image from "next/image";
import { iWalletSymbol } from "@/types/utils";
import CryptoIcon from "../CryptoIcon";

function WalletBalanceModal({ wallet }: { wallet: any }) {
  const { setModal } = useStore();
  const [loading, setLoading] = useState(false);
  const [action, setAction] = useState("");
  const [formDetails, setFormDetails] = useState({
    amount: "",
    address: "",
    bankCode: "",
    bankName: "",
    accName: "",
    accNumber: "",
  });

  useEffect(() => {
    const { bankCode, accNumber } = formDetails;
    if (bankCode && accNumber.length == 10) {
      //  fetch bank account name
      console.log("fetch bank account name");
      api
        .post("/v1/wallets/resolve-account", {
          accountNumber: formDetails.accNumber,
          bankCode: formDetails.bankCode,
        })
        .then((res) => {
          console.log(res.data);
          if (res.data?.data?.account_name) {
            setFormDetails((prev) => ({
              ...prev,
              accName: res.data.data.account_name,
            }));
          }
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, [formDetails.bankCode, formDetails.accNumber, formDetails]);

  const submitForm = async (e: any) => {
    e.preventDefault();
    setLoading(true);

    adminApi
      .post(`/v1/admin/users/${wallet.id}/admin-update-wallet-balance`, {
        action,
        amount: Number(formDetails.amount.replace(/,/g, "")),
        accountName: formDetails.accName,
        accountNumber: formDetails.accNumber,
        bankCode: formDetails.bankCode,
        bankName: formDetails.bankName,
        address: formDetails.address,
      })
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          window.location.reload();
        }
        console.log(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => setLoading(false));
  };

  console.log("form details", formDetails);

  return (
    <div className="bg-white rounded-md h-fit max-w-80 md:max-w-96 p-4 grow">
      <div className="flex justify-between items-center">
        <h3 className="font-semibold">
          Update {getTokenName(wallet.symbol).name} Wallet
        </h3>
        <button onClick={() => setModal(null)}>
          <XMarkIcon className="w-6 h-6" />
        </button>
      </div>

      <form
        onSubmit={submitForm}
        className="bg-white rounded-md h-fit grow pb-6"
      >
        <div className="border mt-4 rounded p-2 mx-auto flex justify-between items-center focus-within:border-appPurple">
          <div className="flex flex-col">
            <label className="text-[#999999] text-xs font-light">Amount</label>
            <div className="flex gap">
              {/* <input
                name="amount"
                autoComplete="off"
                type="text"
                value={formDetails.amount || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d*\.?\d*$/.test(value)) {
                    setFormDetails({
                      ...formDetails,
                      [e.target.name]: value === "" ? "" : value
                    })
                  }
                }}
                className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
              /> */}
              <input
                name="amount"
                autoComplete="off"
                type="text"
                value={formDetails.amount || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  const formattedValue = value.replace(/[^\d.,]/g, "");
                  setFormDetails({
                    ...formDetails,
                    [e.target.name]: formattedValue,
                  });
                }}
                className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
              />
            </div>
          </div>
          {wallet.walletType === "FIAT" ? (
            <div className="flex items-center gap-1">
              <Image src={NairaIcon} alt="." className="w-5 h-5" />
              <span className="text-sm">NGN</span>
            </div>
          ) : (
            <div className="flex items-center gap-1 pr-2">
              <CryptoIcon
                assetType={wallet.symbol as Exclude<iWalletSymbol, "NGN">}
                className="w-5 h-5"
              />
              <span className="text-sm">{wallet.symbol}</span>
            </div>
          )}
        </div>

        <div className="mx-auto mt-3">
          <p className="text-sm font-semibold">Balance: {wallet.balance}</p>
        </div>

        <ActionSelect
          options={[
            { value: "withdraw", label: "Withdraw" },
            { value: "deposit", label: "Deposit" },
          ]}
          placeholder="Action"
          onChange={(act) => setAction(act)}
          className="mt-3 h-12 w-full"
        />

        {wallet.walletType === "FIAT" ? (
          <div>
            <Select
              options={banks.map((bank) => ({
                label: bank.name,
                value: bank.code,
              }))}
              placeholder="Select Bank"
              isSearchable={true}
              onChange={(e: any) =>
                setFormDetails({
                  ...formDetails,
                  bankName: e.label,
                  bankCode: e.value,
                })
              }
              styles={{
                control: (baseStyles) => ({
                  ...baseStyles,
                  border: "1px solid #CACACA",
                  boxShadow: "none",
                  ":focus-within": {
                    border: "1px solid #7928FF",
                  },
                  padding: "4px 2px 4px 2px",
                  fontSize: "14px",
                  marginTop: "8px",
                }),
              }}
            />

            <div className="my-5">
              <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
                <label className="text-[#999999] text-xs font-light">
                  Account Number
                </label>
                <input
                  name="accNumber"
                  value={formDetails.accNumber}
                  onChange={(e) =>
                    e.target.value.length <= 10 &&
                    setFormDetails({
                      ...formDetails,
                      [e.target.name]: e.target.value,
                    })
                  }
                  className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
                />
              </div>
              <div
                style={{ opacity: formDetails.accName ? "100" : "0" }}
                className={`text-[13px] mt-1.5 text-appPurple flex gap-1 items-center`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="25"
                  height="24"
                  className="w-5 h-5"
                  viewBox="0 0 25 24"
                  fill="none"
                >
                  <path
                    d="M5.6 16C5.3 15.5 4.7 15.4 4.2 15.6C3.7 15.9 3.6 16.5 3.8 17C4.1 17.5 4.7 17.6 5.2 17.4C5.7 17.1 5.8 16.5 5.6 16ZM5.2 6.6C4.7 6.4 4.1 6.5 3.8 7C3.6 7.5 3.7 8.1 4.2 8.4C4.7 8.6 5.3 8.5 5.6 8C5.8 7.5 5.7 6.9 5.2 6.6ZM20.8 8.4C21.3 8.1 21.4 7.5 21.2 7C20.9 6.5 20.3 6.4 19.8 6.6C19.3 6.9 19.2 7.5 19.4 8C19.7 8.5 20.3 8.6 20.8 8.4ZM4.5 12C4.5 11.4 4.1 11 3.5 11C2.9 11 2.5 11.4 2.5 12C2.5 12.6 2.9 13 3.5 13C4.1 13 4.5 12.6 4.5 12ZM7.7 18.8C7.2 18.9 6.8 19.5 7 20C7.1 20.5 7.7 20.9 8.2 20.7C8.7 20.6 9.1 20 8.9 19.5C8.8 19 8.3 18.7 7.7 18.8ZM21.5 11C20.9 11 20.5 11.4 20.5 12C20.5 12.6 20.9 13 21.5 13C22.1 13 22.5 12.6 22.5 12C22.5 11.4 22.1 11 21.5 11ZM20.8 15.6C20.3 15.3 19.7 15.5 19.4 16C19.1 16.5 19.3 17.1 19.8 17.4C20.3 17.7 20.9 17.5 21.2 17C21.4 16.5 21.3 15.9 20.8 15.6ZM17.5 3.3C17 3 16.4 3.2 16.1 3.7C15.8 4.2 16 4.8 16.5 5.1C17 5.4 17.6 5.2 17.9 4.7C18.1 4.2 18 3.6 17.5 3.3ZM17.3 18.8C16.8 18.7 16.2 19 16.1 19.5C16 20 16.3 20.6 16.8 20.7C17.3 20.8 17.9 20.5 18 20C18.1 19.5 17.8 19 17.3 18.8ZM12.5 20C11.9 20 11.5 20.4 11.5 21C11.5 21.6 11.9 22 12.5 22C13.1 22 13.5 21.6 13.5 21C13.5 20.4 13.1 20 12.5 20ZM12.5 2C11.9 2 11.5 2.4 11.5 3C11.5 3.6 11.9 4 12.5 4C13.1 4 13.5 3.6 13.5 3C13.5 2.4 13.1 2 12.5 2Z"
                    fill="#DBC5FF"
                  />
                </svg>
                <p>{formDetails.accName}</p>
              </div>
            </div>
          </div>
        ) : action === "withdraw" ? (
          <div className="mt-4 py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
            <label className="text-[#999999] text-xs font-light">
              Wallet Address
            </label>
            <input
              name="address"
              autoComplete="off"
              type="text"
              value={formDetails.address || ""}
              onChange={(e) =>
                setFormDetails({
                  ...formDetails,
                  [e.target.name]: e.target.value,
                })
              }
              className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
            />
          </div>
        ) : null}

        <button
          type="submit"
          className="mt-4 bg-appPurple text-white rounded py-2 w-full"
        >
          {loading ? <LoadingSpinner className="mx-auto" /> : "Submit"}
        </button>
      </form>
    </div>
  );
}

export default WalletBalanceModal;
