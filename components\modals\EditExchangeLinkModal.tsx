import useStore from "@/store";
import { FormEvent, useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
  DialogFooter,
} from "@/components/ui/dialog";
import LoadingSpinner from "../LoadingSpinner";
import { toast } from "react-toastify";
import {
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@/components/icons";
import { api } from "@/app/utils";

export default function EditExchangeLinkModal({
  user,
  setUser,
  domain,
  userName,
  setUserName,
}: any) {
  const [loading, setLoading] = useState(false);
  const [errorMessages, setErrorMessages] = useState<string[]>([]);
  const editTradeUserName = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);
    api
      .post("/v1/settings/trade-link", { companyID: userName })
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          setUser({ ...user, companyID: userName });
          setErrorMessages([]);
          toast.info("Update successful");
        } else {
          toast.info("Failed to update trade username");
          console.log(res.data);
          setErrorMessages(res.data.message);
        }
      })
      .catch((err) => {
        if (err.response) {
          console.log(err.response.data);
        } else console.log(err);
      })
      .finally(() => setLoading(false));
  };
  return (
    <Dialog>
      <DialogTrigger>
        <button
          type="button"
          className="py-1 px-3 text-sm md:text-base md:px-5 bg-white rounded-lg border border-solid border-gray-300 shadow-sm hover:border-gray-400/75"
        >
          Edit
        </button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogClose className="text-left flex items-center">
            <ChevronLeftIcon className="w-3 h-3" />
            <p className=" font-circularStd">Go back</p>
          </DialogClose>
          <DialogTitle className="text-center">
            <h3 className="font-circularStd">Exchange Link</h3>
          </DialogTitle>
          <hr />
          <DialogDescription>
            <form className="mt-2" onSubmit={editTradeUserName}>
              <ul className="list-disc pl-4">
                {errorMessages.map((msg: string, idx: number) => (
                  <li key={idx} className="text-red-500">
                    {msg}
                  </li>
                ))}
              </ul>
              <div className="flex items-center py-1 px-3 border border-[#CACACA] rounded bg">
                <div className="flex flex-col">
                  <label className="text-[#999999] text-xs font-light">
                    Exchange Username
                  </label>
                  <p className="text-black font-circularStd">{domain}/</p>
                </div>
                <input
                  name="username"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  className="grow h-10 pt-3.5 bg-transparent outline-none text-sm text-black font-circularStd"
                />
              </div>
              <div className="mt-7">
                <button
                  type="submit"
                  className="bg-appPurple w-full text-white px-4 py-2 rounded mb-2"
                >
                  {!loading ? "Update" : <LoadingSpinner className="mx-auto" />}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    if (user?.companyID) {
                      navigator.clipboard
                        .writeText(domain + "/" + userName)
                        .then((res) => {
                          toast.info("Payment link copied to clipboard");
                        });
                    } else {
                      toast.info("Please update your payment link");
                    }
                  }}
                  className="w-full text-appPurple border border-appPurple px-4 py-2 rounded"
                >
                  Copy
                </button>
              </div>
            </form>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="sm:justify-start text-xs text-[#999999]">
          <p>Click the Update button to save your changes.</p>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
