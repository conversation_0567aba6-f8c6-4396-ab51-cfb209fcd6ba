"use client";
import Image from "next/image";
import Link from "next/link";
import {
  BellIcon,
  Hamburger,
  QuestionIcon,
  SignoutIcon,
  XMarkIcon,
} from "./icons";
import ReactSwitch from "react-switch";
import useStore from "@/store";
import { useEffect, useState } from "react";

function DashboardNav() {
  const { liveMode, setLiveMode } = useStore();
  const [showMenu, setShowMenu] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      // load environment_mode
      // const tenv = localStorage.getItem("tenv");
      // setLiveMode(tenv === "live");
      setLiveMode(location.hostname.startsWith("www.flincap.com"));
    }
  }, [setLiveMode]);

  const logoutUser = () => {
    localStorage.removeItem("tid");
    window.location.href = "/auth/login";
  };

  return (
    <header className="bg-white">
      <div className="px-4 py-[10px] flex justify-between items-center">
        <Link href={"/"} className="logo">
          <Image
            src={"/images/flincap-icon.svg"}
            alt="."
            width={100}
            height={50}
          />
        </Link>

        <div className="actions hidden md:flex gap-2 items-center">
          <div className="mode border-r-[1px] border-r-[#eeeeee] pr-2 flex gap-2 items-center">
            <ReactSwitch
              onChange={(checked) => {
                // setLiveMode(checked);
                // localStorage.setItem("tenv", checked ? "live" : "test");
                if (checked) {
                  window.location.href =
                    "https://flincap.com" + window.location.pathname;
                } else {
                  window.location.href =
                    "https://test.flincap.com" + window.location.pathname;
                }
              }}
              height={16}
              width={36}
              draggable={false}
              handleDiameter={10}
              checkedIcon={false}
              uncheckedIcon={false}
              checked={liveMode}
            />
            <span className="text-sm">Live Mode</span>
          </div>

          <div className="flex gap-2 mr-1">
            <Link href={"/"}>
              <BellIcon />
            </Link>
            <Link href={"/"}>
              <QuestionIcon />
            </Link>
          </div>

          <button
            onClick={logoutUser}
            className="flex space-x-2 text-red-500 font-light text-sm"
          >
            <SignoutIcon />
            <span>Sign out</span>
          </button>
        </div>

        <button
          className="burger p-1 block md:hidden"
          onClick={() => setShowMenu((prev) => !prev)}
        >
          {showMenu ? (
            <XMarkIcon className="w-4 h-4" />
          ) : (
            <Hamburger className="w-4 h-4" />
          )}
        </button>

        {showMenu ? (
          <ul className="md:hidden absolute z-20 bg-white left-0 right-0 top-[50px] border-b shadow-sm px-4 py-5 flex flex-col gap-4">
            <li className="flex items-center gap-2 pl-1">
              <ReactSwitch
                onChange={(checked) => {
                  if (checked) {
                    window.location.href =
                      "https://flincap.com" + window.location.pathname;
                  } else {
                    window.location.href =
                      "https://test.flincap.com" + window.location.pathname;
                  }
                }}
                height={18}
                width={33}
                draggable={false}
                handleDiameter={12}
                checkedIcon={false}
                uncheckedIcon={false}
                checked={liveMode}
              />
              <span className="text-sm">Live Mode</span>
            </li>
            {/* <li className="flex flex-row-reverse items-center p-2 gap-2 bg-[#fbfbfb] rounded-lg">
              <MagnifyingGlassIcon className="w-5 h-5" />
              <input
                type="text"
                placeholder="Search or jump to...."
                className="placeholder:text-appGrayTextLight text-appGrayText w-full bg-transparent border-none outline-none ring-0 text-sm"
              />
            </li> */}
            <li className="flex flex-col gap-4 w-full text-sm">
              <Link href={"/"} className="flex gap-2">
                <BellIcon /> <p>Notification</p>
              </Link>
              <Link href={"/"} className="flex gap-2">
                <QuestionIcon /> <p>Support</p>
              </Link>
            </li>
            <li>
              <button
                onClick={logoutUser}
                className="flex items-center gap-2 text-red-500 font-light text-sm cursor-pointer"
              >
                <SignoutIcon />
                <span>Sign out</span>
              </button>
            </li>
          </ul>
        ) : null}
      </div>
    </header>
  );
}

export default DashboardNav;
