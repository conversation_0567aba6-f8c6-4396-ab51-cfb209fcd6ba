"use client";
import Link from 'next/link'
import React from 'react'

function Preferences() {
  return (
    <div className='md:px-6 px-4 py-6'>
      <section>
        <div className='space-y-4'>
          <label className='border px-2.5 py-4 rounded flex items-start gap-2 cursor-pointer max-w-[500px] '>
            <input type="checkbox" className='mt-2 accent-appPurple' />
            <div>
              <h3>Email Notifications</h3>
              <p className='text-appGrayTextLight font-light text-sm'>By selecting this, you will receive email notifications</p>
            </div>
          </label>
          <label className='border px-2.5 py-4 rounded flex items-start gap-2 cursor-pointer max-w-[500px] '>
            <input type="checkbox" className='mt-2 accent-appPurple' />
            <div>
              <h3>Telegram Notifications</h3>
              <p className='text-appGrayTextLight font-light text-sm'>By selecting this, you will receive telegram nofications</p>
            </div>
          </label>
        </div>
      </section>
    </div >
  )
}

export default Preferences