/**
 * @description A library of icons
 * @param {*} props
 * @returns
 */

import { SVGAttributes } from "react";

export function SwapCryptoIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="16" cy="16.8" r="15.5" fill="white" stroke="#E3E3E3" />
      <path
        d="M9.6665 19.4668L12.3332 22.1334M12.3332 22.1334L14.9998 19.4668M12.3332 22.1334L12.3332 12.8001C12.3332 12.0637 12.9301 11.4668 13.6665 11.4668V11.4668"
        stroke="#7928FF"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M17 14.1333L19.6667 11.4667M19.6667 11.4667L22.3333 14.1333M19.6667 11.4667L19.6667 21.4667C19.6667 22.2031 19.0697 22.8 18.3333 22.8V22.8"
        stroke="#7928FF"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
}

export function InformationCircleIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
      />
    </svg>
  );
}

export function SendIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      width="31"
      height="30"
      viewBox="0 0 31 30"
      fill="none"
      {...props}
    >
      <path
        d="M25.0916 5.18638L5.93231 11.972C5.76367 12.0317 5.74379 12.2622 5.89973 12.3499L12.0524 15.8108C12.1223 15.8501 12.2084 15.8459 12.274 15.7999L19.5621 10.6983C19.752 10.5653 19.9844 10.7953 19.8535 10.9866L15.149 17.8624C15.1082 17.9221 15.1012 17.9986 15.1306 18.0647L18.2024 24.9763C18.2791 25.149 18.5277 25.1385 18.5896 24.96L25.358 5.45105C25.4152 5.28605 25.2563 5.12808 25.0916 5.18638Z"
        fill="black"
      />
    </svg>
  );
}

export function NewTabIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      strokeWidth={1.5}
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"
      />
    </svg>
  );
}

export function TagIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      strokeWidth={0.2}
      stroke="currentColor"
      viewBox="0 0 30 30"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        d="M8.75002 7.50002C8.50279 7.50002 8.26112 7.57333 8.05556 7.71068C7.85 7.84804 7.68978 8.04326 7.59517 8.27167C7.50056 8.50007 7.47581 8.75141 7.52404 8.99388C7.57227 9.23636 7.69132 9.45909 7.86614 9.6339C8.04095 9.80872 8.26368 9.92777 8.50616 9.976C8.74863 10.0242 8.99997 9.99948 9.22837 9.90487C9.45678 9.81026 9.65201 9.65004 9.78936 9.44448C9.92671 9.23892 10 8.99725 10 8.75002C10 8.4185 9.86832 8.10056 9.6339 7.86614C9.39948 7.63172 9.08154 7.50002 8.75002 7.50002ZM27.1375 14.725L15.2875 2.90002C15.171 2.77435 15.0298 2.67399 14.8728 2.60518C14.7159 2.53637 14.5464 2.50057 14.375 2.50002H6.87502C6.71051 2.49907 6.54743 2.5306 6.39514 2.59281C6.24284 2.65501 6.10432 2.74667 5.98752 2.86252L2.86252 5.97502C2.74667 6.09182 2.65501 6.23034 2.59281 6.38264C2.5306 6.53493 2.49907 6.69801 2.50002 6.86252V14.3625C2.50463 14.6935 2.6341 15.0105 2.86252 15.25L14.725 27.125C14.9646 27.3534 15.2816 27.4829 15.6125 27.4875C15.777 27.4885 15.9401 27.4569 16.0924 27.3947C16.2447 27.3325 16.3832 27.2409 16.5 27.125L27.1375 16.4875C27.2534 16.3707 27.345 16.2322 27.4072 16.0799C27.4694 15.9276 27.501 15.7645 27.5 15.6C27.4923 15.2734 27.363 14.9614 27.1375 14.725ZM15.6125 24.4875L5.00002 13.8625V7.37502L7.37502 5.00002H13.85L24.475 15.6125L15.6125 24.4875Z"
        fill="black"
      />
    </svg>
  );
}

export function LogoutIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 19 20"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        d="M13.3243 14.75C13.1261 14.5093 13.027 14.2359 13.027 13.93C13.027 13.6248 13.1261 13.3704 13.3243 13.1667L15.3243 11.1111H7.56757C7.26126 11.1111 7.00468 11.0044 6.79784 10.7911C6.59027 10.5785 6.48649 10.3148 6.48649 10C6.48649 9.68519 6.59027 9.42111 6.79784 9.20778C7.00468 8.99519 7.26126 8.88889 7.56757 8.88889H15.3243L13.3243 6.83333C13.1081 6.61111 13 6.34741 13 6.04222C13 5.7363 13.1081 5.47222 13.3243 5.25C13.5225 5.02778 13.7704 4.91667 14.0681 4.91667C14.365 4.91667 14.6126 5.01852 14.8108 5.22222L18.7027 9.22222C18.8108 9.33333 18.8876 9.4537 18.933 9.58333C18.9777 9.71296 19 9.85185 19 10C19 10.1481 18.9777 10.287 18.933 10.4167C18.8876 10.5463 18.8108 10.6667 18.7027 10.7778L14.8108 14.7778C14.5766 15.0185 14.32 15.1248 14.0411 15.0967C13.7614 15.0693 13.5225 14.9537 13.3243 14.75ZM2.16216 20C1.56757 20 1.05838 19.7826 0.634594 19.3478C0.211531 18.9122 0 18.3889 0 17.7778V2.22222C0 1.61111 0.211531 1.08778 0.634594 0.652222C1.05838 0.217407 1.56757 0 2.16216 0H8.64865C8.95495 0 9.21189 0.106296 9.41946 0.318889C9.62631 0.532222 9.72973 0.796296 9.72973 1.11111C9.72973 1.42593 9.62631 1.68963 9.41946 1.90222C9.21189 2.11556 8.95495 2.22222 8.64865 2.22222H2.16216V17.7778H8.64865C8.95495 17.7778 9.21189 17.8844 9.41946 18.0978C9.62631 18.3104 9.72973 18.5741 9.72973 18.8889C9.72973 19.2037 9.62631 19.4674 9.41946 19.68C9.21189 19.8933 8.95495 20 8.64865 20H2.16216Z"
        fill="#637381"
      />
    </svg>
  );
}

export function CrownIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      {...props}
      viewBox="0 0 17 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.7042 3.68707C16.5764 3.5759 16.4183 3.50513 16.25 3.48374C16.0818 3.46236 15.911 3.49132 15.7593 3.56696L12.2026 5.33328L9.23318 0.413728C9.15685 0.28749 9.04911 0.183065 8.92041 0.110577C8.79171 0.0380901 8.64641 0 8.49861 0C8.35081 0 8.20552 0.0380901 8.07682 0.110577C7.94812 0.183065 7.84038 0.28749 7.76405 0.413728L4.79462 5.3354L1.23938 3.56908C1.08803 3.49411 0.917855 3.4654 0.750207 3.48654C0.582558 3.50769 0.424905 3.57775 0.297032 3.68793C0.169158 3.79812 0.0767607 3.94352 0.0314346 4.10589C-0.0138916 4.26826 -0.0101274 4.44037 0.042255 4.60062L2.66317 12.6098C2.68952 12.6903 2.73366 12.7638 2.79232 12.825C2.85097 12.8862 2.92266 12.9334 3.00207 12.9632C3.08149 12.9931 3.16661 13.0048 3.25116 12.9974C3.3357 12.99 3.41751 12.9638 3.49053 12.9207C3.50824 12.9101 5.31951 11.8694 8.49861 11.8694C11.6777 11.8694 13.489 12.9101 13.5053 12.92C13.5783 12.9635 13.6603 12.9901 13.745 12.9977C13.8298 13.0053 13.9152 12.9938 13.9949 12.964C14.0745 12.9342 14.1465 12.8869 14.2053 12.8256C14.2642 12.7643 14.3084 12.6905 14.3348 12.6098L16.9557 4.60273C17.0096 4.44244 17.0144 4.26979 16.9697 4.10674C16.9249 3.94368 16.8325 3.7976 16.7042 3.68707ZM11.8874 9.3824C11.8643 9.51315 11.7957 9.63161 11.6938 9.71701C11.5919 9.8024 11.463 9.84928 11.3299 9.84941C11.2967 9.84937 11.2635 9.84653 11.2307 9.84094C9.42067 9.53101 7.57089 9.53101 5.76082 9.84094C5.68751 9.85383 5.61238 9.8522 5.53971 9.83614C5.46704 9.82008 5.39826 9.78989 5.33728 9.74731C5.21414 9.66132 5.13028 9.53006 5.10417 9.3824C5.07806 9.23474 5.11182 9.08278 5.19804 8.95996C5.28425 8.83713 5.41585 8.75349 5.56389 8.72745C7.50427 8.39491 9.48729 8.39491 11.4277 8.72745C11.5012 8.74 11.5716 8.76693 11.6347 8.80667C11.6978 8.84642 11.7524 8.89821 11.7954 8.95907C11.8384 9.01993 11.8689 9.08865 11.8851 9.1613C11.9014 9.23394 11.9031 9.30908 11.8902 9.3824H11.8874Z"
        fill="#DAAC10"
      />
    </svg>
  );
}

export function OptionsIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        d="M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C9.09826 1 10.1375 1.25292 11.0625 1.7037M13.6875 3.625L7.5625 9.75L5.8125 8"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function WalletIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        d="M1.56207 5.45639H18.4372M3.25001 1.5H16.7497C17.9924 1.5 18.9997 2.51144 18.9997 3.76005L19 12.2401C19 13.4888 17.9927 14.5 16.75 14.5L3.25023 14.4999C2.00762 14.4999 1.00028 13.4877 1.00024 12.2391L1 3.76094C0.999965 2.51227 2.00734 1.5 3.25001 1.5Z"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function SupportIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      viewBox="0 0 31 35"
      fill="none"
      // stroke="black"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M3 20.5V14.3735C3 11.8478 3.74761 9.37858 5.14862 7.27707V7.27707C6.97845 4.53233 9.79778 2.60049 13.018 1.88489L14.3405 1.591C16.0859 1.20314 17.9004 1.25726 19.6195 1.74844V1.74844C22.4191 2.5483 24.7737 4.45076 26.1439 7.01978L26.517 7.7193C27.4907 9.54501 28 11.5823 28 13.6515V20.5"
        strokeWidth="3"
        strokeLinecap="round"
      />
      <path
        d="M2 20.5H4.5V27.5H2C1.17157 27.5 0.5 26.8284 0.5 26V22C0.5 21.1716 1.17157 20.5 2 20.5Z"
        fill="white"
      />
      <path
        d="M29 20.5H26.5V27.5H29C29.8284 27.5 30.5 26.8284 30.5 26V22C30.5 21.1716 29.8284 20.5 29 20.5Z"
        fill="white"
      />
      <path
        d="M17.5 32.5C17.5 33.6046 16.6046 34.5 15.5 34.5C14.3954 34.5 13.5 33.6046 13.5 32.5C13.5 31.3954 14.3954 30.5 15.5 30.5C16.6046 30.5 17.5 31.3954 17.5 32.5Z"
        fill="white"
      />
      <path d="M28.5 27.5C25.5001 31 25 34 17.5 33" />
    </svg>
  );
}

export function ListBulletIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm-.375 5.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
      />
    </svg>
  );
}

export function Cog8ToothIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 011.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.56.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.893.149c-.425.07-.765.383-.93.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 01-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.397.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 01-.12-1.45l.527-.737c.25-.35.273-.806.108-1.204-.165-.397-.505-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.107-1.204l-.527-.738a1.125 1.125 0 01.12-1.45l.773-.773a1.125 1.125 0 011.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      />
    </svg>
  );
}

export function HomeIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
      />
    </svg>
  );
}

export function CheckIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M4.5 12.75l6 6 9-13.5"
      />
    </svg>
  );
}

export function TrashIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
      />
    </svg>
  );
}

export function ChevronDownIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 8.25l-7.5 7.5-7.5-7.5"
      />
    </svg>
  );
}

export function ChevronLeftIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      strokeWidth={1.5}
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15.75 19.5 8.25 12l7.5-7.5"
      />
    </svg>
  );
}

export function DocumentArrowDownIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="#3046D9"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m.75 12l3 3m0 0l3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
      />
    </svg>
  );
}

export function EyeIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      />
    </svg>
  );
}

export function EyeSlashIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88"
      />
    </svg>
  );
}

export function GoogleIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_89_2508)">
        <path
          d="M24.5709 12.2765C24.5709 11.4608 24.5048 10.6406 24.3637 9.83813H13.0449V14.4591H19.5267C19.2577 15.9495 18.3935 17.2679 17.128 18.1056V21.104H20.995C23.2658 19.014 24.5709 15.9274 24.5709 12.2765Z"
          fill="#4285F4"
        />
        <path
          d="M13.0448 24.0008C16.2812 24.0008 19.0106 22.9382 20.9992 21.1039L17.1322 18.1055C16.0564 18.8375 14.6674 19.252 13.0492 19.252C9.91857 19.252 7.26415 17.1399 6.31174 14.3003H2.32129V17.3912C4.3584 21.4434 8.50759 24.0008 13.0448 24.0008Z"
          fill="#34A853"
        />
        <path
          d="M6.30746 14.3002C5.8048 12.8099 5.8048 11.196 6.30746 9.70569V6.61475H2.32142C0.619421 10.0055 0.619421 14.0004 2.32142 17.3912L6.30746 14.3002Z"
          fill="#FBBC04"
        />
        <path
          d="M13.0448 4.74966C14.7556 4.7232 16.4091 5.36697 17.6481 6.54867L21.0742 3.12262C18.9048 1.0855 16.0255 -0.034466 13.0448 0.000808666C8.50758 0.000808666 4.3584 2.55822 2.32129 6.61481L6.30733 9.70575C7.25533 6.86173 9.91416 4.74966 13.0448 4.74966Z"
          fill="#EA4335"
        />
      </g>
      <defs>
        <clipPath id="clip0_89_2508">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0.804688)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function AttachmentIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_89_2508)">
        <path
          id="attachment-icon"
          d="M7 0H16.5C19.5 0 22 2.5 22 5.5C22 8.5 19.5 11 16.5 11H4C1.8 11 0 9.2 0 7C0 4.8 1.8 3 4 3H13.5C14.9 3 16 4.1 16 5.5C16 6.9 14.9 8 13.5 8H7V6H13.5C13.8 6 14.1 5.9 14.1 5.6C14.1 5.6 14.1 5.6 14.1 5.5C14.1 5.2 13.8 5 13.6 5H13.5H4C3 4.9 2 5.6 1.9 6.6C1.9 6.7 1.9 6.9 1.9 7C1.8 8 2.6 8.9 3.6 9C3.7 9 3.9 9 4 9H16.6C18.5 9 20.1 7.5 20.1 5.7C20.1 5.6 20.1 5.6 20.1 5.5C20.1 3.6 18.7 2.1 16.8 2C16.7 2 16.7 2 16.6 2H7V0Z"
          fill="#3046D9"
        />
      </g>
      <defs>
        <clipPath id="clip0_89_2508">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0.804688)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function WithdrawIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="31"
      height="31"
      viewBox="0 0 31 31"
      fill="none"
    >
      <rect
        x="30.582"
        y="30.7795"
        width="30.5821"
        height="30.5822"
        rx="15.2911"
        transform="rotate(-180 30.582 30.7795)"
        fill="#FFA7AC"
        fill-opacity="0.5"
      />
      <path
        d="M11.9396 11.658H18.6428C18.7698 11.658 18.8916 11.6076 18.9813 11.5178C19.0711 11.428 19.1216 11.3062 19.1216 11.1793C19.1216 11.0523 19.0711 10.9305 18.9813 10.8407C18.8916 10.7509 18.7698 10.7005 18.6428 10.7005H11.9396C11.8127 10.7005 11.6909 10.7509 11.6011 10.8407C11.5113 10.9305 11.4609 11.0523 11.4609 11.1793C11.4609 11.3062 11.5113 11.428 11.6011 11.5178C11.6909 11.6076 11.8127 11.658 11.9396 11.658ZM15.6312 12.7545C15.5856 12.7109 15.5319 12.6767 15.4732 12.6539C15.4158 12.6286 15.3539 12.6155 15.2912 12.6155C15.2286 12.6155 15.1666 12.6286 15.1093 12.6539C15.0505 12.6767 14.9968 12.7109 14.9513 12.7545L13.0361 14.6697C12.9459 14.7598 12.8953 14.8821 12.8953 15.0096C12.8953 15.1371 12.9459 15.2594 13.0361 15.3496C13.1262 15.4397 13.2485 15.4904 13.376 15.4904C13.5035 15.4904 13.6258 15.4397 13.716 15.3496L14.8124 14.2483V19.7976C14.8124 19.9246 14.8629 20.0463 14.9527 20.1361C15.0424 20.2259 15.1642 20.2764 15.2912 20.2764C15.4182 20.2764 15.54 20.2259 15.6298 20.1361C15.7196 20.0463 15.77 19.9246 15.77 19.7976V14.2483L16.8665 15.3496C16.9111 15.3942 16.9641 15.4296 17.0224 15.4538C17.0808 15.4779 17.1433 15.4904 17.2064 15.4904C17.2695 15.4904 17.332 15.4779 17.3904 15.4538C17.4487 15.4296 17.5017 15.3942 17.5463 15.3496C17.591 15.3049 17.6264 15.2519 17.6506 15.1936C17.6747 15.1353 17.6872 15.0727 17.6872 15.0096C17.6872 14.9465 17.6747 14.884 17.6506 14.8256C17.6264 14.7673 17.591 14.7143 17.5463 14.6697L15.6312 12.7545Z"
        fill="#E72F2F"
      />
    </svg>
  );
}

export function DepositIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="31"
      height="31"
      viewBox="0 0 31 31"
      fill="none"
    >
      <rect
        y="0.19751"
        width="30.5821"
        height="30.5822"
        rx="15.2911"
        fill="#D1FAE4"
      />
      <path
        d="M18.6424 19.319H11.9392C11.8123 19.319 11.6905 19.3694 11.6007 19.4592C11.5109 19.549 11.4604 19.6708 11.4604 19.7978C11.4604 19.9248 11.5109 20.0466 11.6007 20.1364C11.6905 20.2262 11.8123 20.2766 11.9392 20.2766H18.6424C18.7694 20.2766 18.8912 20.2262 18.9809 20.1364C19.0707 20.0466 19.1212 19.9248 19.1212 19.7978C19.1212 19.6708 19.0707 19.549 18.9809 19.4592C18.8912 19.3694 18.7694 19.319 18.6424 19.319ZM14.9509 18.2226C14.9964 18.2662 15.0501 18.3003 15.1089 18.3231C15.1662 18.3484 15.2282 18.3615 15.2908 18.3615C15.3535 18.3615 15.4154 18.3484 15.4728 18.3231C15.5315 18.3003 15.5852 18.2662 15.6308 18.2226L17.5459 16.3074C17.6361 16.2172 17.6868 16.0949 17.6868 15.9674C17.6868 15.8399 17.6361 15.7176 17.5459 15.6275C17.4558 15.5373 17.3335 15.4867 17.206 15.4867C17.0785 15.4867 16.9562 15.5373 16.8661 15.6275L15.7696 16.7287V11.1795C15.7696 11.0525 15.7192 10.9307 15.6294 10.8409C15.5396 10.7511 15.4178 10.7007 15.2908 10.7007C15.1638 10.7007 15.042 10.7511 14.9523 10.8409C14.8625 10.9307 14.812 11.0525 14.812 11.1795V16.7287L13.7156 15.6275C13.6709 15.5828 13.6179 15.5474 13.5596 15.5233C13.5013 15.4991 13.4388 15.4867 13.3756 15.4867C13.3125 15.4867 13.25 15.4991 13.1917 15.5233C13.1333 15.5474 13.0803 15.5828 13.0357 15.6275C12.991 15.6721 12.9556 15.7251 12.9315 15.7835C12.9073 15.8418 12.8949 15.9043 12.8949 15.9674C12.8949 16.0306 12.9073 16.0931 12.9315 16.1514C12.9556 16.2097 12.991 16.2627 13.0357 16.3074L14.9509 18.2226Z"
        fill="#13BF62"
      />
    </svg>
  );
}
export function ExchangeIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="31"
      height="32"
      viewBox="0 0 31 32"
      fill="none"
    >
      <rect
        y="0.988525"
        width="31"
        height="31"
        rx="15.5"
        fill="#F7931A"
        fill-opacity="0.2"
      />
      <path
        d="M22.6675 12.4479H12.1675C11.2392 12.4479 10.349 12.8166 9.69261 13.473C9.03623 14.1294 8.66748 15.0196 8.66748 15.9479V17.6979M22.6675 16.8229V18.5729C22.6675 19.5011 22.2987 20.3914 21.6424 21.0477C20.986 21.7041 20.0957 22.0729 19.1675 22.0729H8.66748"
        stroke="#F7931A"
        stroke-width="1.3125"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M20.0425 15.0729L22.6675 12.4479L20.0425 9.82288M11.2925 19.4479L8.66748 22.0729L11.2925 24.6979"
        stroke="#F7931A"
        stroke-width="1.3125"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
}

export function OptionIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="39"
      height="39"
      viewBox="0 0 39 39"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect width="39" height="39" rx="10" fill="#E9EDF1" />
      <ellipse cx="19.5" cy="11" rx="1.5" ry="2" fill="#3046D9" />
      <ellipse cx="19.5" cy="20.5" rx="1.5" ry="2.5" fill="#3046D9" />
      <ellipse cx="19.5" cy="31" rx="1.5" ry="2" fill="#3046D9" />
    </svg>
  );
}

export function ReloadIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle
        cx="20"
        cy="20"
        r="19.5"
        fill="#637381"
        fillOpacity="0.1"
        stroke="#FAFAFB"
      />
      <path
        d="M28 19.0007C27.7554 17.2409 26.9391 15.6103 25.6766 14.3602C24.4142 13.11 22.7758 12.3096 21.0137 12.0822C19.2516 11.8549 17.4636 12.2132 15.9252 13.1019C14.3868 13.9907 13.1832 15.3606 12.5 17.0007M12 13.0007V17.0007H16M12 21.0007C12.2446 22.7605 13.0609 24.391 14.3234 25.6412C15.5858 26.8914 17.2242 27.6918 18.9863 27.9191C20.7484 28.1465 22.5364 27.7882 24.0748 26.8994C25.6132 26.0107 26.8168 24.6407 27.5 23.0007M28 27.0007V23.0007H24"
        stroke="#3046D9"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function BitcoinIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="Bitcoin">
        <g id="Bitcoin_2">
          <circle
            id="Oval"
            cx="15.9963"
            cy="15.9963"
            r="15.9963"
            fill="#F5B300"
          />
          <path
            id="Shape"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.6569 7.82156L11.6688 7.8248C11.612 7.86651 11.6232 7.85534 11.6569 7.82156ZM11.7726 7.85242L11.8083 7.72064L11.8083 7.72068C11.7416 7.77071 11.6969 7.8042 11.6688 7.8248C11.7034 7.83419 11.738 7.84339 11.7726 7.85242ZM11.7726 7.85242C11.7088 8.08937 11.6451 8.33387 11.5813 8.57838L11.5812 8.57849L11.5812 8.5785C11.5055 8.86865 11.4299 9.1588 11.3542 9.43633C11.3037 9.53726 11.3542 9.58772 11.4551 9.58772C11.5342 9.60751 11.6153 9.6273 11.698 9.64747L11.6981 9.64749L11.6981 9.64749C12.037 9.73018 12.402 9.81926 12.7671 9.94095C13.2212 10.0923 13.4736 10.5465 13.3726 10.9502C12.7671 13.5742 12.1111 16.1477 11.4551 18.7212L11.4551 18.7213C11.4046 19.0745 11.1019 19.2763 10.6982 19.1754C10.3449 19.1249 9.94124 19.024 9.58801 18.9231C9.43663 18.9231 9.38617 18.9231 9.3357 19.0745C9.18432 19.4782 8.98248 19.9323 8.78064 20.3865L8.78063 20.3865C8.73017 20.4874 8.69232 20.5757 8.65447 20.664C8.61663 20.7523 8.57878 20.8406 8.52832 20.9416C9.7394 21.2443 10.9 21.5471 12.0606 21.8499C12.0606 21.909 12.0433 21.9508 12.029 21.9854C12.0188 22.0099 12.0102 22.0308 12.0102 22.0517C11.7579 22.8591 11.556 23.7169 11.3542 24.5748L11.3542 24.5748C11.3037 24.6757 11.3037 24.7262 11.4551 24.7766L11.4551 24.7767C11.9597 24.8776 12.4643 24.9785 12.9689 25.1299C13.1023 25.1743 13.1182 25.1405 13.1546 25.0627C13.1595 25.0522 13.1648 25.041 13.1708 25.0289C13.2212 24.8019 13.2843 24.5748 13.3474 24.3477L13.3474 24.3477L13.3474 24.3477C13.4105 24.1206 13.4736 23.8936 13.524 23.6665C13.5745 23.4394 13.6376 23.2123 13.7006 22.9853C13.7637 22.7582 13.8268 22.5311 13.8772 22.304C14.3314 22.4049 14.7351 22.5059 15.1388 22.6068C15.2115 22.6432 15.2056 22.6795 15.1967 22.7348C15.1932 22.7562 15.1892 22.7805 15.1892 22.8086C14.9369 23.616 14.7351 24.4739 14.5332 25.3317L14.5332 25.3317C14.4828 25.4326 14.4828 25.4831 14.6342 25.5336C14.8612 25.584 15.1009 25.6471 15.3406 25.7102C15.5803 25.7733 15.82 25.8363 16.0471 25.8868C16.2885 25.9834 16.2989 25.9413 16.3436 25.7606L16.3499 25.7354C16.4508 25.3318 16.5516 24.9156 16.6525 24.4995L16.6526 24.4991L16.6527 24.4987C16.7536 24.0825 16.8545 23.6664 16.9554 23.2628C17.0059 23.1114 17.1068 23.0609 17.2582 23.1114C18.2674 23.2628 19.2766 23.3637 20.3363 23.2123C21.1437 23.1114 21.8502 22.7582 22.4052 22.1022C22.8594 21.5471 23.1117 20.9416 23.3136 20.2351C23.4649 19.68 23.5154 19.0745 23.4145 18.5194C23.2126 17.8129 22.8089 17.2579 22.2034 16.8037C22.1025 16.728 22.0016 16.665 21.9007 16.6019L21.9006 16.6019C21.7997 16.5388 21.6988 16.4757 21.5979 16.4C21.657 16.4 21.6988 16.3827 21.7334 16.3684C21.7579 16.3582 21.7788 16.3496 21.7997 16.3496C22.2034 16.1982 22.6071 15.9963 22.9099 15.6936C23.3136 15.2899 23.5154 14.7853 23.6668 14.2302C23.8686 13.4228 23.8182 12.6154 23.364 11.8585C23.0108 11.3539 22.5566 10.9502 22.052 10.6474L20.3868 9.89049C20.2354 9.84003 20.1849 9.78956 20.2354 9.63818C20.3363 9.38588 20.3868 9.13357 20.4372 8.88127L20.4372 8.88126L20.4373 8.88112C20.5887 8.27563 20.74 7.67013 20.8914 7.01418C20.8662 7.01418 20.8536 7.00156 20.8409 6.98895C20.8283 6.97633 20.8157 6.96372 20.7905 6.96372C20.5513 6.8985 20.3216 6.84264 20.0971 6.78809C19.8006 6.71601 19.5134 6.64619 19.2262 6.56003C19.1252 6.50956 19.0748 6.56003 19.0748 6.66095L18.9234 7.26649C18.7216 7.92249 18.5702 8.52803 18.4188 9.18403C18.402 9.20085 18.3851 9.22327 18.3683 9.2457C18.3347 9.29056 18.301 9.33541 18.2674 9.33541C18.0656 9.28495 17.8511 9.23449 17.6366 9.18403C17.4222 9.13356 17.2077 9.0831 17.0059 9.03264C17.0563 8.93172 17.0563 8.88126 17.0563 8.8308C17.1572 8.42711 17.2582 8.0108 17.3591 7.5945L17.3591 7.59449L17.3591 7.59447C17.46 7.17817 17.5609 6.76187 17.6619 6.35818C17.7123 6.25726 17.7123 6.2068 17.5609 6.15633C17.0563 6.05541 16.6022 5.95449 16.0976 5.8031C15.9462 5.75264 15.8957 5.8031 15.8452 5.95449C15.7443 6.38341 15.6434 6.79972 15.5425 7.21603C15.4416 7.63233 15.3406 8.04864 15.2397 8.47756C15.1892 8.62895 15.1388 8.67941 14.9874 8.62895C14.7099 8.55326 14.4323 8.49018 14.1548 8.4271C13.8772 8.36403 13.5997 8.30095 13.3222 8.22526C13.0446 8.14956 12.7671 8.08649 12.4896 8.02341C12.2506 7.9691 12.0116 7.91478 11.7726 7.85242ZM14.2305 20.2856C14.2809 20.2856 14.3314 20.336 14.3314 20.336C14.4578 20.3676 14.5855 20.4005 14.7142 20.4336C15.4075 20.6119 16.131 20.7981 16.8545 20.8406C17.46 20.8911 18.0151 20.8911 18.5702 20.6893C19.5289 20.3865 19.9326 19.1249 19.3776 18.3176C19.1252 17.9139 18.7216 17.6616 18.3179 17.4093C17.4362 16.9195 16.412 16.7149 15.4297 16.5187L15.3406 16.5009C15.2027 16.455 15.1904 16.4928 15.1511 16.6145L15.1388 16.6523C15.0883 16.9046 15.0252 17.1443 14.9622 17.384L14.9622 17.384C14.8991 17.6237 14.836 17.8634 14.7856 18.1157C14.6705 18.5183 14.5719 18.9208 14.471 19.3327C14.3948 19.6437 14.3173 19.9599 14.2305 20.2856ZM19.0243 15.0376C18.8225 15.088 18.5702 15.1385 18.4692 15.1385C17.8836 15.1385 17.3776 15.0588 16.855 14.9766C16.6571 14.9454 16.4568 14.9139 16.2489 14.8862C16.148 14.8609 16.0597 14.8357 15.9714 14.8105C15.8831 14.7853 15.7948 14.76 15.6939 14.7348C15.5929 14.6843 15.5425 14.6339 15.5929 14.5329C15.8957 13.4733 16.148 12.464 16.4003 11.4043C16.4508 11.2529 16.5012 11.2025 16.6526 11.2529C16.8029 11.293 16.9512 11.3311 17.0979 11.3688C17.6901 11.5209 18.2563 11.6663 18.8225 11.9089C19.2766 12.1108 19.7308 12.4136 19.9831 12.8677C20.4877 13.7256 19.9831 14.8357 19.0243 15.0376Z"
            fill="white"
          />
        </g>
      </g>
    </svg>
  );
}

export function EthereumIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      {...props}
      width="32px"
      height="32px"
      viewBox="0 0 32 32"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Ethereum" fill="none" fillRule="evenodd">
        <circle cx="16" cy="16" r="16" fill="#627EEA" />
        <g fill="#FFFFFF" fillRule="nonzero">
          <path fillOpacity=".602" d="M16.498 4v8.87l7.497 3.35z" />
          <path d="M16.498 4L9 16.22l7.498-3.35z" />
          <path fillOpacity=".602" d="M16.498 21.968v6.027L24 17.616z" />
          <path d="M16.498 27.995v-6.028L9 17.616z" />
          <path fillOpacity=".2" d="M16.498 20.573l7.497-4.353-7.497-3.348z" />
          <path fillOpacity=".602" d="M9 16.22l7.498 4.353v-7.701z" />
        </g>
      </g>
    </svg>
  );
}

export function TronIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      {...props}
      width="32"
      height="32"
      viewBox="0 0 800 800"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_341_2)">
        <path
          d="M400 800C620.914 800 800 620.914 800 400C800 179.086 620.914 0 400 0C179.086 0 0 179.086 0 400C0 620.914 179.086 800 400 800Z"
          fill="#FF0103"
        />
        <path
          d="M662.82 281.574C640.131 265.705 615.869 248.787 592.131 232C591.607 231.607 591.082 231.213 590.426 230.82C587.803 228.853 584.787 226.754 581.115 225.574L580.852 225.443C517.377 210.099 452.853 194.361 390.426 179.148C333.771 165.377 277.246 151.607 220.59 137.836C219.148 137.443 217.705 137.049 216.131 136.656C211.016 135.213 205.115 133.64 198.82 134.426C196.984 134.689 195.41 135.344 193.967 136.262L192.393 137.574C189.902 139.935 188.59 142.951 187.934 144.656L187.541 145.705V151.738L187.803 152.656C223.607 252.984 260.328 354.754 295.738 453.246C323.016 529.049 351.344 607.607 379.016 684.721C380.721 689.967 385.574 693.377 391.607 693.902H392.918C398.557 693.902 403.541 691.148 406.033 686.689L509.902 535.213C535.213 498.361 560.525 461.377 585.836 424.525C596.197 409.443 606.557 394.23 616.918 379.148C633.967 354.23 651.541 328.525 668.984 303.476L669.902 302.164V300.59C670.295 296 670.426 286.558 662.82 281.574ZM541.115 336.787C516.721 349.508 491.803 362.623 466.754 375.607C481.311 360 496 344.263 510.557 328.656C528.787 308.984 547.803 288.656 566.426 268.722L566.82 268.328C568.393 266.23 570.361 264.262 572.459 262.164C573.902 260.722 575.475 259.279 576.918 257.574C586.623 264.262 596.459 271.082 605.902 277.771C612.721 282.623 619.672 287.476 626.754 292.328C597.902 307.017 569.049 322.099 541.115 336.787ZM478.426 330.492C459.672 350.82 440.262 371.672 420.984 392.263C383.607 346.885 345.443 300.853 308.459 256.394C291.672 236.197 274.754 215.869 257.967 195.672L257.836 195.541C254.033 191.213 250.361 186.492 246.689 182.033C244.328 179.017 241.836 176.131 239.344 173.115C254.557 177.049 269.902 180.722 284.984 184.262C298.23 187.41 312 190.689 325.508 194.099C401.574 212.59 477.771 231.082 553.836 249.574C528.525 276.59 503.082 304 478.426 330.492ZM412.459 586.099C413.902 572.328 415.475 558.164 416.787 544.262C417.967 533.115 419.148 521.705 420.328 510.82C422.164 493.377 424.131 475.279 425.705 457.574L426.098 454.426C427.41 443.148 428.721 431.476 429.508 419.803C430.951 419.017 432.525 418.23 434.23 417.574C436.197 416.656 438.164 415.869 440.131 414.689C470.426 398.82 500.721 382.951 531.148 367.213C561.443 351.476 592.525 335.213 623.344 319.082C595.279 359.738 567.082 400.787 539.803 440.787C516.328 475.017 492.197 510.295 468.197 545.049C458.754 558.951 448.918 573.115 439.607 586.754C429.115 601.967 418.361 617.574 407.869 633.181C409.18 617.443 410.754 601.574 412.459 586.099ZM229.639 204.066C227.934 199.344 226.098 194.492 224.525 189.902C259.934 232.656 295.607 275.672 330.361 317.377C348.328 339.017 366.295 360.525 384.262 382.295C387.803 386.361 391.344 390.689 394.754 394.885C399.213 400.263 403.672 405.902 408.525 411.279C406.951 424.787 405.639 438.426 404.197 451.541C403.279 460.721 402.361 469.902 401.312 479.213V479.344C400.918 485.246 400.131 491.148 399.475 496.918C398.557 504.918 396.459 523.017 396.459 523.017L396.328 523.935C393.967 550.426 391.082 577.181 388.328 603.148C387.148 613.902 386.098 624.918 384.918 635.935C384.262 633.967 383.475 632 382.82 630.164C380.852 624.918 378.885 619.41 377.049 614.033L363.016 575.082C318.557 451.41 274.098 327.869 229.639 204.066Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_341_2">
          <rect width="800" height="800" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function TetherIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      {...props}
      width="32"
      height="32"
      viewBox="0 0 111 90"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24.4825 0.862305H88.0496C89.5663 0.862305 90.9675 1.64827 91.7239 2.92338L110.244 34.1419C111.204 35.7609 110.919 37.8043 109.549 39.1171L58.5729 87.9703C56.9216 89.5528 54.2652 89.5528 52.6139 87.9703L1.70699 39.1831C0.305262 37.8398 0.0427812 35.7367 1.07354 34.1077L20.8696 2.82322C21.6406 1.60483 23.0087 0.862305 24.4825 0.862305ZM79.8419 14.8003V23.5597H61.7343V29.6329C74.4518 30.2819 83.9934 32.9475 84.0642 36.1425L84.0638 42.803C83.993 45.998 74.4518 48.6635 61.7343 49.3125V64.2168H49.7105V49.3125C36.9929 48.6635 27.4513 45.998 27.3805 42.803L27.381 36.1425C27.4517 32.9475 36.9929 30.2819 49.7105 29.6329V23.5597H31.6028V14.8003H79.8419ZM55.7224 44.7367C69.2943 44.7367 80.6382 42.4827 83.4143 39.4727C81.0601 36.9202 72.5448 34.9114 61.7343 34.3597V40.7183C59.7966 40.8172 57.7852 40.8693 55.7224 40.8693C53.6595 40.8693 51.6481 40.8172 49.7105 40.7183V34.3597C38.8999 34.9114 30.3846 36.9202 28.0304 39.4727C30.8066 42.4827 42.1504 44.7367 55.7224 44.7367Z"
        fill="#009393"
      />
    </svg>
  );
}

export function DollarIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      {...props}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.9998 1.46582C7.97282 1.46582 1.46582 7.97282 1.46582 15.9998C1.46582 24.0268 7.97282 30.5338 15.9998 30.5338C24.0268 30.5338 30.5338 24.0268 30.5338 15.9998C30.5338 7.97282 24.0268 1.46582 15.9998 1.46582ZM17.2548 23.8798V25.9268H15.2968V23.9028C12.0838 23.4628 10.6768 20.8228 10.6768 20.8228L12.6768 19.1498C12.6768 19.1498 13.9538 21.3728 16.2638 21.3728C17.5398 21.3728 18.5078 20.6898 18.5078 19.5228C18.5078 16.7948 11.1578 17.1258 11.1578 12.0648C11.1578 9.86482 12.8978 8.27982 15.2958 7.90482V5.85982H17.2538V7.90482C18.9258 8.12482 20.9058 9.00482 20.9058 10.8978V12.3498H18.3098V11.6458C18.3098 10.9198 17.3848 10.4358 16.3498 10.4358C15.0298 10.4358 14.0628 11.0958 14.0628 12.0198C14.0628 14.8138 21.4128 14.1318 21.4128 19.4348C21.4128 21.6148 19.7848 23.5048 17.2548 23.8798Z"
        fill="#015697"
      />
    </svg>
  );
}

export function NairaIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      {...props}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="32" height="32" rx="16" fill="#69E708" />
      <circle cx="16.5" cy="15.5" r="11.5" fill="white" />
      <circle cx="16.5" cy="15.5" r="10.5" fill="#4CC800" />
      <circle cx="16.5" cy="15.5" r="9.5" fill="white" />
      <path
        d="M12 13.3333H13.125V10H14.25L16.1738 13.3333H18.75V10H19.875V13.3333H21V14.4444H19.875V15.5556H21V16.6667H19.875V20H18.75L16.8206 16.6667H14.25V20H13.125V16.6667H12V15.5556H13.125V14.4444H12V13.3333ZM14.25 13.3333H14.8856L14.25 12.2389V13.3333ZM14.25 14.4444V15.5556H16.1738L15.5325 14.4444H14.25ZM18.75 17.7778V16.6667H18.1031L18.75 17.7778ZM16.815 14.4444L17.4619 15.5556H18.75V14.4444H16.815Z"
        fill="#56D305"
      />
    </svg>
  );
}

export function USDTIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="32"
      height="28"
      viewBox="0 0 32 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_247_3824)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.85969 0.13752L0.0249644 12.4652C0.00276162 12.5111 -0.00427431 12.5629 0.00488557 12.613C0.0140455 12.6632 0.0389206 12.7091 0.0758733 12.744L15.8341 27.9327C15.8788 27.9759 15.9384 28 16.0005 28C16.0625 28 16.1221 27.9759 16.1668 27.9327L31.925 12.745C31.962 12.71 31.9869 12.6641 31.996 12.614C32.0052 12.5638 31.9981 12.512 31.9759 12.4662L26.1412 0.138468C26.1224 0.0970663 26.0921 0.0619938 26.054 0.0374422C26.0159 0.0128907 25.9715 -0.00010156 25.9263 1.83516e-05H6.07652C6.03104 -0.000550773 5.98637 0.0121285 5.9479 0.0365266C5.90942 0.0609247 5.87879 0.0959994 5.85969 0.13752Z"
          fill="#50AF95"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.0245 13.7312C17.9114 13.7397 17.3269 13.7748 16.023 13.7748C14.986 13.7748 14.2497 13.7435 13.9914 13.7312C9.98373 13.5538 6.99236 12.8521 6.99236 12.0119C6.99236 11.1717 9.98373 10.471 13.9914 10.2908V13.0323C14.2535 13.0512 15.0039 13.0958 16.0409 13.0958C17.2854 13.0958 17.9085 13.0436 18.0207 13.0332V10.2927C22.0199 10.4719 25.0047 11.1736 25.0047 12.0119C25.0047 12.8502 22.0208 13.5519 18.0207 13.7302L18.0245 13.7312ZM18.0245 10.0091V7.55592H23.6056V3.81494H8.41027V7.55592H13.9904V10.0082C9.45484 10.2178 6.04395 11.1215 6.04395 12.2044C6.04395 13.2874 9.45484 14.1901 13.9904 14.4006V22.2619H18.0236V14.3978C22.5488 14.1882 25.954 13.2855 25.954 12.2035C25.954 11.1215 22.5516 10.2187 18.0236 10.0082L18.0245 10.0091Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_247_3824">
          <rect width="32" height="28" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function CopyIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      viewBox="0 0 15 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="copy">
        <path
          id="Path"
          d="M5.96597 13.2955C4.36845 13.2955 3.06819 11.9953 3.06819 10.3978V3.40918H1.87505C0.840706 3.40918 0 4.24976 0 5.2841V14.4887C0 15.523 0.840706 16.3637 1.87505 16.3637H10.3977C11.4321 16.3637 12.2728 15.523 12.2728 14.4887V13.2955H5.96597Z"
          fill="#283696"
        />
        <path
          id="Path_2"
          d="M14.9999 1.87505C14.9999 0.839333 14.1606 0 13.125 0H5.96587C4.93015 0 4.09082 0.839333 4.09082 1.87505V10.3977C4.09082 11.4334 4.93015 12.2728 5.96587 12.2728H13.125C14.1606 12.2728 14.9999 11.4334 14.9999 10.3977V1.87505Z"
          fill="#3046D9"
        />
      </g>
    </svg>
  );
}

export function DocumentDuplicateIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      strokeWidth={1.5}
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
      />
    </svg>
  );
}

export function CloseIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="16" cy="16" r="16" fill="#E9EDF1" />
      <path
        d="M22 10L10 22M22 22L10 10"
        stroke="#3046D9"
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  );
}

export function Hamburger(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="32"
      height="21"
      viewBox="0 0 32 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="menu-icon">
        <rect id="Rectangle 19" width="32" height="3" rx="1.5" fill="#2e4050" />
        <rect
          id="Rectangle 19 Copy"
          y="9"
          width="32"
          height="3"
          rx="1.5"
          fill="#2e4050"
        />
        <rect
          id="Rectangle 19 Copy 2"
          y="18"
          width="32"
          height="3"
          rx="1.5"
          fill="#2e4050"
        />
      </g>
    </svg>
  );
}

export function XMarkIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      {...props}
      aria-hidden="true"
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 18L18 6M6 6l12 12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function ChevronRightIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M8.25 4.5l7.5 7.5-7.5 7.5"
      />
    </svg>
  );
}

export function Spinner(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      {...props}
      aria-hidden="true"
      className="w-8 h-8 mr-2 text-[#637381] animate-spin dark:text-[#fff] fill-[#637381]"
      viewBox="0 0 100 101"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
        fill="currentColor"
      />
      <path
        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
        fill="currentFill"
      />
    </svg>
  );
}
export function SelectIcon(props: SVGAttributes<SVGElement>) {
  <svg
    {...props}
    width="13"
    height="14"
    viewBox="0 0 13 14"
    fill="E9EDF1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.75 7.83333L6.375 12M6.375 12L2 7.83333M6.375 12L6.375 2"
      stroke="#444971"
      stroke-width="3"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>;
}

export function UsersIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"
      />
    </svg>
  );
}

export function UserGroupIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"
      />
    </svg>
  );
}

export function BanknotesIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z"
      />
    </svg>
  );
}

export function CalendarIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      stroke="currentColor"
      strokeWidth={1.5}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5"
      />
    </svg>
  );
}

export function EditIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
      />
    </svg>
  );
}

export function EmailIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="169"
      height="207"
      viewBox="0 0 169 207"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="freepik--Message--inject-47">
        <path
          id="Vector"
          d="M150.173 120.803L155.803 186.015C156.253 191.323 153.012 195.98 148.564 196.358L24.8156 206.974C20.392 207.361 16.3946 203.323 15.9684 198.023L10.3383 132.803C10.2266 131.578 10.3217 130.344 10.6198 129.151C11.5045 125.508 14.1909 122.717 17.5528 122.467L141.301 111.843C144.655 111.553 147.736 113.813 149.272 117.24C149.767 118.369 150.071 119.573 150.173 120.803Z"
          fill="#50C0E9"
        />
        <path
          id="Vector_2"
          opacity="0.1"
          d="M150.173 120.803L155.803 186.015C156.253 191.323 153.012 195.98 148.564 196.358L24.8156 206.974C20.392 207.361 16.3946 203.323 15.9684 198.023L10.3383 132.803C10.2266 131.578 10.3217 130.344 10.6198 129.151C11.5045 125.508 14.1909 122.717 17.5528 122.467L141.301 111.843C144.655 111.553 147.736 113.813 149.272 117.24C149.767 118.369 150.071 119.573 150.173 120.803Z"
          fill="black"
        />
        <path
          id="Vector_3"
          d="M164.863 159.245L163.984 159.351L164.339 162.289L165.218 162.183L164.863 159.245Z"
          fill="#50C0E9"
        />
        <path
          id="Vector_4"
          d="M163.15 145.058L162.271 145.164L163.594 156.111L164.473 156.005L163.15 145.058Z"
          fill="#50C0E9"
        />
        <path
          id="Vector_5"
          d="M155.714 109.14L163.805 176.025C164.456 181.462 161.287 186.368 156.751 186.915L29.8419 202.269C25.3057 202.816 21.051 198.811 20.3915 193.366L12.3003 126.489C12.1463 125.231 12.2007 123.957 12.4611 122.717C13.2654 118.945 15.9035 116.009 19.3539 115.591L146.263 100.245C149.714 99.8266 152.995 102.038 154.66 105.513C155.209 106.656 155.565 107.881 155.714 109.14Z"
          fill="#50C0E9"
        />
        <path
          id="Vector_6"
          opacity="0.1"
          d="M154.661 105.513L97.0979 144.272C94.2807 146.197 91.0415 147.417 87.6538 147.827C84.2661 148.237 80.8294 147.826 77.634 146.628L12.4863 122.717C13.2906 118.945 15.9287 116.009 19.3791 115.591L146.288 100.245C149.715 99.8266 152.996 102.038 154.661 105.513Z"
          fill="black"
        />
        <path
          id="Vector_7"
          d="M133.186 119.966L116.65 131.098L111.237 134.749L97.0976 144.272C94.3533 146.155 91.2039 147.365 87.9047 147.804C84.6055 148.242 81.2494 147.898 78.1083 146.797L77.6016 146.628L69.6713 143.717L51.8159 137.162L43.4674 134.098L34.0089 130.623L33.3253 113.902L31.7891 76.7434L104.441 73.7917L132.133 94.7757L132.422 101.91L133.186 119.966Z"
          fill="#50C0E9"
        />
        <path
          id="Vector_8"
          opacity="0.7"
          d="M133.186 119.966L116.65 131.098L111.237 134.749L97.0976 144.272C94.3533 146.155 91.2039 147.365 87.9047 147.804C84.6055 148.242 81.2494 147.898 78.1083 146.797L77.6016 146.628L69.6713 143.717L51.8159 137.162L43.4674 134.098L34.0089 130.623L33.3253 113.902L31.7891 76.7434L104.441 73.7917L132.133 94.7757L132.422 101.91L133.186 119.966Z"
          fill="#FAFAFA"
        />
        <g id="Group" opacity="0.9">
          <path
            id="Vector_9"
            d="M122.946 102.167L42.517 105.433C42.0684 105.45 41.6306 105.292 41.2971 104.991C40.9635 104.691 40.7605 104.272 40.7315 103.824C40.7159 103.377 40.8752 102.941 41.1755 102.609C41.4758 102.277 41.8935 102.075 42.3401 102.046L122.769 98.781C123.218 98.7633 123.656 98.9216 123.989 99.2221C124.323 99.5226 124.526 99.9416 124.555 100.39C124.573 100.837 124.414 101.274 124.113 101.607C123.813 101.939 123.394 102.14 122.946 102.167Z"
            fill="white"
          />
          <path
            id="Vector_10"
            d="M123.372 112.856L42.9428 116.122C42.4942 116.139 42.0564 115.981 41.7229 115.681C41.3893 115.38 41.1863 114.961 41.1573 114.513C41.1416 114.065 41.3006 113.628 41.6007 113.295C41.9008 112.962 42.3186 112.758 42.7659 112.727L123.195 109.462C123.643 109.446 124.08 109.605 124.413 109.905C124.746 110.206 124.95 110.623 124.981 111.071C124.994 111.518 124.835 111.954 124.535 112.286C124.235 112.619 123.819 112.823 123.372 112.856Z"
            fill="white"
          />
          <path
            id="Vector_11"
            d="M123.808 123.537L43.3787 126.803C42.93 126.82 42.4922 126.662 42.1587 126.362C41.8251 126.061 41.6221 125.642 41.5931 125.194C41.5755 124.746 41.734 124.309 42.0347 123.977C42.3354 123.645 42.7544 123.444 43.2017 123.417L123.631 120.151C124.079 120.133 124.516 120.292 124.848 120.593C125.18 120.893 125.381 121.312 125.408 121.76C125.426 122.206 125.269 122.642 124.97 122.974C124.671 123.306 124.254 123.508 123.808 123.537Z"
            fill="white"
          />
          <path
            id="Vector_12"
            d="M116.647 131.097L111.235 134.749L51.8134 137.162L43.4648 134.097C43.5343 134.089 43.6045 134.089 43.674 134.097L116.647 131.097Z"
            fill="white"
          />
          <path
            id="Vector_13"
            d="M94.6362 144.409V144.473C94.6371 144.911 94.4713 145.332 94.1725 145.652C93.8737 145.972 93.4643 146.165 93.0276 146.194L78.1401 146.797L77.6334 146.628L69.7031 143.717L92.8909 142.768C93.3379 142.761 93.77 142.928 94.0958 143.235C94.4215 143.541 94.6154 143.962 94.6362 144.409Z"
            fill="white"
          />
        </g>
        <path
          id="Vector_14"
          opacity="0.1"
          d="M132.162 94.7757L112.924 93.9473L104.471 73.7917L132.162 94.7757Z"
          fill="#263238"
        />
        <path
          id="Vector_15"
          d="M132.162 94.7757L113.495 91.5826L104.471 73.7917L132.162 94.7757Z"
          fill="#50C0E9"
        />
        <path
          id="Vector_16"
          opacity="0.1"
          d="M144.726 12.6976C140.994 16.7914 135.927 18.2552 133.41 15.9147C130.892 13.5743 131.873 8.44287 135.597 4.34902C139.321 0.255169 144.396 -1.20865 146.922 1.08359C149.447 3.37582 148.458 8.60372 144.726 12.6976Z"
          fill="white"
        />
        <path
          id="Vector_17"
          opacity="0.1"
          d="M133.731 29.1775C133.241 27.955 133.369 26.7647 134.021 26.4912C134.672 26.2177 135.63 27.0059 136.088 28.2285C136.546 29.451 136.45 30.6413 135.798 30.9068C135.147 31.1722 134.222 30.392 133.731 29.1775Z"
          fill="white"
        />
        <path
          id="Vector_18"
          d="M149.504 48.569H147.606C147.45 48.569 147.324 48.695 147.324 48.8505V49.0516C147.324 49.207 147.45 49.3331 147.606 49.3331H149.504C149.659 49.3331 149.785 49.207 149.785 49.0516V48.8505C149.785 48.695 149.659 48.569 149.504 48.569Z"
          fill="#263238"
        />
        <path
          id="Vector_19"
          opacity="0.1"
          d="M15.875 28.6493C19.6051 24.5543 20.587 19.3748 18.0683 17.0805C15.5496 14.7863 10.4839 16.246 6.75387 20.341C3.02381 24.436 2.04183 29.6155 4.56055 31.9098C7.07928 34.204 12.1449 32.7443 15.875 28.6493Z"
          fill="white"
        />
        <path
          id="Vector_20"
          opacity="0.1"
          d="M4.87788 45.1267C4.38726 43.9041 4.51595 42.7138 5.16742 42.4403C5.8189 42.1669 6.77601 42.9551 7.23446 44.1776C7.69291 45.4001 7.59639 46.5905 6.94491 46.8639C6.29344 47.1374 5.36849 46.3492 4.87788 45.1267Z"
          fill="white"
        />
        <path
          id="Vector_21"
          d="M20.1328 63.8506H18.2346C18.0792 63.8506 17.9531 63.9766 17.9531 64.1321V64.3331C17.9531 64.4886 18.0792 64.6146 18.2346 64.6146H20.1328C20.2882 64.6146 20.4143 64.4886 20.4143 64.3331V64.1321C20.4143 63.9766 20.2882 63.8506 20.1328 63.8506Z"
          fill="#50C0E9"
        />
        <path
          id="Vector_22"
          d="M159.715 115.619C164.586 111.146 164.909 103.57 160.436 98.6982C155.962 93.8264 148.386 93.5036 143.514 97.9771C138.643 102.451 138.32 110.027 142.793 114.898C147.267 119.77 154.843 120.093 159.715 115.619Z"
          fill="#DE5753"
        />
        <path
          id="Vector_23"
          d="M153.994 101.017V113.081H150.664V105.183C150.172 105.562 149.647 105.896 149.095 106.181C148.483 106.472 147.848 106.714 147.197 106.904V104.21C148.141 103.94 149.027 103.498 149.811 102.907C150.43 102.398 150.927 101.758 151.267 101.033L153.994 101.017Z"
          fill="white"
        />
      </g>
    </svg>
  );
}

export const ShieldIcon = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="49"
      height="49"
      viewBox="0 0 49 49"
      fill="none"
    >
      <path
        d="M39.6925 8.26705C39.4602 8.07909 39.1886 7.94559 38.8979 7.87637C38.6072 7.80715 38.3046 7.80397 38.0125 7.86705C35.8754 8.31493 33.6692 8.32064 31.5298 7.88381C29.3904 7.44699 27.363 6.57688 25.5725 5.32705C25.2377 5.09479 24.84 4.97034 24.4325 4.97034C24.025 4.97034 23.6273 5.09479 23.2925 5.32705C21.502 6.57688 19.4747 7.44699 17.3352 7.88381C15.1958 8.32064 12.9896 8.31493 10.8525 7.86705C10.5604 7.80397 10.2578 7.80715 9.96711 7.87637C9.67638 7.94559 9.40486 8.07909 9.17251 8.26705C8.94047 8.45528 8.75355 8.69312 8.62549 8.96308C8.49743 9.23304 8.43149 9.52826 8.43251 9.82705V24.727C8.43073 27.5945 9.11405 30.421 10.4256 32.971C11.7371 35.521 13.6389 37.7207 15.9725 39.387L23.2725 44.587C23.6112 44.8282 24.0167 44.9578 24.4325 44.9578C24.8483 44.9578 25.2538 44.8282 25.5925 44.587L32.8925 39.387C35.2261 37.7207 37.1279 35.521 38.4394 32.971C39.751 30.421 40.4343 27.5945 40.4325 24.727V9.82705C40.4335 9.52826 40.3676 9.23304 40.2395 8.96308C40.1115 8.69312 39.9245 8.45528 39.6925 8.26705ZM36.4325 24.727C36.434 26.9566 35.9031 29.1542 34.8838 31.1371C33.8645 33.12 32.3864 34.8307 30.5725 36.127L24.4325 40.507L18.2925 36.127C16.4786 34.8307 15.0005 33.12 13.9812 31.1371C12.9619 29.1542 12.431 26.9566 12.4325 24.727V12.127C16.6254 12.4859 20.8246 11.5131 24.4325 9.34705C28.0404 11.5131 32.2396 12.4859 36.4325 12.127V24.727ZM27.5125 20.147L22.1325 25.547L20.3525 23.747C19.9759 23.3704 19.4651 23.1589 18.9325 23.1589C18.3999 23.1589 17.8891 23.3704 17.5125 23.747C17.1359 24.1237 16.9243 24.6344 16.9243 25.167C16.9243 25.6997 17.1359 26.2104 17.5125 26.587L20.7125 29.787C20.8984 29.9745 21.1196 30.1233 21.3634 30.2248C21.6071 30.3264 21.8685 30.3786 22.1325 30.3786C22.3965 30.3786 22.6579 30.3264 22.9017 30.2248C23.1454 30.1233 23.3666 29.9745 23.5525 29.787L30.4325 22.967C30.8091 22.5904 31.0207 22.0796 31.0207 21.547C31.0207 21.0144 30.8091 20.5037 30.4325 20.127C30.0559 19.7504 29.5451 19.5389 29.0125 19.5389C28.4799 19.5389 27.9691 19.7504 27.5925 20.127L27.5125 20.147Z"
        fill="#71BBFF"
      />
    </svg>
  );
};

export const MoneyInsertIcon = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="49"
      height="49"
      viewBox="0 0 49 49"
      fill="none"
    >
      <path
        d="M20.9598 12.9671L22.0398 11.7871V18.9671C22.0398 19.4975 22.2505 20.0062 22.6256 20.3813C23.0007 20.7564 23.5094 20.9671 24.0398 20.9671C24.5702 20.9671 25.0789 20.7564 25.454 20.3813C25.8291 20.0062 26.0398 19.4975 26.0398 18.9671V11.7871L27.1198 12.8871C27.3013 13.0921 27.5231 13.2577 27.7712 13.3735C28.0194 13.4894 28.2887 13.553 28.5624 13.5605C28.8362 13.568 29.1086 13.5192 29.3627 13.4171C29.6168 13.3151 29.8473 13.1619 30.0398 12.9671C30.2273 12.7812 30.376 12.5599 30.4776 12.3162C30.5791 12.0725 30.6314 11.8111 30.6314 11.5471C30.6314 11.2831 30.5791 11.0216 30.4776 10.7779C30.376 10.5342 30.2273 10.313 30.0398 10.1271L25.4598 5.54708C25.2696 5.365 25.0453 5.22227 24.7998 5.12708C24.3129 4.92704 23.7667 4.92704 23.2798 5.12708C23.0343 5.22227 22.81 5.365 22.6198 5.54708L18.0398 10.0471C17.6526 10.4343 17.435 10.9595 17.435 11.5071C17.435 12.0547 17.6526 12.5799 18.0398 12.9671C18.427 13.3543 18.9522 13.5718 19.4998 13.5718C20.0474 13.5718 20.5726 13.3543 20.9598 12.9671V12.9671ZM24.0398 24.9671C22.8531 24.9671 21.6931 25.319 20.7064 25.9783C19.7197 26.6375 18.9506 27.5746 18.4965 28.671C18.0424 29.7673 17.9236 30.9737 18.1551 32.1376C18.3866 33.3015 18.958 34.3706 19.7972 35.2097C20.6363 36.0488 21.7054 36.6203 22.8693 36.8518C24.0331 37.0833 25.2395 36.9645 26.3359 36.5104C27.4323 36.0562 28.3693 35.2872 29.0286 34.3005C29.6879 33.3138 30.0398 32.1538 30.0398 30.9671C30.0398 29.3758 29.4077 27.8497 28.2824 26.7244C27.1572 25.5992 25.6311 24.9671 24.0398 24.9671ZM24.0398 32.9671C23.6442 32.9671 23.2576 32.8498 22.9287 32.63C22.5998 32.4103 22.3434 32.0979 22.192 31.7324C22.0407 31.367 22.0011 30.9649 22.0782 30.5769C22.1554 30.1889 22.3459 29.8326 22.6256 29.5529C22.9053 29.2732 23.2617 29.0827 23.6496 29.0055C24.0376 28.9283 24.4397 28.9679 24.8052 29.1193C25.1706 29.2707 25.483 29.527 25.7027 29.8559C25.9225 30.1848 26.0398 30.5715 26.0398 30.9671C26.0398 31.4975 25.8291 32.0062 25.454 32.3813C25.0789 32.7564 24.5702 32.9671 24.0398 32.9671ZM10.0398 30.9671C10.0398 31.3626 10.1571 31.7493 10.3769 32.0782C10.5966 32.4071 10.909 32.6635 11.2744 32.8148C11.6399 32.9662 12.042 33.0058 12.43 32.9286C12.8179 32.8515 13.1743 32.661 13.454 32.3813C13.7337 32.1016 13.9242 31.7452 14.0014 31.3573C14.0785 30.9693 14.0389 30.5672 13.8876 30.2017C13.7362 29.8363 13.4798 29.5239 13.1509 29.3041C12.822 29.0844 12.4354 28.9671 12.0398 28.9671C11.5094 28.9671 11.0007 29.1778 10.6256 29.5529C10.2505 29.9279 10.0398 30.4366 10.0398 30.9671ZM38.0398 30.9671C38.0398 30.5715 37.9225 30.1848 37.7027 29.8559C37.483 29.527 37.1706 29.2707 36.8052 29.1193C36.4397 28.9679 36.0376 28.9283 35.6496 29.0055C35.2617 29.0827 34.9053 29.2732 34.6256 29.5529C34.3459 29.8326 34.1554 30.1889 34.0782 30.5769C34.0011 30.9649 34.0407 31.367 34.192 31.7324C34.3434 32.0979 34.5998 32.4103 34.9287 32.63C35.2576 32.8498 35.6442 32.9671 36.0398 32.9671C36.5702 32.9671 37.0789 32.7564 37.454 32.3813C37.8291 32.0062 38.0398 31.4975 38.0398 30.9671ZM40.0398 16.9671H32.0398C31.5094 16.9671 31.0007 17.1778 30.6256 17.5529C30.2505 17.9279 30.0398 18.4366 30.0398 18.9671C30.0398 19.4975 30.2505 20.0062 30.6256 20.3813C31.0007 20.7564 31.5094 20.9671 32.0398 20.9671H40.0398C40.5702 20.9671 41.0789 21.1778 41.454 21.5529C41.8291 21.9279 42.0398 22.4366 42.0398 22.9671V38.9671C42.0398 39.4975 41.8291 40.0062 41.454 40.3813C41.0789 40.7564 40.5702 40.9671 40.0398 40.9671H8.03979C7.50936 40.9671 7.00065 40.7564 6.62558 40.3813C6.25051 40.0062 6.03979 39.4975 6.03979 38.9671V22.9671C6.03979 22.4366 6.25051 21.9279 6.62558 21.5529C7.00065 21.1778 7.50936 20.9671 8.03979 20.9671H16.0398C16.5702 20.9671 17.0789 20.7564 17.454 20.3813C17.8291 20.0062 18.0398 19.4975 18.0398 18.9671C18.0398 18.4366 17.8291 17.9279 17.454 17.5529C17.0789 17.1778 16.5702 16.9671 16.0398 16.9671H8.03979C6.4485 16.9671 4.92237 17.5992 3.79715 18.7244C2.67194 19.8497 2.03979 21.3758 2.03979 22.9671V38.9671C2.03979 40.5584 2.67194 42.0845 3.79715 43.2097C4.92237 44.3349 6.4485 44.9671 8.03979 44.9671H40.0398C41.6311 44.9671 43.1572 44.3349 44.2824 43.2097C45.4077 42.0845 46.0398 40.5584 46.0398 38.9671V22.9671C46.0398 21.3758 45.4077 19.8497 44.2824 18.7244C43.1572 17.5992 41.6311 16.9671 40.0398 16.9671Z"
        fill="#71BBFF"
      />
    </svg>
  );
};

export const PlugIcon = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="49"
      height="49"
      viewBox="0 0 49 49"
      fill="none"
    >
      <path
        d="M38.4325 12.3291H32.4325V6.3291C32.4325 5.79867 32.2218 5.28996 31.8467 4.91489C31.4716 4.53982 30.9629 4.3291 30.4325 4.3291C29.9021 4.3291 29.3934 4.53982 29.0183 4.91489C28.6432 5.28996 28.4325 5.79867 28.4325 6.3291V12.3291H20.4325V6.3291C20.4325 5.79867 20.2218 5.28996 19.8467 4.91489C19.4716 4.53982 18.9629 4.3291 18.4325 4.3291C17.9021 4.3291 17.3934 4.53982 17.0183 4.91489C16.6432 5.28996 16.4325 5.79867 16.4325 6.3291V12.3291H10.4325C9.90206 12.3291 9.39335 12.5398 9.01828 12.9149C8.64321 13.29 8.4325 13.7987 8.4325 14.3291C8.4325 14.8595 8.64321 15.3682 9.01828 15.7433C9.39335 16.1184 9.90206 16.3291 10.4325 16.3291H12.4325V26.3291C12.431 26.5923 12.4814 26.8532 12.581 27.0969C12.6805 27.3406 12.8271 27.5622 13.0125 27.7491L18.4325 33.1491V42.3291C18.4325 42.8595 18.6432 43.3682 19.0183 43.7433C19.3934 44.1184 19.9021 44.3291 20.4325 44.3291C20.9629 44.3291 21.4716 44.1184 21.8467 43.7433C22.2218 43.3682 22.4325 42.8595 22.4325 42.3291V34.3291H26.4325V42.3291C26.4325 42.8595 26.6432 43.3682 27.0183 43.7433C27.3934 44.1184 27.9021 44.3291 28.4325 44.3291C28.9629 44.3291 29.4716 44.1184 29.8467 43.7433C30.2218 43.3682 30.4325 42.8595 30.4325 42.3291V33.1491L35.8525 27.7491C36.0379 27.5622 36.1845 27.3406 36.284 27.0969C36.3836 26.8532 36.434 26.5923 36.4325 26.3291V16.3291H38.4325C38.9629 16.3291 39.4716 16.1184 39.8467 15.7433C40.2218 15.3682 40.4325 14.8595 40.4325 14.3291C40.4325 13.7987 40.2218 13.29 39.8467 12.9149C39.4716 12.5398 38.9629 12.3291 38.4325 12.3291ZM32.4325 25.5091L27.6125 30.3291H21.2525L16.4325 25.5091V16.3291H32.4325V25.5091ZM22.4325 26.3291H26.4325C26.9629 26.3291 27.4716 26.1184 27.8467 25.7433C28.2218 25.3682 28.4325 24.8595 28.4325 24.3291C28.4325 23.7987 28.2218 23.29 27.8467 22.9149C27.4716 22.5398 26.9629 22.3291 26.4325 22.3291H22.4325C21.9021 22.3291 21.3934 22.5398 21.0183 22.9149C20.6432 23.29 20.4325 23.7987 20.4325 24.3291C20.4325 24.8595 20.6432 25.3682 21.0183 25.7433C21.3934 26.1184 21.9021 26.3291 22.4325 26.3291V26.3291Z"
        fill="#71BBFF"
      />
    </svg>
  );
};

export const WalletIcon1 = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="49"
      height="49"
      viewBox="0 0 49 49"
      fill="none"
    >
      <path
        d="M38.0398 14.3291H36.0398V12.3291C36.0398 10.7378 35.4077 9.21168 34.2824 8.08646C33.1572 6.96124 31.6311 6.3291 30.0398 6.3291H10.0398C8.4485 6.3291 6.92237 6.96124 5.79715 8.08646C4.67194 9.21168 4.03979 10.7378 4.03979 12.3291V36.3291C4.03979 37.9204 4.67194 39.4465 5.79715 40.5717C6.92237 41.697 8.4485 42.3291 10.0398 42.3291H38.0398C39.6311 42.3291 41.1572 41.697 42.2824 40.5717C43.4077 39.4465 44.0398 37.9204 44.0398 36.3291V20.3291C44.0398 18.7378 43.4077 17.2117 42.2824 16.0865C41.1572 14.9612 39.6311 14.3291 38.0398 14.3291ZM10.0398 10.3291H30.0398C30.5702 10.3291 31.0789 10.5398 31.454 10.9149C31.8291 11.29 32.0398 11.7987 32.0398 12.3291V14.3291H10.0398C9.50936 14.3291 9.00065 14.1184 8.62558 13.7433C8.25051 13.3682 8.03979 12.8595 8.03979 12.3291C8.03979 11.7987 8.25051 11.29 8.62558 10.9149C9.00065 10.5398 9.50936 10.3291 10.0398 10.3291ZM40.0398 30.3291H38.0398C37.5094 30.3291 37.0007 30.1184 36.6256 29.7433C36.2505 29.3682 36.0398 28.8595 36.0398 28.3291C36.0398 27.7987 36.2505 27.29 36.6256 26.9149C37.0007 26.5398 37.5094 26.3291 38.0398 26.3291H40.0398V30.3291ZM40.0398 22.3291H38.0398C36.4485 22.3291 34.9224 22.9612 33.7972 24.0865C32.6719 25.2117 32.0398 26.7378 32.0398 28.3291C32.0398 29.9204 32.6719 31.4465 33.7972 32.5717C34.9224 33.697 36.4485 34.3291 38.0398 34.3291H40.0398V36.3291C40.0398 36.8595 39.8291 37.3682 39.454 37.7433C39.0789 38.1184 38.5702 38.3291 38.0398 38.3291H10.0398C9.50936 38.3291 9.00065 38.1184 8.62558 37.7433C8.25051 37.3682 8.03979 36.8595 8.03979 36.3291V17.9891C8.68233 18.2151 9.35866 18.3301 10.0398 18.3291H38.0398C38.5702 18.3291 39.0789 18.5398 39.454 18.9149C39.8291 19.29 40.0398 19.7987 40.0398 20.3291V22.3291Z"
        fill="#71BBFF"
      />
    </svg>
  );
};

export const LovedIcon = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="20"
      viewBox="0 0 22 20"
      fill="none"
    >
      <path
        d="M8.73979 17.9973C14.5529 14.7311 15.8857 10.9061 14.8398 8.29188C14.3284 7.0135 13.2566 6.10084 11.9966 5.81923C10.851 5.56317 9.58808 5.83678 8.49484 6.76107C7.40158 5.83678 6.13865 5.56317 4.99303 5.81923C3.73312 6.10085 2.66131 7.01351 2.14988 8.2919C1.10402 10.9061 2.43677 14.7311 8.24995 17.9973C8.40205 18.0828 8.58769 18.0828 8.73979 17.9973Z"
        fill="#FF5C00"
      />
      <path
        d="M18.5117 5.9688C21.5619 4.37985 22.2613 2.51906 21.7125 1.24727C21.4441 0.625359 20.8818 0.181365 20.2206 0.0443668C19.6195 -0.080202 18.9568 0.0529045 18.3832 0.502556C17.8095 0.0529045 17.1468 -0.080202 16.5457 0.0443668C15.8846 0.18137 15.3222 0.625363 15.0539 1.24728C14.5051 2.51906 15.2044 4.37985 18.2547 5.9688C18.3345 6.0104 18.4319 6.0104 18.5117 5.9688Z"
        fill="#FF5C00"
      />
    </svg>
  );
};

/* New Icons */

export const OverviewIcon = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="black"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M7 10H1C0.734784 10 0.48043 10.1054 0.292893 10.2929C0.105357 10.4804 0 10.7348 0 11V17C0 17.2652 0.105357 17.5196 0.292893 17.7071C0.48043 17.8946 0.734784 18 1 18H7C7.26522 18 7.51957 17.8946 7.70711 17.7071C7.89464 17.5196 8 17.2652 8 17V11C8 10.7348 7.89464 10.4804 7.70711 10.2929C7.51957 10.1054 7.26522 10 7 10ZM6 16H2V12H6V16ZM17 0H11C10.7348 0 10.4804 0.105357 10.2929 0.292893C10.1054 0.48043 10 0.734784 10 1V7C10 7.26522 10.1054 7.51957 10.2929 7.70711C10.4804 7.89464 10.7348 8 11 8H17C17.2652 8 17.5196 7.89464 17.7071 7.70711C17.8946 7.51957 18 7.26522 18 7V1C18 0.734784 17.8946 0.48043 17.7071 0.292893C17.5196 0.105357 17.2652 0 17 0ZM16 6H12V2H16V6ZM17 13H15V11C15 10.7348 14.8946 10.4804 14.7071 10.2929C14.5196 10.1054 14.2652 10 14 10C13.7348 10 13.4804 10.1054 13.2929 10.2929C13.1054 10.4804 13 10.7348 13 11V13H11C10.7348 13 10.4804 13.1054 10.2929 13.2929C10.1054 13.4804 10 13.7348 10 14C10 14.2652 10.1054 14.5196 10.2929 14.7071C10.4804 14.8946 10.7348 15 11 15H13V17C13 17.2652 13.1054 17.5196 13.2929 17.7071C13.4804 17.8946 13.7348 18 14 18C14.2652 18 14.5196 17.8946 14.7071 17.7071C14.8946 17.5196 15 17.2652 15 17V15H17C17.2652 15 17.5196 14.8946 17.7071 14.7071C17.8946 14.5196 18 14.2652 18 14C18 13.7348 17.8946 13.4804 17.7071 13.2929C17.5196 13.1054 17.2652 13 17 13ZM7 0H1C0.734784 0 0.48043 0.105357 0.292893 0.292893C0.105357 0.48043 0 0.734784 0 1V7C0 7.26522 0.105357 7.51957 0.292893 7.70711C0.48043 7.89464 0.734784 8 1 8H7C7.26522 8 7.51957 7.89464 7.70711 7.70711C7.89464 7.51957 8 7.26522 8 7V1C8 0.734784 7.89464 0.48043 7.70711 0.292893C7.51957 0.105357 7.26522 0 7 0ZM6 6H2V2H6V6Z" />
    </svg>
  );
};
export const WalletIcon2 = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19 7H18V6C18 5.20435 17.6839 4.44129 17.1213 3.87868C16.5587 3.31607 15.7956 3 15 3H5C4.20435 3 3.44129 3.31607 2.87868 3.87868C2.31607 4.44129 2 5.20435 2 6V18C2 18.7956 2.31607 19.5587 2.87868 20.1213C3.44129 20.6839 4.20435 21 5 21H19C19.7956 21 20.5587 20.6839 21.1213 20.1213C21.6839 19.5587 22 18.7956 22 18V10C22 9.20435 21.6839 8.44129 21.1213 7.87868C20.5587 7.31607 19.7956 7 19 7ZM5 5H15C15.2652 5 15.5196 5.10536 15.7071 5.29289C15.8946 5.48043 16 5.73478 16 6V7H5C4.73478 7 4.48043 6.89464 4.29289 6.70711C4.10536 6.51957 4 6.26522 4 6C4 5.73478 4.10536 5.48043 4.29289 5.29289C4.48043 5.10536 4.73478 5 5 5ZM20 15H19C18.7348 15 18.4804 14.8946 18.2929 14.7071C18.1054 14.5196 18 14.2652 18 14C18 13.7348 18.1054 13.4804 18.2929 13.2929C18.4804 13.1054 18.7348 13 19 13H20V15ZM20 11H19C18.2044 11 17.4413 11.3161 16.8787 11.8787C16.3161 12.4413 16 13.2044 16 14C16 14.7956 16.3161 15.5587 16.8787 16.1213C17.4413 16.6839 18.2044 17 19 17H20V18C20 18.2652 19.8946 18.5196 19.7071 18.7071C19.5196 18.8946 19.2652 19 19 19H5C4.73478 19 4.48043 18.8946 4.29289 18.7071C4.10536 18.5196 4 18.2652 4 18V8.83C4.32127 8.94302 4.65943 9.00051 5 9H19C19.2652 9 19.5196 9.10536 19.7071 9.29289C19.8946 9.48043 20 9.73478 20 10V11Z"
        fill={props.fill || "#DBC5FF"}
      />
    </svg>
  );
};
export const TradeIcon = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.5 17.4998H4V6.49982H11.8L11 7.28982C10.8117 7.47679 10.7054 7.73092 10.7044 7.99628C10.7035 8.26164 10.808 8.51651 10.995 8.70482C11.182 8.89312 11.4361 8.99943 11.7015 9.00037C11.9668 9.00131 12.2217 8.89679 12.41 8.70982L14.91 6.20982C15.0037 6.11685 15.0781 6.00625 15.1289 5.88439C15.1797 5.76253 15.2058 5.63183 15.2058 5.49982C15.2058 5.3678 15.1797 5.2371 15.1289 5.11524C15.0781 4.99338 15.0037 4.88278 14.91 4.78982L12.41 2.28982C12.2226 2.10357 11.9692 1.99902 11.705 1.99902C11.4408 1.99902 11.1874 2.10357 11 2.28982C10.9063 2.38278 10.8319 2.49338 10.7811 2.61524C10.7303 2.7371 10.7042 2.8678 10.7042 2.99982C10.7042 3.13183 10.7303 3.26253 10.7811 3.38439C10.8319 3.50625 10.9063 3.61685 11 3.70982L11.79 4.49982H3C2.73478 4.49982 2.48043 4.60517 2.29289 4.79271C2.10536 4.98025 2 5.2346 2 5.49982V18.4998C2 18.765 2.10536 19.0194 2.29289 19.2069C2.48043 19.3945 2.73478 19.4998 3 19.4998H5.5C5.76522 19.4998 6.01957 19.3945 6.20711 19.2069C6.39464 19.0194 6.5 18.765 6.5 18.4998C6.5 18.2346 6.39464 17.9802 6.20711 17.7927C6.01957 17.6052 5.76522 17.4998 5.5 17.4998ZM21 4.49982H18.5C18.2348 4.49982 17.9804 4.60517 17.7929 4.79271C17.6054 4.98025 17.5 5.2346 17.5 5.49982C17.5 5.76503 17.6054 6.01939 17.7929 6.20692C17.9804 6.39446 18.2348 6.49982 18.5 6.49982H20V17.4998H11.63L12.42 16.7098C12.5137 16.6169 12.5881 16.5063 12.6389 16.3844C12.6897 16.2625 12.7158 16.1318 12.7158 15.9998C12.7158 15.8678 12.6897 15.7371 12.6389 15.6152C12.5881 15.4934 12.5137 15.3828 12.42 15.2898C12.2326 15.1036 11.9792 14.999 11.715 14.999C11.4508 14.999 11.1974 15.1036 11.01 15.2898L8.51 17.7898C8.41627 17.8828 8.34188 17.9934 8.29111 18.1152C8.24034 18.2371 8.2142 18.3678 8.2142 18.4998C8.2142 18.6318 8.24034 18.7625 8.29111 18.8844C8.34188 19.0063 8.41627 19.1169 8.51 19.2098L11.01 21.7098C11.1983 21.8968 11.4532 22.0013 11.7185 22.0004C11.9839 21.9994 12.238 21.8931 12.425 21.7048C12.612 21.5165 12.7165 21.2616 12.7156 20.9963C12.7146 20.7309 12.6083 20.4768 12.42 20.2898L11.63 19.4998H21C21.2652 19.4998 21.5196 19.3945 21.7071 19.2069C21.8946 19.0194 22 18.765 22 18.4998V5.49982C22 5.2346 21.8946 4.98025 21.7071 4.79271C21.5196 4.60517 21.2652 4.49982 21 4.49982Z"
        fill={props.fill || "#DBC5FF"}
      />
    </svg>
  );
};
export const AddressesIcon = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="black"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M4.7599 10.5904C5.02511 10.6249 5.29316 10.5526 5.50508 10.3894C5.71699 10.2263 5.85542 9.98562 5.88989 9.7204C5.92437 9.45519 5.85208 9.18714 5.68893 8.97522C5.52577 8.7633 5.28511 8.62488 5.01989 8.5904L3.25989 8.1504C3.13158 8.11107 2.99666 8.09796 2.86318 8.11187C2.72969 8.12577 2.60037 8.16641 2.48292 8.23135C2.36547 8.29629 2.2623 8.3842 2.17954 8.48986C2.09679 8.59551 2.03614 8.71675 2.00123 8.84633C1.96632 8.97592 1.95784 9.11121 1.97632 9.24414C1.99479 9.37707 2.03983 9.50492 2.10876 9.62007C2.17769 9.73523 2.26909 9.83533 2.37752 9.91441C2.48595 9.9935 2.60919 10.05 2.73989 10.0804L4.49989 10.5504C4.58367 10.5784 4.67159 10.5919 4.7599 10.5904ZM8.61989 5.0004C8.6785 5.21856 8.80923 5.41046 8.99082 5.54483C9.1724 5.6792 9.39413 5.74814 9.61989 5.7404C9.70601 5.75423 9.79378 5.75423 9.8799 5.7404C10.1337 5.67019 10.3496 5.50269 10.4806 5.27427C10.6117 5.04585 10.6474 4.77496 10.5799 4.5204L10.1099 2.7604C10.0794 2.6297 10.023 2.50646 9.94391 2.39803C9.86482 2.2896 9.76472 2.19819 9.64956 2.12927C9.53441 2.06034 9.40656 2.0153 9.27363 1.99682C9.1407 1.97835 9.00541 1.98682 8.87582 2.02174C8.74624 2.05665 8.62501 2.11729 8.51935 2.20005C8.4137 2.28281 8.32578 2.38598 8.26084 2.50343C8.1959 2.62088 8.15527 2.7502 8.14136 2.88369C8.12745 3.01717 8.14056 3.15209 8.1799 3.2804L8.61989 5.0004ZM13.4499 15.0004C13.3565 14.9021 13.244 14.8238 13.1194 14.7703C12.9948 14.7167 12.8605 14.6892 12.7249 14.6892C12.5893 14.6892 12.455 14.7167 12.3304 14.7703C12.2058 14.8238 12.0933 14.9021 11.9999 15.0004L8.4999 18.5604C8.08838 18.9552 7.54017 19.1757 6.96989 19.1757C6.39961 19.1757 5.85141 18.9552 5.43989 18.5604C5.23732 18.3604 5.07648 18.1222 4.9667 17.8595C4.85692 17.5969 4.80039 17.3151 4.80039 17.0304C4.80039 16.7457 4.85692 16.4639 4.9667 16.2013C5.07648 15.9386 5.23732 15.7004 5.43989 15.5004L8.9999 12.0004C9.10458 11.9108 9.1896 11.8004 9.24962 11.6764C9.30964 11.5523 9.34337 11.4172 9.34869 11.2795C9.35401 11.1417 9.33081 11.0044 9.28053 10.8761C9.23026 10.7478 9.154 10.6312 9.05655 10.5338C8.95909 10.4363 8.84254 10.36 8.71422 10.3098C8.58589 10.2595 8.44856 10.2363 8.31084 10.2416C8.17312 10.2469 8.03799 10.2807 7.91393 10.3407C7.78986 10.4007 7.67954 10.4857 7.5899 10.5904L3.99989 14.0804C3.61118 14.4691 3.30284 14.9306 3.09247 15.4385C2.8821 15.9463 2.77382 16.4907 2.77382 17.0404C2.77382 17.5901 2.8821 18.1345 3.09247 18.6423C3.30284 19.1502 3.61118 19.6117 3.99989 20.0004C4.38861 20.3891 4.85008 20.6975 5.35795 20.9078C5.86583 21.1182 6.41017 21.2265 6.9599 21.2265C7.50962 21.2265 8.05396 21.1182 8.56184 20.9078C9.06971 20.6975 9.53118 20.3891 9.9199 20.0004L13.4499 16.4704C13.5514 16.3768 13.6324 16.2631 13.6878 16.1366C13.7432 16.0101 13.7718 15.8735 13.7718 15.7354C13.7718 15.5973 13.7432 15.4607 13.6878 15.3342C13.6324 15.2077 13.5514 15.094 13.4499 15.0004ZM5.17989 6.5904C5.36616 6.77515 5.61756 6.8793 5.87989 6.8804C6.0115 6.88116 6.14196 6.85594 6.2638 6.80617C6.38564 6.75641 6.49646 6.68308 6.5899 6.5904C6.77615 6.40304 6.88069 6.14959 6.88069 5.8854C6.88069 5.62122 6.77615 5.36776 6.5899 5.1804L5.2999 3.8904C5.10859 3.72658 4.86252 3.64097 4.61084 3.65069C4.35917 3.66041 4.12043 3.76474 3.94233 3.94284C3.76424 4.12093 3.6599 4.35967 3.65018 4.61135C3.64046 4.86303 3.72607 5.1091 3.88989 5.3004L5.17989 6.5904ZM21.2599 13.9204L19.4999 13.4504C19.3695 13.4053 19.2311 13.3877 19.0936 13.3987C18.956 13.4097 18.8222 13.449 18.7006 13.5143C18.579 13.5795 18.4722 13.6692 18.3869 13.7777C18.3017 13.8862 18.2398 14.0112 18.2052 14.1447C18.1706 14.2783 18.164 14.4176 18.1858 14.5539C18.2077 14.6902 18.2575 14.8204 18.3321 14.9365C18.4068 15.0525 18.5047 15.1519 18.6196 15.2283C18.7345 15.3047 18.864 15.3565 18.9999 15.3804L20.7599 15.8504H21.0199C21.2851 15.8849 21.5532 15.8126 21.7651 15.6494C21.977 15.4863 22.1154 15.2456 22.1499 14.9804C22.1844 14.7152 22.1121 14.4471 21.9489 14.2352C21.7858 14.0233 21.5451 13.8849 21.2799 13.8504L21.2599 13.9204ZM15.3799 19.0004C15.3452 18.8736 15.2858 18.7549 15.2052 18.6511C15.1246 18.5473 15.0243 18.4604 14.91 18.3954C14.7958 18.3304 14.6698 18.2885 14.5394 18.2722C14.409 18.2559 14.2766 18.2655 14.1499 18.3004C13.8961 18.3706 13.6802 18.5381 13.5492 18.7665C13.4181 18.9949 13.3824 19.2658 13.4499 19.5204L13.9199 21.2804C13.9785 21.4986 14.1092 21.6905 14.2908 21.8248C14.4724 21.9592 14.6941 22.0281 14.9199 22.0204C15.0063 22.0302 15.0935 22.0302 15.1799 22.0204C15.3076 21.9868 15.4273 21.9282 15.5322 21.848C15.6372 21.7678 15.7252 21.6677 15.7912 21.5533C15.8572 21.439 15.8999 21.3127 15.9168 21.1817C15.9338 21.0508 15.9246 20.9178 15.8899 20.7904L15.3799 19.0004ZM18.8199 17.4304C18.6286 17.2666 18.3825 17.181 18.1308 17.1907C17.8792 17.2004 17.6404 17.3047 17.4623 17.4828C17.2842 17.6609 17.1799 17.8997 17.1702 18.1514C17.1605 18.403 17.2461 18.6491 17.4099 18.8404L18.6999 20.1304C18.8873 20.3167 19.1407 20.4212 19.4049 20.4212C19.6691 20.4212 19.9225 20.3167 20.1099 20.1304C20.2961 19.943 20.4007 19.6896 20.4007 19.4254C20.4007 19.1612 20.2961 18.9078 20.1099 18.7204L18.8199 17.4304ZM21.1999 7.0004C21.2154 6.16884 20.9814 5.35171 20.5279 4.6545C20.0744 3.9573 19.4224 3.41202 18.6559 3.08908C17.8895 2.76614 17.0438 2.68035 16.2281 2.8428C15.4124 3.00525 14.6641 3.40847 14.0799 4.0004L10.5499 7.5604C10.4553 7.65561 10.3805 7.76851 10.3296 7.89266C10.2786 8.0168 10.2527 8.14976 10.2531 8.28394C10.2536 8.41812 10.2805 8.55089 10.3323 8.67468C10.384 8.79847 10.4597 8.91085 10.5549 9.0054C10.6501 9.09995 10.763 9.17483 10.8871 9.22575C11.0113 9.27667 11.1442 9.30264 11.2784 9.30217C11.4126 9.30171 11.5454 9.27482 11.6692 9.22304C11.793 9.17126 11.9053 9.09561 11.9999 9.0004L15.4999 5.4404C15.9114 5.04559 16.4596 4.82514 17.0299 4.82514C17.6002 4.82514 18.1484 5.04559 18.5599 5.4404C18.7625 5.64039 18.9233 5.87862 19.0331 6.14127C19.1429 6.40391 19.1994 6.68574 19.1994 6.9704C19.1994 7.25507 19.1429 7.53689 19.0331 7.79954C18.9233 8.06218 18.7625 8.30041 18.5599 8.5004L14.9999 12.0004C14.8136 12.1878 14.7091 12.4412 14.7091 12.7054C14.7091 12.9696 14.8136 13.223 14.9999 13.4104C15.1873 13.5967 15.4407 13.7012 15.7049 13.7012C15.9691 13.7012 16.2225 13.5967 16.4099 13.4104L19.9999 9.9204C20.7653 9.14075 21.1959 8.09296 21.1999 7.0004Z" />
    </svg>
  );
};
export const CreditIcon = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="black"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M21.22 12C21.7178 11.4524 21.9956 10.7401 22 10C22 9.20435 21.6839 8.44129 21.1213 7.87868C20.5587 7.31607 19.7956 7 19 7H13.82C13.9797 6.54818 14.0289 6.06466 13.9634 5.58993C13.8979 5.11519 13.7196 4.66306 13.4435 4.27138C13.1674 3.8797 12.8014 3.55988 12.3763 3.33868C11.9512 3.11748 11.4792 3.00135 11 3H5C4.20435 3 3.44129 3.31607 2.87868 3.87868C2.31607 4.44129 2 5.20435 2 6C2.00441 6.74005 2.28221 7.45236 2.78 8C2.28805 8.55002 2.01607 9.26207 2.01607 10C2.01607 10.7379 2.28805 11.45 2.78 12C2.28805 12.55 2.01607 13.2621 2.01607 14C2.01607 14.7379 2.28805 15.45 2.78 16C2.28221 16.5476 2.00441 17.2599 2 18C2 18.7956 2.31607 19.5587 2.87868 20.1213C3.44129 20.6839 4.20435 21 5 21H19C19.5778 20.9969 20.1423 20.8271 20.6259 20.5108C21.1094 20.1946 21.4914 19.7455 21.7259 19.2174C21.9603 18.6894 22.0373 18.1049 21.9476 17.5341C21.8578 16.9633 21.6052 16.4306 21.22 16C21.712 15.45 21.9839 14.7379 21.9839 14C21.9839 13.2621 21.712 12.55 21.22 12ZM11 19H5C4.73478 19 4.48043 18.8946 4.29289 18.7071C4.10536 18.5196 4 18.2652 4 18C4 17.7348 4.10536 17.4804 4.29289 17.2929C4.48043 17.1054 4.73478 17 5 17H11C11.2652 17 11.5196 17.1054 11.7071 17.2929C11.8946 17.4804 12 17.7348 12 18C12 18.2652 11.8946 18.5196 11.7071 18.7071C11.5196 18.8946 11.2652 19 11 19ZM11 15H5C4.73478 15 4.48043 14.8946 4.29289 14.7071C4.10536 14.5196 4 14.2652 4 14C4 13.7348 4.10536 13.4804 4.29289 13.2929C4.48043 13.1054 4.73478 13 5 13H11C11.2652 13 11.5196 13.1054 11.7071 13.2929C11.8946 13.4804 12 13.7348 12 14C12 14.2652 11.8946 14.5196 11.7071 14.7071C11.5196 14.8946 11.2652 15 11 15ZM11 11H5C4.73478 11 4.48043 10.8946 4.29289 10.7071C4.10536 10.5196 4 10.2652 4 10C4 9.73478 4.10536 9.48043 4.29289 9.29289C4.48043 9.10536 4.73478 9 5 9H11C11.2652 9 11.5196 9.10536 11.7071 9.29289C11.8946 9.48043 12 9.73478 12 10C12 10.2652 11.8946 10.5196 11.7071 10.7071C11.5196 10.8946 11.2652 11 11 11ZM11 7H5C4.73478 7 4.48043 6.89464 4.29289 6.70711C4.10536 6.51957 4 6.26522 4 6C4 5.73478 4.10536 5.48043 4.29289 5.29289C4.48043 5.10536 4.73478 5 5 5H11C11.2652 5 11.5196 5.10536 11.7071 5.29289C11.8946 5.48043 12 5.73478 12 6C12 6.26522 11.8946 6.51957 11.7071 6.70711C11.5196 6.89464 11.2652 7 11 7ZM19.69 18.71C19.6014 18.8035 19.4942 18.8776 19.3755 18.9275C19.2567 18.9774 19.1288 19.0021 19 19H13.82C14.0598 18.3549 14.0598 17.6451 13.82 17H19C19.2652 17 19.5196 17.1054 19.7071 17.2929C19.8946 17.4804 20 17.7348 20 18C19.9981 18.133 19.9698 18.2643 19.9165 18.3862C19.8633 18.5082 19.7863 18.6182 19.69 18.71ZM19.69 14.71C19.6014 14.8035 19.4942 14.8776 19.3755 14.9275C19.2567 14.9774 19.1288 15.0021 19 15H13.82C14.0598 14.3549 14.0598 13.6451 13.82 13H19C19.2652 13 19.5196 13.1054 19.7071 13.2929C19.8946 13.4804 20 13.7348 20 14C19.9981 14.133 19.9698 14.2643 19.9165 14.3862C19.8633 14.5082 19.7863 14.6182 19.69 14.71ZM19.69 10.71C19.6014 10.8035 19.4942 10.8776 19.3755 10.9275C19.2567 10.9774 19.1288 11.0021 19 11H13.82C14.0598 10.3549 14.0598 9.6451 13.82 9H19C19.2652 9 19.5196 9.10536 19.7071 9.29289C19.8946 9.48043 20 9.73478 20 10C19.9981 10.133 19.9698 10.2643 19.9165 10.3862C19.8633 10.5082 19.7863 10.6182 19.69 10.71Z" />
    </svg>
  );
};

export function MagnifyingGlassIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      strokeWidth={1.5}
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
      />
    </svg>
  );
}

export function CommandkeyIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      {...props}
    >
      <path
        d="M4.23005 22.455L4.68404 21.564H4.68404L4.23005 22.455ZM2.04497 20.27L2.93597 19.816H2.93597L2.04497 20.27ZM22.955 20.27L22.064 19.816V19.816L22.955 20.27ZM20.77 22.455L20.316 21.564H20.316L20.77 22.455ZM20.77 1.54497L20.316 2.43597V2.43597L20.77 1.54497ZM22.955 3.73005L22.064 4.18404V4.18404L22.955 3.73005ZM4.23005 1.54497L4.68404 2.43597L4.23005 1.54497ZM2.04497 3.73005L2.93597 4.18404L2.04497 3.73005ZM9.5 2H15.5V0H9.5V2ZM22.5 9V15H24.5V9H22.5ZM15.5 22H9.5V24H15.5V22ZM2.5 15V9H0.5V15H2.5ZM9.5 22C8.08337 22 7.08104 21.9992 6.29744 21.9352C5.52552 21.8721 5.05435 21.7527 4.68404 21.564L3.77606 23.346C4.4753 23.7023 5.23898 23.8554 6.13458 23.9286C7.01851 24.0008 8.11637 24 9.5 24V22ZM0.5 15C0.5 16.3836 0.499222 17.4815 0.571442 18.3654C0.644616 19.261 0.797677 20.0247 1.15396 20.7239L2.93597 19.816C2.74729 19.4457 2.62787 18.9745 2.5648 18.2026C2.50078 17.419 2.5 16.4166 2.5 15H0.5ZM4.68404 21.564C3.93139 21.1805 3.31947 20.5686 2.93597 19.816L1.15396 20.7239C1.7292 21.8529 2.64708 22.7708 3.77606 23.346L4.68404 21.564ZM22.5 15C22.5 16.4166 22.4992 17.419 22.4352 18.2026C22.3721 18.9745 22.2527 19.4457 22.064 19.816L23.846 20.7239C24.2023 20.0247 24.3554 19.261 24.4286 18.3654C24.5008 17.4815 24.5 16.3836 24.5 15H22.5ZM15.5 24C16.8836 24 17.9815 24.0008 18.8654 23.9286C19.761 23.8554 20.5247 23.7023 21.2239 23.346L20.316 21.564C19.9457 21.7527 19.4745 21.8721 18.7026 21.9352C17.919 21.9992 16.9166 22 15.5 22V24ZM22.064 19.816C21.6805 20.5686 21.0686 21.1805 20.316 21.564L21.2239 23.346C22.3529 22.7708 23.2708 21.8529 23.846 20.7239L22.064 19.816ZM15.5 2C16.9166 2 17.919 2.00078 18.7026 2.0648C19.4745 2.12787 19.9457 2.24729 20.316 2.43597L21.2239 0.653961C20.5247 0.297677 19.761 0.144616 18.8654 0.071442C17.9815 -0.000777721 16.8836 0 15.5 0V2ZM24.5 9C24.5 7.61637 24.5008 6.51851 24.4286 5.63458C24.3554 4.73898 24.2023 3.9753 23.846 3.27606L22.064 4.18404C22.2527 4.55435 22.3721 5.02552 22.4352 5.79744C22.4992 6.58104 22.5 7.58337 22.5 9H24.5ZM20.316 2.43597C21.0686 2.81947 21.6805 3.43139 22.064 4.18404L23.846 3.27606C23.2708 2.14708 22.3529 1.2292 21.2239 0.653961L20.316 2.43597ZM9.5 0C8.11637 0 7.01851 -0.000777721 6.13458 0.071442C5.23898 0.144616 4.4753 0.297677 3.77606 0.653961L4.68404 2.43597C5.05435 2.24729 5.52552 2.12787 6.29744 2.0648C7.08104 2.00078 8.08337 2 9.5 2V0ZM2.5 9C2.5 7.58337 2.50078 6.58104 2.5648 5.79744C2.62787 5.02552 2.74729 4.55435 2.93597 4.18404L1.15396 3.27606C0.797677 3.9753 0.644616 4.73898 0.571442 5.63458C0.499222 6.51851 0.5 7.61637 0.5 9H2.5ZM3.77606 0.653961C2.64708 1.2292 1.7292 2.14708 1.15396 3.27606L2.93597 4.18404C3.31947 3.43139 3.93139 2.81947 4.68404 2.43597L3.77606 0.653961ZM9.95455 10.4545H15.0455V8.45455H9.95455V10.4545ZM14.0455 9.45455V14.5455H16.0455V9.45455H14.0455ZM15.0455 13.5455H9.95455V15.5455H15.0455V13.5455ZM10.9545 14.5455V9.45455H8.95455V14.5455H10.9545ZM15.0455 15.5455H18.2273V13.5455H15.0455V15.5455ZM16.0455 17.7273V14.5455H14.0455V17.7273H16.0455ZM16.3182 18C16.1676 18 16.0455 17.8779 16.0455 17.7273H14.0455C14.0455 18.9825 15.063 20 16.3182 20V18ZM18.5 15.8182C18.5 17.0232 17.5232 18 16.3182 18V20C18.6277 20 20.5 18.1277 20.5 15.8182H18.5ZM18.2273 15.5455C18.3779 15.5455 18.5 15.6676 18.5 15.8182H20.5C20.5 14.563 19.4825 13.5455 18.2273 13.5455V15.5455ZM9.95455 8.45455H6.77273V10.4545H9.95455V8.45455ZM8.95455 6.27273V9.45455H10.9545V6.27273H8.95455ZM8.68182 6C8.83244 6 8.95455 6.1221 8.95455 6.27273H10.9545C10.9545 5.01753 9.93701 4 8.68182 4V6ZM6.5 8.18182C6.5 6.97683 7.47683 6 8.68182 6V4C6.37226 4 4.5 5.87226 4.5 8.18182H6.5ZM6.77273 8.45455C6.6221 8.45455 6.5 8.33244 6.5 8.18182H4.5C4.5 9.43701 5.51753 10.4545 6.77273 10.4545V8.45455ZM9.95455 13.5455H6.77273V15.5455H9.95455V13.5455ZM10.9545 17.7273V14.5455H8.95455V17.7273H10.9545ZM8.68182 20C9.93701 20 10.9545 18.9825 10.9545 17.7273H8.95455C8.95455 17.8779 8.83244 18 8.68182 18V20ZM4.5 15.8182C4.5 18.1277 6.37226 20 8.68182 20V18C7.47683 18 6.5 17.0232 6.5 15.8182H4.5ZM6.77273 13.5455C5.51753 13.5455 4.5 14.563 4.5 15.8182H6.5C6.5 15.6676 6.62211 15.5455 6.77273 15.5455V13.5455ZM15.0455 10.4545H18.2273V8.45455H15.0455V10.4545ZM14.0455 6.27273V9.45455H16.0455V6.27273H14.0455ZM16.3182 4C15.063 4 14.0455 5.01753 14.0455 6.27273H16.0455C16.0455 6.12211 16.1676 6 16.3182 6V4ZM20.5 8.18182C20.5 5.87226 18.6277 4 16.3182 4V6C17.5232 6 18.5 6.97683 18.5 8.18182H20.5ZM18.2273 10.4545C19.4825 10.4545 20.5 9.43701 20.5 8.18182H18.5C18.5 8.33244 18.3779 8.45455 18.2273 8.45455V10.4545Z"
        fill="#B1B1B1"
      />
    </svg>
  );
}

export function SignoutIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="black"
      {...props}
    >
      <path d="M12.59 13.0001L10.29 15.2901C10.1963 15.3831 10.1219 15.4937 10.0711 15.6155C10.0203 15.7374 9.9942 15.8681 9.9942 16.0001C9.9942 16.1321 10.0203 16.2628 10.0711 16.3847C10.1219 16.5065 10.1963 16.6171 10.29 16.7101C10.383 16.8038 10.4936 16.8782 10.6154 16.929C10.7373 16.9798 10.868 17.0059 11 17.0059C11.132 17.0059 11.2627 16.9798 11.3846 16.929C11.5064 16.8782 11.617 16.8038 11.71 16.7101L15.71 12.7101C15.801 12.615 15.8724 12.5029 15.92 12.3801C16.02 12.1366 16.02 11.8636 15.92 11.6201C15.8724 11.4973 15.801 11.3852 15.71 11.2901L11.71 7.2901C11.6168 7.19686 11.5061 7.1229 11.3842 7.07244C11.2624 7.02198 11.1319 6.99601 11 6.99601C10.8681 6.99601 10.7376 7.02198 10.6158 7.07244C10.4939 7.1229 10.3832 7.19686 10.29 7.2901C10.1968 7.38334 10.1228 7.49403 10.0723 7.61585C10.0219 7.73767 9.99591 7.86824 9.99591 8.0001C9.99591 8.13196 10.0219 8.26253 10.0723 8.38435C10.1228 8.50617 10.1968 8.61686 10.29 8.7101L12.59 11.0001H3C2.73478 11.0001 2.48043 11.1055 2.29289 11.293C2.10536 11.4805 2 11.7349 2 12.0001C2 12.2653 2.10536 12.5197 2.29289 12.7072C2.48043 12.8947 2.73478 13.0001 3 13.0001H12.59ZM12 2.0001C10.1311 1.99176 8.29724 2.50731 6.70647 3.48829C5.11569 4.46927 3.83165 5.87641 3 7.5501C2.88065 7.78879 2.86101 8.06512 2.94541 8.3183C3.0298 8.57147 3.21131 8.78075 3.45 8.9001C3.68869 9.01945 3.96502 9.03909 4.2182 8.95469C4.47137 8.8703 4.68065 8.68879 4.8 8.4501C5.43219 7.17342 6.39383 6.08872 7.58555 5.30809C8.77727 4.52746 10.1558 4.07922 11.5788 4.00969C13.0017 3.94017 14.4174 4.25188 15.6795 4.91261C16.9417 5.57334 18.0045 6.55913 18.7581 7.7681C19.5118 8.97706 19.9289 10.3653 19.9664 11.7895C20.0039 13.2136 19.6605 14.6219 18.9715 15.8689C18.2826 17.1159 17.2731 18.1563 16.0475 18.8825C14.8219 19.6088 13.4246 19.9946 12 20.0001C10.5089 20.0066 9.04615 19.5925 7.77969 18.8053C6.51323 18.0182 5.49435 16.89 4.84 15.5501C4.72065 15.3114 4.51137 15.1299 4.2582 15.0455C4.00502 14.9611 3.72869 14.9808 3.49 15.1001C3.25131 15.2194 3.0698 15.4287 2.98541 15.6819C2.90101 15.9351 2.92065 16.2114 3.04 16.4501C3.83283 18.0456 5.03752 19.4003 6.52947 20.3741C8.02142 21.348 9.74645 21.9055 11.5261 21.9891C13.3058 22.0727 15.0755 21.6793 16.6521 20.8496C18.2288 20.0199 19.5552 18.7841 20.4941 17.2699C21.433 15.7558 21.9503 14.0182 21.9925 12.2371C22.0347 10.456 21.6003 8.69589 20.7342 7.13893C19.8682 5.58197 18.6018 4.28467 17.0663 3.38121C15.5307 2.47774 13.7816 2.00094 12 2.0001Z" />
    </svg>
  );
}

export function QuestionIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        d="M11.29 15.29C11.247 15.3375 11.2069 15.3876 11.17 15.44C11.1322 15.4957 11.1019 15.5563 11.08 15.62C11.0512 15.6767 11.031 15.7374 11.02 15.8C11.0151 15.8666 11.0151 15.9334 11.02 16C11.0166 16.1312 11.044 16.2613 11.1 16.38C11.1449 16.5041 11.2166 16.6168 11.3099 16.7101C11.4032 16.8034 11.5159 16.8751 11.64 16.92C11.7597 16.9729 11.8891 17.0002 12.02 17.0002C12.1509 17.0002 12.2803 16.9729 12.4 16.92C12.5241 16.8751 12.6368 16.8034 12.7301 16.7101C12.8234 16.6168 12.8951 16.5041 12.94 16.38C12.9844 16.2584 13.0048 16.1294 13 16C13.0008 15.8684 12.9755 15.7379 12.9258 15.6161C12.876 15.4943 12.8027 15.3834 12.71 15.29C12.617 15.1963 12.5064 15.1219 12.3846 15.0711C12.2627 15.0203 12.132 14.9942 12 14.9942C11.868 14.9942 11.7373 15.0203 11.6154 15.0711C11.4936 15.1219 11.383 15.1963 11.29 15.29ZM12 2C10.0222 2 8.08879 2.58649 6.4443 3.6853C4.79981 4.78412 3.51809 6.3459 2.76121 8.17317C2.00433 10.0004 1.8063 12.0111 2.19215 13.9509C2.578 15.8907 3.53041 17.6725 4.92894 19.0711C6.32746 20.4696 8.10929 21.422 10.0491 21.8079C11.9889 22.1937 13.9996 21.9957 15.8268 21.2388C17.6541 20.4819 19.2159 19.2002 20.3147 17.5557C21.4135 15.9112 22 13.9778 22 12C22 10.6868 21.7413 9.38642 21.2388 8.17317C20.7363 6.95991 19.9997 5.85752 19.0711 4.92893C18.1425 4.00035 17.0401 3.26375 15.8268 2.7612C14.6136 2.25866 13.3132 2 12 2ZM12 20C10.4178 20 8.87104 19.5308 7.55544 18.6518C6.23985 17.7727 5.21447 16.5233 4.60897 15.0615C4.00347 13.5997 3.84504 11.9911 4.15372 10.4393C4.4624 8.88743 5.22433 7.46197 6.34315 6.34315C7.46197 5.22433 8.88743 4.4624 10.4393 4.15372C11.9911 3.84504 13.5997 4.00346 15.0615 4.60896C16.5233 5.21447 17.7727 6.23984 18.6518 7.55544C19.5308 8.87103 20 10.4177 20 12C20 14.1217 19.1572 16.1566 17.6569 17.6569C16.1566 19.1571 14.1217 20 12 20ZM12 7C11.4731 6.99966 10.9553 7.13812 10.4989 7.40144C10.0425 7.66476 9.66347 8.04366 9.4 8.5C9.32765 8.61382 9.27907 8.7411 9.25718 8.87418C9.23529 9.00726 9.24055 9.14339 9.27263 9.27439C9.30472 9.40538 9.36297 9.52854 9.44389 9.63643C9.52481 9.74433 9.62671 9.83475 9.74348 9.90224C9.86024 9.96974 9.98945 10.0129 10.1233 10.0292C10.2572 10.0454 10.393 10.0345 10.5225 9.99688C10.6521 9.9593 10.7727 9.89591 10.8771 9.81052C10.9814 9.72513 11.0675 9.6195 11.13 9.5C11.2181 9.3474 11.345 9.22078 11.4978 9.13298C11.6505 9.04518 11.8238 8.9993 12 9C12.2652 9 12.5196 9.10536 12.7071 9.29289C12.8946 9.48043 13 9.73478 13 10C13 10.2652 12.8946 10.5196 12.7071 10.7071C12.5196 10.8946 12.2652 11 12 11C11.7348 11 11.4804 11.1054 11.2929 11.2929C11.1054 11.4804 11 11.7348 11 12V13C11 13.2652 11.1054 13.5196 11.2929 13.7071C11.4804 13.8946 11.7348 14 12 14C12.2652 14 12.5196 13.8946 12.7071 13.7071C12.8946 13.5196 13 13.2652 13 13V12.82C13.6614 12.58 14.2174 12.1152 14.5708 11.5069C14.9242 10.8985 15.0525 10.1853 14.9334 9.49189C14.8143 8.79849 14.4552 8.16902 13.919 7.71352C13.3828 7.25801 12.7035 7.00546 12 7Z"
        fill="black"
      />
    </svg>
  );
}

export function BellIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        d="M18 13.18V10C17.9986 8.58312 17.4958 7.21247 16.5806 6.13077C15.6655 5.04908 14.3971 4.32615 13 4.09V3C13 2.73478 12.8946 2.48043 12.7071 2.29289C12.5196 2.10536 12.2652 2 12 2C11.7348 2 11.4804 2.10536 11.2929 2.29289C11.1054 2.48043 11 2.73478 11 3V4.09C9.60294 4.32615 8.33452 5.04908 7.41939 6.13077C6.50425 7.21247 6.00144 8.58312 6 10V13.18C5.41645 13.3863 4.911 13.7681 4.55294 14.2729C4.19488 14.7778 4.00174 15.3811 4 16V18C4 18.2652 4.10536 18.5196 4.29289 18.7071C4.48043 18.8946 4.73478 19 5 19H8.14C8.37028 19.8474 8.873 20.5954 9.5706 21.1287C10.2682 21.6621 11.1219 21.951 12 21.951C12.8781 21.951 13.7318 21.6621 14.4294 21.1287C15.127 20.5954 15.6297 19.8474 15.86 19H19C19.2652 19 19.5196 18.8946 19.7071 18.7071C19.8946 18.5196 20 18.2652 20 18V16C19.9983 15.3811 19.8051 14.7778 19.4471 14.2729C19.089 13.7681 18.5835 13.3863 18 13.18ZM8 10C8 8.93913 8.42143 7.92172 9.17157 7.17157C9.92172 6.42143 10.9391 6 12 6C13.0609 6 14.0783 6.42143 14.8284 7.17157C15.5786 7.92172 16 8.93913 16 10V13H8V10ZM12 20C11.651 19.9979 11.3086 19.9045 11.0068 19.7291C10.7051 19.5536 10.4545 19.3023 10.28 19H13.72C13.5455 19.3023 13.2949 19.5536 12.9932 19.7291C12.6914 19.9045 12.349 19.9979 12 20ZM18 17H6V16C6 15.7348 6.10536 15.4804 6.29289 15.2929C6.48043 15.1054 6.73478 15 7 15H17C17.2652 15 17.5196 15.1054 17.7071 15.2929C17.8946 15.4804 18 15.7348 18 16V17Z"
        fill="black"
      />
    </svg>
  );
}

export function PaymentIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        d="M3 7H21C21.2652 7 21.5196 6.89464 21.7071 6.70711C21.8946 6.51957 22 6.26522 22 6C22 5.73478 21.8946 5.48043 21.7071 5.29289C21.5196 5.10536 21.2652 5 21 5H3C2.73478 5 2.48043 5.10536 2.29289 5.29289C2.10536 5.48043 2 5.73478 2 6C2 6.26522 2.10536 6.51957 2.29289 6.70711C2.48043 6.89464 2.73478 7 3 7ZM9 15H3C2.73478 15 2.48043 15.1054 2.29289 15.2929C2.10536 15.4804 2 15.7348 2 16C2 16.2652 2.10536 16.5196 2.29289 16.7071C2.48043 16.8946 2.73478 17 3 17H9C9.26522 17 9.51957 16.8946 9.70711 16.7071C9.89464 16.5196 10 16.2652 10 16C10 15.7348 9.89464 15.4804 9.70711 15.2929C9.51957 15.1054 9.26522 15 9 15ZM18.5 10H3C2.73478 10 2.48043 10.1054 2.29289 10.2929C2.10536 10.4804 2 10.7348 2 11C2 11.2652 2.10536 11.5196 2.29289 11.7071C2.48043 11.8946 2.73478 12 3 12H18.5C18.8978 12 19.2794 12.158 19.5607 12.4393C19.842 12.7206 20 13.1022 20 13.5C20 13.8978 19.842 14.2794 19.5607 14.5607C19.2794 14.842 18.8978 15 18.5 15H15.41L15.71 14.71C15.8983 14.5217 16.0041 14.2663 16.0041 14C16.0041 13.7337 15.8983 13.4783 15.71 13.29C15.5217 13.1017 15.2663 12.9959 15 12.9959C14.7337 12.9959 14.4783 13.1017 14.29 13.29L12.29 15.29C12.199 15.3851 12.1276 15.4972 12.08 15.62C11.98 15.8635 11.98 16.1365 12.08 16.38C12.1276 16.5028 12.199 16.6149 12.29 16.71L14.29 18.71C14.383 18.8037 14.4936 18.8781 14.6154 18.9289C14.7373 18.9797 14.868 19.0058 15 19.0058C15.132 19.0058 15.2627 18.9797 15.3846 18.9289C15.5064 18.8781 15.617 18.8037 15.71 18.71C15.8037 18.617 15.8781 18.5064 15.9289 18.3846C15.9797 18.2627 16.0058 18.132 16.0058 18C16.0058 17.868 15.9797 17.7373 15.9289 17.6154C15.8781 17.4936 15.8037 17.383 15.71 17.29L15.41 17H18.5C19.4283 17 20.3185 16.6313 20.9749 15.9749C21.6313 15.3185 22 14.4283 22 13.5C22 12.5717 21.6313 11.6815 20.9749 11.0251C20.3185 10.3687 19.4283 10 18.5 10Z"
        fill="#DBC5FF"
      />
    </svg>
  );
}

export function ComplianceIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        d="M19.63 3.64982C19.5138 3.55584 19.3781 3.48909 19.2327 3.45448C19.0873 3.41987 18.9361 3.41828 18.79 3.44982C17.7214 3.67376 16.6183 3.67662 15.5486 3.4582C14.4789 3.23979 13.4653 2.80473 12.57 2.17982C12.4026 2.06369 12.2037 2.00146 12 2.00146C11.7963 2.00146 11.5974 2.06369 11.43 2.17982C10.5348 2.80473 9.52108 3.23979 8.45137 3.4582C7.38166 3.67662 6.27857 3.67376 5.21001 3.44982C5.06394 3.41828 4.91267 3.41987 4.76731 3.45448C4.62194 3.48909 4.48618 3.55584 4.37001 3.64982C4.25399 3.74394 4.16053 3.86286 4.0965 3.99784C4.03247 4.13282 3.9995 4.28043 4.00001 4.42982V11.8798C3.99912 13.3136 4.34078 14.7268 4.99654 16.0018C5.6523 17.2768 6.60319 18.3767 7.77001 19.2098L11.42 21.8098C11.5894 21.9304 11.7921 21.9952 12 21.9952C12.2079 21.9952 12.4106 21.9304 12.58 21.8098L16.23 19.2098C17.3968 18.3767 18.3477 17.2768 19.0035 16.0018C19.6592 14.7268 20.0009 13.3136 20 11.8798V4.42982C20.0005 4.28043 19.9675 4.13282 19.9035 3.99784C19.8395 3.86286 19.746 3.74394 19.63 3.64982ZM18 11.8798C18.0008 12.9946 17.7353 14.0934 17.2257 15.0848C16.716 16.0763 15.977 16.9317 15.07 17.5798L12 19.7698L8.93001 17.5798C8.02304 16.9317 7.28399 16.0763 6.77435 15.0848C6.26472 14.0934 5.99924 12.9946 6.00001 11.8798V5.57982C8.09643 5.75925 10.196 5.27284 12 4.18982C13.804 5.27284 15.9036 5.75925 18 5.57982V11.8798ZM13.54 9.58982L10.85 12.2898L9.96001 11.3898C9.7717 11.2015 9.51631 11.0957 9.25001 11.0957C8.9837 11.0957 8.72831 11.2015 8.54001 11.3898C8.3517 11.5781 8.24591 11.8335 8.24591 12.0998C8.24591 12.3661 8.3517 12.6215 8.54001 12.8098L10.14 14.4098C10.233 14.5035 10.3436 14.5779 10.4654 14.6287C10.5873 14.6795 10.718 14.7056 10.85 14.7056C10.982 14.7056 11.1127 14.6795 11.2346 14.6287C11.3564 14.5779 11.467 14.5035 11.56 14.4098L15 10.9998C15.1883 10.8115 15.2941 10.5561 15.2941 10.2898C15.2941 10.0235 15.1883 9.76812 15 9.57982C14.8117 9.39152 14.5563 9.28573 14.29 9.28573C14.0237 9.28573 13.7683 9.39152 13.58 9.57982L13.54 9.58982Z"
        fill="#DBC5FF"
      />
    </svg>
  );
}

export function SubscriptionsIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        d="M11.9998 15C11.4065 15 10.8264 15.1759 10.3331 15.5056C9.83974 15.8352 9.45522 16.3037 9.22816 16.8519C9.00109 17.4001 8.94168 18.0033 9.05744 18.5852C9.17319 19.1672 9.45892 19.7017 9.87847 20.1213C10.298 20.5408 10.8326 20.8266 11.4145 20.9423C11.9965 21.0581 12.5997 20.9987 13.1478 20.7716C13.696 20.5445 14.1646 20.16 14.4942 19.6667C14.8238 19.1733 14.9998 18.5933 14.9998 18C14.9998 17.2043 14.6837 16.4412 14.1211 15.8786C13.5585 15.316 12.7954 15 11.9998 15ZM11.9998 19C11.802 19 11.6087 18.9413 11.4442 18.8314C11.2798 18.7216 11.1516 18.5654 11.0759 18.3826C11.0002 18.1999 10.9804 17.9989 11.019 17.8049C11.0576 17.6109 11.1528 17.4327 11.2927 17.2929C11.4325 17.153 11.6107 17.0578 11.8047 17.0192C11.9987 16.9806 12.1998 17.0004 12.3825 17.0761C12.5652 17.1518 12.7214 17.2799 12.8313 17.4444C12.9411 17.6088 12.9998 17.8022 12.9998 18C12.9998 18.2652 12.8944 18.5195 12.7069 18.7071C12.5194 18.8946 12.265 19 11.9998 19ZM11.9998 11C10.1445 11.0064 8.36635 11.7428 7.0498 13.05C6.86354 13.2373 6.759 13.4908 6.759 13.755C6.759 14.0191 6.86354 14.2726 7.0498 14.46C7.23716 14.6462 7.49061 14.7508 7.7548 14.7508C8.01898 14.7508 8.27243 14.6462 8.45979 14.46C8.92425 13.9943 9.47601 13.6249 10.0835 13.3728C10.6909 13.1208 11.3421 12.991 11.9998 12.991C12.6575 12.991 13.3087 13.1208 13.9161 13.3728C14.5236 13.6249 15.0753 13.9943 15.5398 14.46C15.7244 14.6484 15.976 14.7562 16.2398 14.76C16.4427 14.7716 16.6443 14.7212 16.8178 14.6154C16.9914 14.5096 17.1285 14.3535 17.2111 14.1678C17.2936 13.982 17.3176 13.7756 17.2799 13.5759C17.2421 13.3762 17.1444 13.1927 16.9998 13.05C16.3418 12.3967 15.5616 11.8794 14.7037 11.5276C13.8458 11.1759 12.927 10.9966 11.9998 11ZM11.9998 6.99996C9.08378 7.00771 6.28837 8.16468 4.21979 10.22C4.12656 10.3132 4.0526 10.4239 4.00214 10.5457C3.95167 10.6675 3.9257 10.7981 3.9257 10.93C3.9257 11.1963 4.03149 11.4517 4.21979 11.64C4.4081 11.8283 4.66349 11.9341 4.92979 11.9341C5.1961 11.9341 5.45149 11.8283 5.63979 11.64C7.3273 9.95456 9.61479 9.00788 11.9998 9.00788C14.3848 9.00788 16.6723 9.95456 18.3598 11.64C18.4532 11.7326 18.5641 11.806 18.6859 11.8557C18.8077 11.9055 18.9382 11.9307 19.0698 11.93C19.2014 11.9307 19.3319 11.9055 19.4537 11.8557C19.5755 11.806 19.6864 11.7326 19.7798 11.64C19.8735 11.547 19.9479 11.4364 19.9987 11.3145C20.0495 11.1927 20.0756 11.062 20.0756 10.93C20.0756 10.798 20.0495 10.6672 19.9987 10.5454C19.9479 10.4235 19.8735 10.3129 19.7798 10.22C17.7112 8.16468 14.9158 7.00771 11.9998 6.99996ZM22.6098 7.38996C21.2168 5.99606 19.5628 4.89031 17.7423 4.13589C15.9218 3.38147 13.9704 2.99316 11.9998 2.99316C10.0292 2.99316 8.07783 3.38147 6.25732 4.13589C4.43681 4.89031 2.7828 5.99606 1.38979 7.38996C1.20149 7.57827 1.0957 7.83366 1.0957 8.09996C1.0957 8.36626 1.20149 8.62166 1.38979 8.80996C1.5781 8.99827 1.83349 9.10405 2.09979 9.10405C2.3661 9.10405 2.62149 8.99827 2.80979 8.80996C5.24757 6.37345 8.55315 5.00474 11.9998 5.00474C15.4464 5.00474 18.752 6.37345 21.1898 8.80996C21.2828 8.90369 21.3934 8.97808 21.5152 9.02885C21.6371 9.07962 21.7678 9.10576 21.8998 9.10576C22.0318 9.10576 22.1625 9.07962 22.2844 9.02885C22.4062 8.97808 22.5168 8.90369 22.6098 8.80996C22.7035 8.717 22.7779 8.6064 22.8287 8.48454C22.8795 8.36268 22.9056 8.23197 22.9056 8.09996C22.9056 7.96795 22.8795 7.83724 22.8287 7.71539C22.7779 7.59353 22.7035 7.48292 22.6098 7.38996Z"
        fill="#DBC5FF"
      />
    </svg>
  );
}

export function DevelopersIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        d="M17 6.06018C16.3065 6.06065 15.6345 6.30138 15.0985 6.74143C14.5625 7.18147 14.1954 7.79365 14.0599 8.4738C13.9243 9.15395 14.0286 9.86006 14.355 10.472C14.6813 11.0839 15.2097 11.5639 15.85 11.8302C15.6978 12.1951 15.4408 12.5067 15.1115 12.7257C14.7822 12.9446 14.3954 13.061 14 13.0602H10C9.29505 13.0631 8.604 13.2566 8.00001 13.6202V7.88019C8.66722 7.64429 9.22956 7.18012 9.58764 6.56971C9.94571 5.95929 10.0765 5.24195 9.9568 4.54445C9.83713 3.84696 9.47473 3.21422 8.93367 2.75808C8.3926 2.30194 7.70769 2.05176 7.00001 2.05176C6.29232 2.05176 5.60741 2.30194 5.06635 2.75808C4.52528 3.21422 4.16288 3.84696 4.04321 4.54445C3.92354 5.24195 4.0543 5.95929 4.41237 6.56971C4.77045 7.18012 5.33279 7.64429 6.00001 7.88019V16.2402C5.34099 16.4772 4.78566 16.9373 4.43026 17.5407C4.07485 18.1442 3.94176 18.8529 4.05406 19.5442C4.16635 20.2355 4.51695 20.8657 5.0451 21.3256C5.57325 21.7855 6.24567 22.0462 6.94581 22.0624C7.64596 22.0786 8.32971 21.8493 8.87857 21.4143C9.42744 20.9794 9.80683 20.3661 9.95099 19.6807C10.0952 18.9954 9.995 18.2812 9.6679 17.662C9.34079 17.0427 8.80735 16.5574 8.16001 16.2902C8.31158 15.9268 8.56704 15.6163 8.89436 15.3975C9.22168 15.1787 9.60629 15.0613 10 15.0602H14C14.9148 15.06 15.802 14.7462 16.5135 14.1712C17.225 13.5962 17.7179 12.7946 17.91 11.9002C18.5786 11.673 19.1456 11.2173 19.5113 10.6132C19.8769 10.0092 20.0178 9.29548 19.9091 8.59779C19.8004 7.90009 19.449 7.2631 18.9169 6.79893C18.3848 6.33476 17.706 6.07317 17 6.06018ZM7.00001 4.06018C7.19779 4.06018 7.39113 4.11883 7.55558 4.22872C7.72003 4.3386 7.8482 4.49478 7.92389 4.6775C7.99957 4.86023 8.01938 5.06129 7.98079 5.25528C7.94221 5.44926 7.84697 5.62744 7.70711 5.76729C7.56726 5.90714 7.38908 6.00239 7.1951 6.04097C7.00112 6.07956 6.80005 6.05975 6.61732 5.98406C6.4346 5.90838 6.27842 5.7802 6.16854 5.61576C6.05866 5.45131 6.00001 5.25797 6.00001 5.06018C6.00001 4.79497 6.10536 4.54061 6.2929 4.35308C6.48044 4.16554 6.73479 4.06018 7.00001 4.06018ZM7.00001 20.0602C6.80222 20.0602 6.60889 20.0015 6.44444 19.8917C6.27999 19.7818 6.15181 19.6256 6.07613 19.4429C6.00044 19.2601 5.98064 19.0591 6.01922 18.8651C6.05781 18.6711 6.15305 18.4929 6.2929 18.3531C6.43275 18.2132 6.61093 18.118 6.80492 18.0794C6.9989 18.0408 7.19996 18.0606 7.38269 18.1363C7.56542 18.212 7.72159 18.3402 7.83148 18.5046C7.94136 18.6691 8.00001 18.8624 8.00001 19.0602C8.00001 19.3254 7.89465 19.5798 7.70711 19.7673C7.51958 19.9548 7.26522 20.0602 7.00001 20.0602ZM17 10.0602C16.8022 10.0602 16.6089 10.0015 16.4444 9.89165C16.28 9.78177 16.1518 9.62559 16.0761 9.44287C16.0004 9.26014 15.9806 9.05908 16.0192 8.8651C16.0578 8.67111 16.153 8.49293 16.2929 8.35308C16.4328 8.21323 16.6109 8.11799 16.8049 8.0794C16.9989 8.04081 17.2 8.06062 17.3827 8.13631C17.5654 8.21199 17.7216 8.34017 17.8315 8.50461C17.9414 8.66906 18 8.8624 18 9.06019C18 9.3254 17.8947 9.57976 17.7071 9.76729C17.5196 9.95483 17.2652 10.0602 17 10.0602Z"
        fill="#DBC5FF"
      />
    </svg>
  );
}

export function TransactionsIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="black"
      {...props}
    >
      <path d="M12 2C9.43639 2.00731 6.97349 2.99891 5.12 4.77V3C5.12 2.73478 5.01464 2.48043 4.82711 2.29289C4.63957 2.10536 4.38522 2 4.12 2C3.85478 2 3.60043 2.10536 3.41289 2.29289C3.22536 2.48043 3.12 2.73478 3.12 3V7.5C3.12 7.76522 3.22536 8.01957 3.41289 8.20711C3.60043 8.39464 3.85478 8.5 4.12 8.5H8.62C8.88522 8.5 9.13957 8.39464 9.32711 8.20711C9.51464 8.01957 9.62 7.76522 9.62 7.5C9.62 7.23478 9.51464 6.98043 9.32711 6.79289C9.13957 6.60536 8.88522 6.5 8.62 6.5H6.22C7.50578 5.15636 9.21951 4.30265 11.0665 4.08567C12.9136 3.86868 14.7785 4.30198 16.3407 5.31104C17.9028 6.32011 19.0646 7.84191 19.6263 9.61479C20.188 11.3877 20.1145 13.3009 19.4184 15.0254C18.7223 16.7499 17.4472 18.1781 15.8122 19.0643C14.1772 19.9505 12.2845 20.2394 10.4596 19.8813C8.63463 19.5232 6.99147 18.5405 5.81259 17.1022C4.63372 15.6638 3.99279 13.8597 4 12C4 11.7348 3.89464 11.4804 3.70711 11.2929C3.51957 11.1054 3.26522 11 3 11C2.73478 11 2.48043 11.1054 2.29289 11.2929C2.10536 11.4804 2 11.7348 2 12C2 13.9778 2.58649 15.9112 3.6853 17.5557C4.78412 19.2002 6.3459 20.4819 8.17317 21.2388C10.0004 21.9957 12.0111 22.1937 13.9509 21.8079C15.8907 21.422 17.6725 20.4696 19.0711 19.0711C20.4696 17.6725 21.422 15.8907 21.8079 13.9509C22.1937 12.0111 21.9957 10.0004 21.2388 8.17317C20.4819 6.3459 19.2002 4.78412 17.5557 3.6853C15.9112 2.58649 13.9778 2 12 2ZM12 8C11.7348 8 11.4804 8.10536 11.2929 8.29289C11.1054 8.48043 11 8.73478 11 9V12C11 12.2652 11.1054 12.5196 11.2929 12.7071C11.4804 12.8946 11.7348 13 12 13H14C14.2652 13 14.5196 12.8946 14.7071 12.7071C14.8946 12.5196 15 12.2652 15 12C15 11.7348 14.8946 11.4804 14.7071 11.2929C14.5196 11.1054 14.2652 11 14 11H13V9C13 8.73478 12.8946 8.48043 12.7071 8.29289C12.5196 8.10536 12.2652 8 12 8Z" />
    </svg>
  );
}

export function SettingsIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="black"
      {...props}
    >
      <path d="M19.8999 12.6599C19.7396 12.4774 19.6512 12.2428 19.6512 11.9999C19.6512 11.757 19.7396 11.5224 19.8999 11.3399L21.1799 9.89989C21.3209 9.74256 21.4085 9.5446 21.4301 9.3344C21.4516 9.12421 21.4061 8.91258 21.2999 8.72989L19.2999 5.2699C19.1948 5.08742 19.0348 4.94277 18.8426 4.85658C18.6505 4.77039 18.4361 4.74705 18.2299 4.7899L16.3499 5.1699C16.1107 5.21932 15.8616 5.17948 15.6498 5.0579C15.4379 4.93631 15.2779 4.74138 15.1999 4.5099L14.5899 2.6799C14.5228 2.48127 14.395 2.30876 14.2245 2.18674C14.0541 2.06471 13.8495 1.99935 13.6399 1.9999H9.6399C9.42183 1.98851 9.20603 2.04882 9.02546 2.17161C8.84489 2.2944 8.70948 2.47291 8.6399 2.6799L8.0799 4.5099C8.0019 4.74138 7.84187 4.93631 7.63001 5.0579C7.41815 5.17948 7.16911 5.21932 6.9299 5.1699L4.9999 4.7899C4.80445 4.76228 4.6052 4.79312 4.42724 4.87853C4.24929 4.96395 4.1006 5.10012 3.9999 5.2699L1.9999 8.72989C1.89106 8.91054 1.84212 9.12098 1.86008 9.33112C1.87804 9.54126 1.96198 9.74034 2.0999 9.89989L3.3699 11.3399C3.53022 11.5224 3.61863 11.757 3.61863 11.9999C3.61863 12.2428 3.53022 12.4774 3.3699 12.6599L2.0999 14.0999C1.96198 14.2595 1.87804 14.4585 1.86008 14.6687C1.84212 14.8788 1.89106 15.0892 1.9999 15.2699L3.9999 18.7299C4.10499 18.9124 4.26502 19.057 4.45715 19.1432C4.64928 19.2294 4.86372 19.2527 5.0699 19.2099L6.9499 18.8299C7.18911 18.7805 7.43815 18.8203 7.65001 18.9419C7.86187 19.0635 8.0219 19.2584 8.0999 19.4899L8.7099 21.3199C8.77948 21.5269 8.91489 21.7054 9.09546 21.8282C9.27603 21.951 9.49183 22.0113 9.7099 21.9999H13.7099C13.9195 22.0004 14.1241 21.9351 14.2945 21.8131C14.465 21.691 14.5928 21.5185 14.6599 21.3199L15.2699 19.4899C15.3479 19.2584 15.5079 19.0635 15.7198 18.9419C15.9316 18.8203 16.1807 18.7805 16.4199 18.8299L18.2999 19.2099C18.5061 19.2527 18.7205 19.2294 18.9126 19.1432C19.1048 19.057 19.2648 18.9124 19.3699 18.7299L21.3699 15.2699C21.4761 15.0872 21.5216 14.8756 21.5001 14.6654C21.4785 14.4552 21.3909 14.2572 21.2499 14.0999L19.8999 12.6599ZM18.4099 13.9999L19.2099 14.8999L17.9299 17.1199L16.7499 16.8799C16.0297 16.7327 15.2805 16.855 14.6445 17.2237C14.0085 17.5924 13.53 18.1817 13.2999 18.8799L12.9199 19.9999H10.3599L9.9999 18.8599C9.76975 18.1617 9.29128 17.5724 8.6553 17.2037C8.01932 16.835 7.27012 16.7127 6.5499 16.8599L5.3699 17.0999L4.0699 14.8899L4.8699 13.9899C5.36185 13.4399 5.63383 12.7278 5.63383 11.9899C5.63383 11.252 5.36185 10.5399 4.8699 9.98989L4.0699 9.0899L5.3499 6.88989L6.5299 7.1299C7.25012 7.27712 7.99932 7.15478 8.6353 6.78609C9.27128 6.41741 9.74975 5.82805 9.9799 5.1299L10.3599 3.9999H12.9199L13.2999 5.13989C13.53 5.83805 14.0085 6.42741 14.6445 6.79609C15.2805 7.16478 16.0297 7.28712 16.7499 7.13989L17.9299 6.8999L19.2099 9.11989L18.4099 10.0199C17.9235 10.5687 17.6549 11.2766 17.6549 12.0099C17.6549 12.7432 17.9235 13.4511 18.4099 13.9999ZM11.6399 7.9999C10.8488 7.9999 10.0754 8.23449 9.41761 8.67402C8.75982 9.11354 8.24713 9.73826 7.94438 10.4692C7.64163 11.2001 7.56241 12.0043 7.71675 12.7803C7.8711 13.5562 8.25206 14.2689 8.81147 14.8283C9.37088 15.3877 10.0836 15.7687 10.8595 15.923C11.6355 16.0774 12.4397 15.9982 13.1706 15.6954C13.9015 15.3927 14.5262 14.88 14.9658 14.2222C15.4053 13.5644 15.6399 12.791 15.6399 11.9999C15.6399 10.939 15.2185 9.92161 14.4683 9.17147C13.7182 8.42132 12.7008 7.9999 11.6399 7.9999ZM11.6399 13.9999C11.2443 13.9999 10.8577 13.8826 10.5288 13.6628C10.1999 13.4431 9.94351 13.1307 9.79214 12.7653C9.64076 12.3998 9.60116 11.9977 9.67833 11.6097C9.7555 11.2218 9.94598 10.8654 10.2257 10.5857C10.5054 10.306 10.8618 10.1155 11.2497 10.0383C11.6377 9.96115 12.0398 10.0008 12.4053 10.1521C12.7707 10.3035 13.0831 10.5599 13.3028 10.8888C13.5226 11.2177 13.6399 11.6043 13.6399 11.9999C13.6399 12.5303 13.4292 13.039 13.0541 13.4141C12.679 13.7892 12.1703 13.9999 11.6399 13.9999Z" />
    </svg>
  );
}

export function GettingStartedIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        d="M18 2H8C6.93913 2 5.92172 2.42143 5.17157 3.17157C4.42143 3.92172 4 4.93913 4 6V18C4 19.0609 4.42143 20.0783 5.17157 20.8284C5.92172 21.5786 6.93913 22 8 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V4C20 3.46957 19.7893 2.96086 19.4142 2.58579C19.0391 2.21071 18.5304 2 18 2ZM6 6C6 5.46957 6.21071 4.96086 6.58579 4.58579C6.96086 4.21071 7.46957 4 8 4H18V14H8C7.29504 14.003 6.60399 14.1964 6 14.56V6ZM8 20C7.46957 20 6.96086 19.7893 6.58579 19.4142C6.21071 19.0391 6 18.5304 6 18C6 17.4696 6.21071 16.9609 6.58579 16.5858C6.96086 16.2107 7.46957 16 8 16H18V20H8ZM10 8H14C14.2652 8 14.5196 7.89464 14.7071 7.70711C14.8946 7.51957 15 7.26522 15 7C15 6.73478 14.8946 6.48043 14.7071 6.29289C14.5196 6.10536 14.2652 6 14 6H10C9.73478 6 9.48043 6.10536 9.29289 6.29289C9.10536 6.48043 9 6.73478 9 7C9 7.26522 9.10536 7.51957 9.29289 7.70711C9.48043 7.89464 9.73478 8 10 8Z"
        fill="#DBC5FF"
      />
    </svg>
  );
}

export function SupportIcon1(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="black"
      {...props}
    >
      <path d="M19.2901 3.71014C19.3831 3.80387 19.4937 3.87826 19.6156 3.92903C19.7374 3.9798 19.8681 4.00594 20.0001 4.00594C20.1322 4.00594 20.2629 3.9798 20.3847 3.92903C20.5066 3.87826 20.6172 3.80387 20.7101 3.71014C20.7985 3.61294 20.8695 3.50132 20.9201 3.38014C20.9714 3.25992 20.9986 3.13082 21.0001 3.00014C21.0009 2.86853 20.9757 2.73807 20.9259 2.61623C20.8761 2.4944 20.8028 2.38358 20.7101 2.29014L20.5601 2.17014C20.5044 2.13229 20.4439 2.10203 20.3801 2.08014C20.198 2.00356 19.9973 1.98264 19.8033 2.02001C19.6093 2.05739 19.4308 2.15138 19.2901 2.29014C19.1975 2.38358 19.1241 2.4944 19.0744 2.61623C19.0246 2.73807 18.9994 2.86853 19.0001 3.00014C19.0017 3.13082 19.0289 3.25992 19.0801 3.38014C19.1308 3.50132 19.2018 3.61294 19.2901 3.71014ZM20.0001 5.00014C19.7349 5.00014 19.4806 5.1055 19.293 5.29303C19.1055 5.48057 19.0001 5.73492 19.0001 6.00014V10.0001C19.0001 10.2654 19.1055 10.5197 19.293 10.7072C19.4806 10.8948 19.7349 11.0001 20.0001 11.0001C20.2654 11.0001 20.5197 10.8948 20.7072 10.7072C20.8948 10.5197 21.0001 10.2654 21.0001 10.0001V6.00014C21.0001 5.73492 20.8948 5.48057 20.7072 5.29303C20.5197 5.1055 20.2654 5.00014 20.0001 5.00014ZM20.0601 13.0001C19.7977 12.9691 19.5336 13.0431 19.3257 13.2061C19.1177 13.3692 18.9827 13.6079 18.9501 13.8701C18.738 15.5635 17.9146 17.121 16.6348 18.2499C15.3549 19.3788 13.7067 20.0012 12.0001 20.0001H6.41014L7.05014 19.3701C7.23639 19.1828 7.34093 18.9293 7.34093 18.6651C7.34093 18.401 7.23639 18.1475 7.05014 17.9601C6.06763 16.9817 5.39784 15.7335 5.12572 14.3739C4.85361 13.0142 4.99142 11.6044 5.52168 10.3232C6.05195 9.04198 6.95078 7.94713 8.10416 7.17747C9.25755 6.40782 10.6135 5.99805 12.0001 6.00014C13.2263 5.99829 14.4308 6.32272 15.4901 6.94014C15.7157 7.04209 15.9711 7.05675 16.2069 6.98126C16.4427 6.90578 16.6421 6.74552 16.7665 6.5315C16.8909 6.31748 16.9315 6.06492 16.8805 5.82268C16.8294 5.58044 16.6904 5.36575 16.4901 5.22014C15.1293 4.41983 13.5789 3.99857 12.0001 4.00014C10.3076 4.0057 8.65094 4.48842 7.22033 5.39288C5.78971 6.29734 4.64313 7.58687 3.91221 9.11346C3.18129 10.64 2.89565 12.3418 3.08808 14.0234C3.28052 15.7049 3.94323 17.2982 5.00014 18.6201L3.29014 20.2901C3.15138 20.4308 3.05739 20.6093 3.02001 20.8033C2.98264 20.9973 3.00356 21.198 3.08014 21.3801C3.15516 21.5628 3.28256 21.7191 3.44628 21.8294C3.61 21.9398 3.80271 21.9992 4.00014 22.0001H12.0001C14.1931 22.0001 16.3106 21.1994 17.9549 19.7485C19.5993 18.2975 20.6572 16.2961 20.9301 14.1201C20.9469 13.9894 20.9377 13.8567 20.903 13.7295C20.8684 13.6024 20.8089 13.4833 20.728 13.3792C20.6472 13.2752 20.5465 13.1881 20.4319 13.123C20.3173 13.058 20.191 13.0162 20.0601 13.0001Z" />
    </svg>
  );
}

export function AboutUsIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        d="M17.0899 2.81982C16.1702 2.06341 15.0933 1.52185 13.9376 1.23467C12.7819 0.947483 11.5767 0.921913 10.4099 1.15982C8.85632 1.47365 7.43093 2.2419 6.31462 3.3671C5.19832 4.49231 4.44141 5.92376 4.13994 7.47982C3.91979 8.64655 3.95979 9.84747 4.2571 10.997C4.55441 12.1464 5.1017 13.2162 5.85994 14.1298C6.56363 14.9239 6.96698 15.9393 6.99994 16.9998V19.9998C6.99994 20.7955 7.31601 21.5585 7.87862 22.1211C8.44123 22.6838 9.20429 22.9998 9.99994 22.9998H13.9999C14.7956 22.9998 15.5587 22.6838 16.1213 22.1211C16.6839 21.5585 16.9999 20.7955 16.9999 19.9998V17.1898C17.0335 16.019 17.4637 14.8943 18.2199 13.9998C19.5451 12.3606 20.1697 10.2646 19.9581 8.16745C19.7465 6.07026 18.7158 4.14131 17.0899 2.79982V2.81982ZM14.9999 19.9998C14.9999 20.265 14.8946 20.5194 14.707 20.7069C14.5195 20.8945 14.2652 20.9998 13.9999 20.9998H9.99994C9.73473 20.9998 9.48037 20.8945 9.29284 20.7069C9.1053 20.5194 8.99994 20.265 8.99994 19.9998V18.9998H14.9999V19.9998ZM16.6699 12.7598C15.6644 13.9524 15.0778 15.4419 14.9999 16.9998H12.9999V13.9998C12.9999 13.7346 12.8946 13.4803 12.707 13.2927C12.5195 13.1052 12.2652 12.9998 11.9999 12.9998C11.7347 12.9998 11.4804 13.1052 11.2928 13.2927C11.1053 13.4803 10.9999 13.7346 10.9999 13.9998V16.9998H8.99994C8.97356 15.4679 8.40689 13.9946 7.39994 12.8398C6.73558 12.0438 6.28883 11.0892 6.10325 10.0692C5.91767 9.0491 5.99959 7.99834 6.34103 7.01935C6.68247 6.04037 7.27176 5.16657 8.05146 4.48315C8.83116 3.79973 9.77465 3.33002 10.7899 3.11982C11.6625 2.94017 12.5641 2.95707 13.4293 3.16929C14.2945 3.38152 15.1016 3.78375 15.792 4.34678C16.4824 4.9098 17.0387 5.6195 17.4206 6.42434C17.8025 7.22918 18.0004 8.10897 17.9999 8.99982C18.0073 10.3697 17.537 11.6993 16.6699 12.7598Z"
        fill="#DBC5FF"
      />
    </svg>
  );
}

export function ChevronUpIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      strokeWidth={1.5}
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m4.5 15.75 7.5-7.5 7.5 7.5"
      />
    </svg>
  );
}

export function ArrowUpRightIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      strokeWidth={1.5}
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m4.5 19.5 15-15m0 0H8.25m11.25 0v11.25"
      />
    </svg>
  );
}

export function CameraIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="15"
      height="15"
      viewBox="0 0 15 15"
      fill="none"
      {...props}
    >
      <path
        d="M11.3604 4.46692H10.6545L10.478 3.91545C10.3636 3.59187 10.1514 3.31187 9.87077 3.11426C9.59016 2.91664 9.25503 2.81119 8.91182 2.81251H6.08829C5.74172 2.81316 5.4041 2.92263 5.12309 3.12547C4.84208 3.32832 4.63186 3.61429 4.52212 3.94303L4.34565 4.4945H3.63976C3.20099 4.4945 2.78018 4.6688 2.46992 4.97906C2.15966 5.28933 1.98535 5.71013 1.98535 6.14891V10.5607C1.98535 10.9995 2.15966 11.4203 2.46992 11.7305C2.78018 12.0408 3.20099 12.2151 3.63976 12.2151H11.3604C11.7991 12.2151 12.2199 12.0408 12.5302 11.7305C12.8405 11.4203 13.0148 10.9995 13.0148 10.5607V6.14891C13.0184 5.92934 12.9783 5.71124 12.8968 5.50732C12.8153 5.30341 12.694 5.11776 12.5401 4.9612C12.3861 4.80464 12.2024 4.6803 11.9999 4.59544C11.7974 4.51058 11.58 4.46689 11.3604 4.46692ZM11.9118 10.5331C11.9118 10.6794 11.8537 10.8196 11.7503 10.923C11.6469 11.0265 11.5066 11.0846 11.3604 11.0846H3.63976C3.4935 11.0846 3.35324 11.0265 3.24981 10.923C3.14639 10.8196 3.08829 10.6794 3.08829 10.5331V6.12134C3.08829 5.97508 3.14639 5.83481 3.24981 5.73139C3.35324 5.62797 3.4935 5.56987 3.63976 5.56987H4.7427C4.86296 5.57614 4.98197 5.54288 5.08155 5.47517C5.18113 5.40746 5.2558 5.30901 5.29418 5.19487L5.59197 4.29045C5.62896 4.18092 5.69945 4.08578 5.79346 4.01849C5.88747 3.9512 6.00025 3.91516 6.11587 3.91545H8.9394C9.05501 3.91516 9.16779 3.9512 9.2618 4.01849C9.35581 4.08578 9.4263 4.18092 9.46329 4.29045L9.76109 5.19487C9.79647 5.30006 9.86278 5.39212 9.95133 5.45903C10.0399 5.52593 10.1466 5.56456 10.2574 5.56987H11.3604C11.5066 5.56987 11.6469 5.62797 11.7503 5.73139C11.8537 5.83481 11.9118 5.97508 11.9118 6.12134V10.5331ZM7.50006 5.56987C7.06378 5.56987 6.63729 5.69924 6.27454 5.94162C5.91178 6.18401 5.62905 6.52852 5.46209 6.93159C5.29513 7.33467 5.25145 7.77819 5.33656 8.20609C5.42167 8.63399 5.63176 9.02704 5.94026 9.33554C6.24876 9.64404 6.64181 9.85413 7.06971 9.93924C7.49761 10.0244 7.94114 9.98068 8.34421 9.81372C8.74728 9.64676 9.0918 9.36403 9.33418 9.00127C9.57657 8.63851 9.70594 8.21203 9.70594 7.77575C9.70594 7.19071 9.47354 6.62964 9.05985 6.21595C8.64617 5.80227 8.08509 5.56987 7.50006 5.56987ZM7.50006 8.87869C7.28192 8.87869 7.06867 8.814 6.8873 8.69281C6.70592 8.57162 6.56455 8.39936 6.48107 8.19782C6.39759 7.99629 6.37575 7.77452 6.41831 7.56057C6.46087 7.34662 6.56591 7.1501 6.72016 6.99585C6.87441 6.8416 7.07093 6.73656 7.28488 6.694C7.49883 6.65144 7.7206 6.67328 7.92213 6.75676C8.12367 6.84024 8.29593 6.98161 8.41712 7.16299C8.53831 7.34436 8.603 7.55761 8.603 7.77575C8.603 8.06827 8.4868 8.3488 8.27995 8.55564C8.07311 8.76249 7.79258 8.87869 7.50006 8.87869Z"
        fill="white"
      />
    </svg>
  );
}

export function UpDownIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
    >
      <path
        d="M14.8861 10.3981L13.7393 9.34805L13.0391 8.70363C12.7426 8.43212 12.2603 8.43212 11.9637 8.70363L10.1131 10.3981C9.87019 10.6205 10.0452 11 10.3846 11H12.3889H14.6146C14.9576 11 15.1291 10.6205 14.8861 10.3981Z"
        fill="#BDBDBE"
      />
      <path
        d="M14.8861 15.1019L13.7393 16.1519L13.0391 16.7964C12.7426 17.0679 12.2603 17.0679 11.9637 16.7964L10.1131 15.1019C9.87019 14.8795 10.0452 14.5 10.3846 14.5H12.3889H14.6146C14.9576 14.5 15.1291 14.8795 14.8861 15.1019Z"
        fill="#617889"
      />
    </svg>
  );
}

export function ArrowsRightLeftIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      strokeWidth={1.5}
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"
      />
    </svg>
  );
}

export function ShieldCheckIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      strokeWidth={1.5}
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"
      />
    </svg>
  );
}

export function QuestionMarkCircleIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      fill="none"
      strokeWidth={1.5}
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z"
      />
    </svg>
  );
}

export function CopyAccNumberIcon(props: SVGAttributes<SVGElement>) {
  return (
    <svg
      width="24"
      height="22"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M21 9.44C20.9896 9.34813 20.9695 9.25763 20.94 9.17V9.08C20.8919 8.97718 20.8278 8.88267 20.75 8.8L14.75 2.8C14.6673 2.72222 14.5728 2.65808 14.47 2.61C14.4402 2.60576 14.4099 2.60576 14.38 2.61C14.2784 2.55174 14.1662 2.51434 14.05 2.5H10C9.20435 2.5 8.44129 2.81607 7.87868 3.37868C7.31607 3.94129 7 4.70435 7 5.5V6.5H6C5.20435 6.5 4.44129 6.81607 3.87868 7.37868C3.31607 7.94129 3 8.70435 3 9.5V19.5C3 20.2956 3.31607 21.0587 3.87868 21.6213C4.44129 22.1839 5.20435 22.5 6 22.5H14C14.7956 22.5 15.5587 22.1839 16.1213 21.6213C16.6839 21.0587 17 20.2956 17 19.5V18.5H18C18.7956 18.5 19.5587 18.1839 20.1213 17.6213C20.6839 17.0587 21 16.2956 21 15.5V9.5C21 9.5 21 9.5 21 9.44ZM15 5.91L17.59 8.5H16C15.7348 8.5 15.4804 8.39464 15.2929 8.20711C15.1054 8.01957 15 7.76522 15 7.5V5.91ZM15 19.5C15 19.7652 14.8946 20.0196 14.7071 20.2071C14.5196 20.3946 14.2652 20.5 14 20.5H6C5.73478 20.5 5.48043 20.3946 5.29289 20.2071C5.10536 20.0196 5 19.7652 5 19.5V9.5C5 9.23478 5.10536 8.98043 5.29289 8.79289C5.48043 8.60536 5.73478 8.5 6 8.5H7V15.5C7 16.2956 7.31607 17.0587 7.87868 17.6213C8.44129 18.1839 9.20435 18.5 10 18.5H15V19.5ZM19 15.5C19 15.7652 18.8946 16.0196 18.7071 16.2071C18.5196 16.3946 18.2652 16.5 18 16.5H10C9.73478 16.5 9.48043 16.3946 9.29289 16.2071C9.10536 16.0196 9 15.7652 9 15.5V5.5C9 5.23478 9.10536 4.98043 9.29289 4.79289C9.48043 4.60536 9.73478 4.5 10 4.5H13V7.5C13 8.29565 13.3161 9.05871 13.8787 9.62132C14.4413 10.1839 15.2044 10.5 16 10.5H19V15.5Z"
        fill="#7928FF"
      />
    </svg>
  );
}
