"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { ReactNode } from "react";

function Layout({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  return (
    <div>
      <nav className="bg-[#fcfcfc]">
        <ul className="flex overflow-auto">
          <li>
            <Link
              href={`/admin/clients/${pathname.split("/")[3]}`}
              className={`px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${
                pathname === `/admin/clients/${pathname.split("/")[3]}`
                  ? "border-b-[#71bbff] text-[#71bbff]"
                  : ""
              }`}
            >
              Profile
            </Link>
          </li>
          <li>
            <Link
              href={`/admin/clients/${pathname.split("/")[3]}/business-profile`}
              className={`px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${
                pathname.startsWith(
                  `/admin/clients/${pathname.split("/")[3]}/business-profile`
                )
                  ? "border-b-[#71bbff] text-[#71bbff]"
                  : ""
              }`}
            >
              Business Profile
            </Link>
          </li>
          <li>
            <Link
              href={`/admin/clients/${pathname.split("/")[3]}/transactions`}
              className={`px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${
                pathname.startsWith(
                  `/admin/clients/${pathname.split("/")[3]}/transactions`
                )
                  ? "border-b-[#71bbff] text-[#71bbff]"
                  : ""
              }`}
            >
              Transactions
            </Link>
          </li>
          <li>
            <Link
              href={`/admin/clients/${pathname.split("/")[3]}/admins`}
              className={`px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${
                pathname.startsWith(
                  `/admin/clients/${pathname.split("/")[3]}/admins`
                )
                  ? "border-b-[#71bbff] text-[#71bbff]"
                  : ""
              }`}
            >
              Admins
            </Link>
          </li>
          <li>
            <Link
              href={`/admin/clients/${pathname.split("/")[3]}/wallets`}
              className={`px-4 w-fit text-nowrap py-4 block font-light border-b-2 border-transparent text-sm ${
                pathname.startsWith(
                  `/admin/clients/${pathname.split("/")[3]}/wallets`
                )
                  ? "border-b-[#71bbff] text-[#71bbff]"
                  : ""
              }`}
            >
              Wallets
            </Link>
          </li>
        </ul>
      </nav>

      <main>{children}</main>
    </div>
  );
}

export default Layout;
