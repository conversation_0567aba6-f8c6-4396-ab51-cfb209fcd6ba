import useStore from '@/store'
import React, { useState } from 'react'
import { ChevronLeftIcon, InformationCircleIcon } from '../icons';
import QRCode from "react-qr-code";
import CryptoIcon from '../CryptoIcon';

function DepositWalletModal({ wallet }: { wallet: Record<string, any> }) {
  const { setModal } = useStore();
  const [copiedAddress, setCopiedAddress] = useState(false);
  return (
    <div className='rounded-md h-fit max-w-80 md:max-w-96 grow border bg-white pb-5'>
      <div className='bg-[#fcfcfc] rounded-tl-md rounded-tr-md p-4'>
        <button onClick={() => setModal(null)} className="flex items-center">
          <ChevronLeftIcon className="w-4 h-4 stroke-[2px]" />
          <p className='text-sm font-semibold'>Go Back</p>
        </button>
      </div>

      <div className='bg-white p-4 rounded-bl-md rounded-br-md'>
        <p className="text-center border-b max-w-fit px-10 mx-auto py-1.5 text-sm">Deposit details</p>
      </div>

      <div className={`flex flex-col gap-3 justify-center px-4 ${wallet.walletType === "FIAT" ? "" : "items-center text-center"} mt-3`}>
        <CryptoIcon assetType={wallet?.symbol} className="w-7 h-7 mx-auto" />
        <p className='text-sm mx-auto w-[260px] text-center'>{wallet?.name} Wallet</p>

        <p className="text-xs text-appGrayTextLight mx-auto w-[260px]">
          {wallet.walletType === "FIAT" ? <><span className='text-appGrayText text-sm'>Bank Name:</span> {wallet.bank_name}</> : wallet.network}
        </p>

        {wallet.walletType === "CRYPTO" ? <QRCode
          value={wallet.address}
          className="w-[158px] h-[158px] mx-auto border-2 border-blue p-2 rounded mt-4"
        /> : null}

        <div className='mx-auto w-[260px]'>
          {wallet.walletType === "FIAT" ? <p className='max-w-[260px] leading-7 text-sm'><span>Account Number:</span> {wallet.account_num}</p> : <p className='max-w-[250px] leading-7 break-words text-sm text-center'>{wallet.address}</p>}
        </div>

        <button onClick={() => {
          navigator.clipboard.writeText(wallet.walletType === "FIAT" ? wallet.account_num : wallet.address)
            .then(() => {
              setCopiedAddress(true);
              setTimeout(() => setCopiedAddress(false), 2000);
            });
        }} className='bg-appPurple text-white rounded py-2 px-4 text-sm w-[260px] block mx-auto'>{copiedAddress ? "Copied" : wallet.walletType === "FIAT" ? "Copy Account Number" : "Copy Address"}</button>

        <div className="border rounded bg-[#fdfdfd] p-2 max-w-[260px] mx-auto">
          <div className='flex gap-1.5 items-start'>
            <InformationCircleIcon className='w-4 stroke-[#71bbff] mt-1' />
            <p className='text-[10px] text-appGrayText'>
              {wallet.walletType === "CRYPTO" ? "Make sure you send your crypto assets to this address so you don&apos;t permanently loose your funds" : "Make sure you send your funds to this bank account so you don&apos;t permanently loose your funds"}</p>
          </div>
        </div>
      </div >
    </div>
  )
}

export default DepositWalletModal