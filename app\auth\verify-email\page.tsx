"use client";

import { CONSTANTS } from "@/app/utils";
import LoadingSpinner from "@/components/LoadingSpinner";
import Link from "next/link";
import React, { FormEvent, useState } from "react";
import axios from "axios";
import { ToastContainer, toast } from "react-toastify";

function Page() {
  const [loaded, setLoaded] = useState(false);
  const [verified, setVerified] = useState(false);
  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState(false);

  async function verifyOTP() {
    setLoading(true);
    try {
      const res = await axios.post(
        CONSTANTS.SERVER_URL + "/v1/auth/verify-email",
        {
          otp,
        }
      );
      if (res.status.toString().startsWith("2")) {
        window.location.href = "/auth/login";
        setVerified(true);
        setLoading(false);
      } else {
        console.log("email verification failed");
        toast.error("Email Verification failed");
        setLoading(false);
      }
    } catch (err: any) {
      setVerified(false);
      console.log(err);
      if (err.response) {
        console.log(err.response.data);
        toast.error(err.response?.data?.message);
      }
      setLoading(false);
    }
  }

  return (
    <div className="flex items-center justify-center px-4 py-10 md:py-4 md:h-[550px] w-full">
      <div className="grow max-w-md">
        <h1 className="text-3xl font-semibold">Verify Email</h1>

        <div className="border-[1px] border-[#cacaca] rounded py-1 px-2 my-4">
          <label htmlFor="password" className="text-xs text-[#999999]">
            Otp
          </label>
          <input
            type="password"
            id="password"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            className="bg-white block w-full outline-none border-none"
          />
        </div>

        {/* <div className="my-4">
          {loaded ? (
            verified ? (
              <p className="py-2.5">You&apos;ll be redirected</p>
            ) : (
              <p className="py-2.5">Link expired</p>
            )
          ) : (
            <LoadingSpinner className="mx-auto" />
          )}
        </div> */}
        <button
          className="bg-appPurple text-white rounded-md py-2.5 w-full block mt-2 text-sm"
          onClick={verifyOTP}
        >
          {!loading ? "Proceed" : <LoadingSpinner className="mx-auto" />}
        </button>
        <Link
          href={"/auth/resend-otp"}
          className="border border-appPurple text-appPurple rounded-md py-2.5 w-full block mt-2 text-sm text-center"
        >
          Resend otp
        </Link>
      </div>
    </div>
  );
}

export default Page;
