"use client";

import { CONSTANTS } from "@/app/utils";
import LoadingSpinner from "@/components/LoadingSpinner";
import Link from "next/link";
import React, { FormEvent,useState } from "react";
import axios from "axios";
import { ToastContainer, toast } from "react-toastify";

function Page() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);

  async function sendOTP() {
    setLoading(true);
    try {
      const res = await axios.post(CONSTANTS.SERVER_URL + "/v1/auth/send-otp", {
        email,
      });
      if (res.status.toString().startsWith("2")) {
        window.location.href = "/auth/verify-email";
        setLoading(false);
      } else {
        console.log("email could not be sent");
        toast.error("Email could not be sent");
        setLoading(false);
      }
    } catch (err: any) {
      setLoading(false);
      console.log(err);
      if (err.response) {
        console.log(err.response.data);
        toast.error(err.response?.data?.message);
      }
      setLoading(false);
    }
  }

  return (
    <div className="flex items-center justify-center px-4 py-10 md:py-4 md:h-[550px] w-full">
      <div className="grow max-w-md">
        <h1 className="text-3xl font-semibold">
          Resend Email otp for verification
        </h1>

        <div className="border-[1px] border-[#cacaca] rounded py-1 px-2 my-4">
          <label htmlFor="password" className="text-xs text-[#999999]">
            Email
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="bg-white block w-full outline-none border-none"
          />
        </div>
        <button
          className="bg-appPurple text-white rounded-md py-2.5 w-full block mt-2 text-sm"
          onClick={sendOTP}
        >
          {!loading ? "Resend otp" : <LoadingSpinner className="mx-auto" />}
        </button>
        <Link
          href={"/auth/login"}
          className="border border-appPurple text-appPurple rounded-md py-2.5 w-full block mt-2 text-sm text-center"
        >
          Back to Login
        </Link>
      </div>
    </div>
  );
}

export default Page;
