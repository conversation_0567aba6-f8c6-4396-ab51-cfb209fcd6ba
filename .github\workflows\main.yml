---
name: <PERSON>uild and Push Flincap-frontend Image to AWS ECR
on:
  push:
    branches: [ test ]
jobs:
  build-and-push:
    name: Build and Push to ECR
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-2

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Automatic Tagging of Releases
      id: increment-git-tag
      run: |
        bash ./deployment/git_update.sh -v major

    - name: Build, Tag, and Push the Image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: flincap-frontend
        IMAGE_TAG: ${{ steps.increment-git-tag.outputs.git-tag }}
        SERVER_URL: ${{ secrets.FLINCAP_SERVER_URL }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG --build-arg SERVER_URL=$SERVER_URL .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG