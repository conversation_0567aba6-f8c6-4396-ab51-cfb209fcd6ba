import { useEffect, useState, FormEvent } from "react";
import useStore from "@/store";
import { ChevronLeftIcon, XMarkIcon } from "../icons";
import { api } from "@/app/utils";
import { toast } from "react-toastify";
import LoadingSpinner from "@/components/LoadingSpinner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
  DialogFooter,
} from "@/components/ui/dialog";

export default function SetMarginModal({
  defaultMargin,
  appData,
  setAppData,
}: any) {
  const [loading, setLoading] = useState(false);

  const updateDefaultMargin = async (e: FormEvent) => {
    e.preventDefault();

    if (loading) return;

    setLoading(true);
    api
      .post("/v1/wallets/margin", {
        sellMargin: Number(appData.sellMargin),
        buyMargin: Number(appData.buyMargin),
      })
      .then((res) => {
        console.log(res);
        if (res.status === 201) {
          toast.info(res.data.message);
        }
      })
      .catch((err) => {
        console.log(err);
        toast.info("Update failed");
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Dialog>
      <DialogTrigger className="ml-auto">
        <button
          className={`bg-[#7928FF] text-sm rounded-md px-4 py-1 text-white font-circularStd ${
            defaultMargin
              ? "disabled:cursor-not-allowed disabled:bg-[#917ab6]"
              : ""
          }`}
          disabled={defaultMargin}
        >
          Set margin
        </button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogClose className="text-left flex items-center">
            <ChevronLeftIcon className="w-3 h-3" />
            <p className=" font-circularStd">Go back</p>
          </DialogClose>
          <DialogTitle className="text-center">
            <h3 className="font-circularStd">Exchange Margin</h3>
          </DialogTitle>
          <DialogDescription>
            <div className="col-span-5 mt-5 border px-4 py-4 rounded">
              <div className="flex items-center gap-2">
                <div className="flex grow border rounded px-1.5 py-1.5">
                  <input
                    type="number"
                    placeholder="1.2"
                    value={appData.sellMargin}
                    onChange={(e: any) => {
                      console.log(e.target.value);
                      setAppData((prev: any) => ({
                        ...prev,
                        sellMargin: e.target.value,
                      }));
                    }}
                    className="grow border-none outline-none w-12 text-right"
                  />
                  <p>%</p>
                </div>
              </div>
              <button
                onClick={updateDefaultMargin}
                className="bg-appPurple text-white block w-full rounded py-1 mt-2"
              >
                {loading ? <LoadingSpinner className="mx-auto" /> : "Update"}
              </button>
            </div>
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
