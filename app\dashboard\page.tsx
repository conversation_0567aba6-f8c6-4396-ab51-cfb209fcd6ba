"use client";
import { useEffect, useState } from "react";
import { api, CONSTANTS, getToken, getTokenName } from "../utils";
import DataItem from "@/components/DataItem";
import { exportReceiptAsImage, generatePDF } from "@/lib/utils";
import RecentTransactionsTable from "@/components/dashboard/RecentTransactionsTable";
import { toast } from "sonner";

import { Data } from "@/types/types";
import axios from "axios";
import useStore from "@/store";

type InviteStatusType = {
  accepted: boolean;
  email: string;
  id: string;
  organisationId: string;
  role: string;
};

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";

function Page() {
  const [transactions, setTransactions] = useState([]);
  const [inviteDetails, setInviteDetails] = useState<InviteStatusType>();

  const [data, setData] = useState<Data>({
    recentHistory: [],
    totalBalance: 0,
    totalDeposits: 0,
    totalSent: 0,
    wallets: [],
  });
  const [copied, setCopied] = useState(false);
  const [bankData, setBankData] = useState({
    account_name: "",
    account_number: "",
    bank_code: "",
    bank_name: "",
    balance: 0,
  });
  const [userId, setUserId] = useState<string>("");
  const { fiatProvider } = useStore();

  async function fetchAuth() {
    try {
      const res = await axios.get(CONSTANTS.SERVER_URL + "/v1/auth", {
        headers: { Authorization: "Bearer " + getToken() },
      });
      if (res.status === 403) return;
      else setUserId(res.data.data.id);
    } catch (err: any) {
      if (err.response) {
        if (err.response.status === 403) {
          // ------ Not logged in
        }
      }
      console.log(err);
    }
  }

  useEffect(() => {
    if (typeof window != "undefined") {
      api
        .get("/v1/wallets/transactions")
        .then((res) => {
          setTransactions(res.data.data);
        })
        .catch((err) => {
          console.log(err);
        });
      api
        .get("/v1/wallets/dashboard-data")
        .then((res) => {
          setData(res.data.data);
          const fiat = res.data.data.wallets.find(
            (wallet: any) => wallet.walletType === "FIAT"
          );
          setBankData({
            account_name: fiat.account_name,
            account_number: fiat.account_num,
            bank_code: fiat.bank_code,
            bank_name: fiat.bank_name,
            balance: fiat.balance,
          });
        })
        .catch((err) => {
          console.log(err);
        });

      fetchAuth();
    }
  }, []);

  useEffect(() => {
    if (typeof window !== undefined) {
      async function checkInvite() {
        const response = await api.get("/v1/settings/check-for-team-invite");
        console.log(response);
        if (response.status === 200 && response.data.data) {
          setInviteDetails(response.data.data);
        } else {
          setInviteDetails(undefined);
        }
      }
      checkInvite();
    }
  }, []);

  function InviteAlert() {
    const [loading, setLoading] = useState(false);
    const open = !!inviteDetails && inviteDetails.accepted === false;

    const handleAccept = async () => {
      if (!inviteDetails) return;
      setLoading(true);
      try {
        const response = await api.post("/v1/settings/accept-team-invite");
        console.log(response);
        setInviteDetails(undefined);
        toast.success("Invite accepted");
      } catch (err) {
        toast.error("Failed to accept invite");
      } finally {
        setLoading(false);
      }
    };
    const handleDecline = async () => {
      if (!inviteDetails) return;
      setLoading(true);
      try {
        const response = await api.post("/v1/settings/reject-team-invite");
        console.log(response);
        setInviteDetails(undefined);
        toast.success("Invitation declined");
      } catch (err) {
        toast.error("Failed to decline invite");
      } finally {
        setLoading(false);
      }
    };

    return (
      <AlertDialog open={open}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Team Invite</AlertDialogTitle>
            <AlertDialogDescription>
              {inviteDetails?.email
                ? `You have been invited to join an organization as ${inviteDetails.role} (${inviteDetails.email}).`
                : "You have a pending team invite."}
              <br />
              Would you like to accept this invitation?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDecline} disabled={loading}>
              {loading ? "Declining..." : "Decline"}
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleAccept} disabled={loading}>
              {loading ? "Accepting..." : "Accept"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return (
    <div className="relative">
      <section className="md:px-6 px-4 pt-6 mb-6">
        <div className="">
          <div className="grid lg:grid-cols-3 grid-cols-1 lg:gap-5 gap-2">
            <DataItem
              title="NGN"
              userId={userId}
              amount={Math.abs(bankData.balance)}
              symbol={"₦"}
              bankData={bankData}
              fiat={true}
              wallets={data.wallets?.filter(
                (wallet: any) => wallet.walletType === "FIAT"
              )}
            />
            <DataItem
              title="USD"
              userId={userId}
              amount={
                data.wallets?.filter(
                  (wallet: any) => wallet.walletType === "USD"
                )[0]?.balance
              }
              symbol={"$"}
              fiat={false}
              wallets={data.wallets?.filter(
                (wallet: any) => wallet.walletType === "USD"
              )}
            />
          </div>
        </div>
      </section>

      <RecentTransactionsTable
        transactions={transactions}
        fiatBankData={bankData}
        exportReceiptAsImage={exportReceiptAsImage}
        generatePDF={generatePDF}
      />
      <InviteAlert />
    </div>
  );
}

export default Page;
