{"name": "flincap-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:docker": "docker build . -t flincap-frontend", "run:docker": "docker run -p 3000:3000 --env-file=./.env flincap-frontend", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"@headlessui/react": "^1.7.18", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.7", "@tremor/react": "^2.11.0", "antd": "^5.16.1", "axios": "^1.6.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.10", "embla-carousel-auto-scroll": "^8.1.3", "embla-carousel-autoplay": "^8.1.3", "embla-carousel-react": "^8.1.3", "framer-motion": "^11.0.3", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "jspdf": "^2.5.1", "lodash": "^4.17.21", "lucide-react": "^0.379.0", "moment": "^2.30.1", "next": "13.5.6", "next-themes": "^0.4.4", "react": "^18", "react-calendly": "^4.3.0", "react-chartjs-2": "^5.2.0", "react-datepicker": "^6.6.0", "react-day-picker": "^9.8.1", "react-dom": "^18", "react-floating-whatsapp": "^5.0.8", "react-hook-form": "^7.56.4", "react-otp-input": "^3.1.1", "react-qr-code": "^2.0.12", "react-query": "^3.39.3", "react-select": "^5.8.0", "react-switch": "^7.0.0", "react-timer-hook": "^3.0.7", "react-toastify": "^10.0.4", "sharp": "^0.33.3", "socket.io-client": "^4.7.5", "sonner": "^2.0.1", "speakeasy": "^2.0.0", "svix-react": "^1.10.2", "swiper": "^11.0.5", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.28", "zustand": "^4.5.0"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@jest/globals": "^29.7.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.15", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10", "babel-jest": "^29.7.0", "eslint": "^8", "eslint-config-next": "13.5.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3", "ts-jest": "^29.3.0", "ts-node": "^10.9.2", "typescript": "^5"}}