import React from "react";
import { ScaleButton } from "../animations/animations";
import Link from "next/link";
import Image from "next/image";

export default function BlackBanner() {
  return (
    <aside className="bg-black">
      <div className="max-w-5xl mx-auto py-24 flex flex-col md:flex-row justify-between items-center">
        <div className="text md:block flex flex-col justify-center items-center mb-5">
          <h2 className="text-3xl leading-8 md:text-5xl font-semibold mb-6 text-white">
            <span className="text-[#7928FF] mb-2">Ready to start using</span>{" "}
            <br />
            <span>Flincap?</span>
          </h2>
          <ScaleButton>
            <Link
              href="https://forms.gle/HmaHKiMd2BJ1SGZg7"
              className="bg-appPurple hover:bg-appPurpleDark text-white px-7 md:px-12 py-2.5 md:py-3 rounded-md block w-fit mt-2"
            >
              Get started
            </Link>
          </ScaleButton>
        </div>
        <div className="w-[200px] h-[200px] mt-10 md:mt-0">
          <Image
            src="/images/illustrations/Group.svg"
            alt="Group"
            width={200}
            height={200}
            className="w-full h-full"
          />
        </div>
      </div>
    </aside>
  );
}
