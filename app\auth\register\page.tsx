"use client";

import Image from "next/image";
import Select from "react-select";
import React, { FormEvent, useState } from "react";
import { toast } from "react-toastify";
import COUNTRIES from "@/countries.json";
import expand from "@/assets/images/expanddown.svg";
import Link from "next/link";
import axios from "axios";
import { CONSTANTS } from "@/app/utils";
import LoadingSpinner from "@/components/LoadingSpinner";

function Page() {
  const [step, setStep] = useState(1);
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [formDetails, setFormDetails] = useState<Record<string, any>>({
    firstName: "",
    lastName: "",
    businessName: "",
    email: "",
    phone: "",
    products: [],
    country: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === "businessName") {
      const letterOnlyRegex = /^[A-Za-z]*$/;

      if (!letterOnlyRegex.test(value)) {
        setErrorMessage("Business name can only contain letters.");
        return;
      } else {
        setErrorMessage("");
      }
    }

    setFormDetails({ ...formDetails, [name]: value });
  };

  const countries = COUNTRIES.map((country) => ({
    value: country,
    label: country,
  }));

  const submitData = async (e: FormEvent) => {
    e.preventDefault();

    if (
      formDetails.firstName &&
      formDetails.lastName &&
      formDetails.businessName &&
      formDetails.email &&
      formDetails.country &&
      formDetails.phone
    ) {
      try {
        setLoading(true);
        const res = await axios.post(CONSTANTS.SERVER_URL + "/v1/auth/signup", {
          fullName: formDetails.firstName + " " + formDetails.lastName,
          email: formDetails.email,
          country: formDetails.country,
          businessName: formDetails.businessName,
          phone: formDetails.phone,
        });

        if (res.status.toString().startsWith("2")) {
          // good resquest/response
          setStep(2);
        } else {
          // bad resquest/response
          setStep(3);
        }
      } catch (err: any) {
        console.log(err);
        if (err.response) {
          setStep(3);
        }
      } finally {
        setLoading(false);
      }
    } else {
      toast.error("Please fill all fields");
    }
  };

  return (
    <div className="flex items-center justify-center px-4 pt-10 md:py-4 md:h-[550px] w-full">
      {
        {
          1: (
            <div className="max-w-lg p-4">
              <h1 className="text-lg md:text-3xl text-[#0A2540] font-semibold">
                Welcome to Flincap
              </h1>
              <p className="text-[#424242] text-sm md:text-base mt-1">
                Choose your account type
              </p>
              <form
                onSubmit={submitData}
                className="mt-8 flex flex-col gap-4 w-full"
              >
                <div className="flex gap-4">
                  <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple max-w-[265px]">
                    <label className="text-[#999999] text-xs font-light">
                      First Name
                    </label>
                    <input
                      name="firstName"
                      value={formDetails.firstName}
                      onChange={handleChange}
                      className="w-full bg-transparent outline-none text-sm"
                    />
                  </div>
                  <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple max-w-[265px]">
                    <label className="text-[#999999] text-xs font-light">
                      Last Name
                    </label>
                    <input
                      name="lastName"
                      value={formDetails.lastName}
                      onChange={handleChange}
                      className="w-full bg-transparent outline-none text-sm"
                    />
                  </div>
                </div>
                <div>
                  <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
                    <label className="text-[#999999] text-xs font-light">
                      Exchange Name
                    </label>
                    <input
                      name="businessName"
                      value={formDetails.businessName}
                      onChange={handleChange}
                      className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
                    />
                  </div>
                  {errorMessage ? (
                    <p className="text-xs text-red-500">{errorMessage}</p>
                  ) : (
                    ""
                  )}
                </div>
                <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
                  <label className="text-[#999999] text-xs font-light">
                    Email Address
                  </label>
                  <input
                    name="email"
                    value={formDetails.email}
                    onChange={handleChange}
                    type="email"
                    className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
                  />
                </div>
                <Select
                  options={countries}
                  placeholder="Select Country"
                  onChange={(e: any) =>
                    setFormDetails({ ...formDetails, country: e.value })
                  }
                  styles={{
                    control: (baseStyles) => ({
                      ...baseStyles,
                      border: "1px solid #CACACA",
                      boxShadow: "none",
                      ":focus-within": {
                        border: "1px solid #7928FF",
                      },
                      padding: "4px 2px 4px 2px",
                      fontSize: "14px",
                    }),
                  }}
                />
                <div className="py-1 px-3 border border-[#CACACA] rounded flex flex-col focus-within:border-appPurple">
                  <label className="text-[#999999] text-xs font-light">
                    Contact Phone Number
                  </label>
                  <input
                    name="phone"
                    value={formDetails.phone}
                    onChange={handleChange}
                    className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
                  />
                </div>
                <button
                  type="submit"
                  className="bg-appPurple text-white py-2 rounded-md font-semibold mt-5"
                >
                  {!loading ? (
                    "Continue"
                  ) : (
                    <LoadingSpinner className="mx-auto" />
                  )}
                </button>
              </form>
            </div>
          ),
          2: (
            <div className="max-w-xl p-4 md:-mt-32 text-center">
              <h1 className="text-lg md:text-3xl text-[#0A2540] font-semibold">
                Account registered
              </h1>
              <p className="text-[#424242] text-sm md:text-base mt-3">
                Your account has been setup. Head of to the login page by <br />{" "}
                clicking the link below.
              </p>
              <div className="mt-8 flex flex-col items-center gap-4">
                <Link
                  href={`/auth/login`}
                  className="bg-appPurple text-white py-2 px-3 rounded-md font-semibold"
                >
                  Login here
                </Link>
                <Link
                  href="/"
                  className="text-appPurple py-2 rounded-md font-semibold w-full"
                >
                  Go home
                </Link>
              </div>
            </div>
          ),
          3: (
            <div className="max-w-xl p-4 md:-mt-32 text-center">
              <h1 className="text-lg md:text-3xl text-[#0A2540] font-semibold">
                Registration error
              </h1>
              <p className="text-[#424242] text-sm md:text-base mt-3">
                {errorMessage ||
                  "Sorry, you may have to contact the sales team before."}{" "}
                or You can try again by <br /> clicking the link below.
              </p>
              <div className="mt-8 flex flex-col items-center gap-4">
                <button
                  onClick={() => setStep(1)}
                  className="bg-appPurple text-white py-2 px-3 rounded-md font-semibold"
                >
                  Try again
                </button>
                <Link
                  href="/"
                  className="text-appPurple py-2 rounded-md font-semibold w-full"
                >
                  Go home
                </Link>
              </div>
            </div>
          ),
        }[step]
      }
    </div>
  );
}

export default Page;
