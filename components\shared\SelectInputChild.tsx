export default function SelectInputChild({
  children,
}: {
  children?: React.ReactNode | string;
}) {
  return (
    <div className="flex flex-col pl-4 py-2 text-base">
      <small className="text-sm font-light text-blue-400 relative before:content-[''] before:absolute before:top-0 before:-left-2 before:w-[1px] before:h-full before:bg-zinc-400 pt-1 pl-2">
        <span className="bg-zinc-400 h-[5px] w-[5px] rounded-full absolute top-[12px] -left-[10px]"></span>
        {children}
      </small>
    </div>
  );
}
