"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import NotransactionIcon from "@/assets/images/no-data.svg";
import { adminApi } from "@/app/utils";
import useStore from "@/store";
import AddAssetModalAdmin from "@/components/modals/AddAssetModalAdmin";
import { MagnifyingGlassIcon } from "@/components/icons";
import WalletBalanceModal from "@/components/modals/WalletBalanceModal";

function Page({ params }: { params: { userId: string } }) {
  const [user, setUser] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const { setModal } = useStore();
  const [walletFilterSearch, setWalletFilterSearch] = useState("");

  const deleteWallet = (walletID: string) => {
    if (!confirm("Are you sure?")) {
      return;
    }
    setLoading(true);
    adminApi
      .delete(`/v1/admin/users/${walletID}/delete-wallet`)
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          window.location.reload();
        }
        console.log(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => setLoading(false));
  };

  const UpdateWalletBalance = (wallet: any) => {
    setModal(<WalletBalanceModal wallet={wallet} />);
  };

  useEffect(() => {
    // Fetch user data
    setLoading(true);
    adminApi
      .get("/v1/admin/users/" + params.userId)
      .then((res) => {
        if (res.status.toString().startsWith("2")) {
          console.log(res.data.data);
          setUser(res.data.data);
        } else {
          console.log("Couldn't fetch user");
        }
      })
      .catch((err) => {
        console.log(err);
        if (err.response.data) {
          alert("User BVN or other data may be invalid");
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params.userId]);

  return (
    <section className="mt-4">
      <div className="px-4 flex flex-wrap gap-2 justify-between">
        <h3 className="font-semibold">Wallets</h3>
        <div className="flex gap-3 justify-between items-center text-sm">
          <p className="text-[#999999]">
            Showing:{" "}
            <select className="border-none outline-none text-appPurple">
              <option defaultValue={"USD"}>USD</option>
            </select>
          </p>
          <p className="text-[#999999]">
            Filter:{" "}
            <select className="border-none outline-none text-appPurple">
              <option defaultValue={"None"}>None</option>
            </select>
          </p>
        </div>
      </div>

      <div className="cards grid md:grid-cols-3 gap-4 px-4 my-4">
        <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
          <h3 className="font-semibold">Total Balance</h3>
          <p className="text-[#999999] mt-1">
            Showing:{" "}
            <select className="border-none outline-none text-appPurple">
              <option defaultValue={"USD"}>USD</option>
            </select>
          </p>

          <h1 className="font-semibold text-xl mt-6">₦0</h1>
        </div>
        <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
          <h3 className="font-semibold">No. of Wallets</h3>
          <div className="flex justify-between">
            <p className="text-[#999999] mt-1">
              Showing:{" "}
              <select className="border-none outline-none text-appPurple">
                <option defaultValue={"All"}>All</option>
              </select>
            </p>
            <button
              onClick={() =>
                setModal(
                  <AddAssetModalAdmin
                    userId={params.userId}
                    wallets={user.wallets.filter(
                      (wallet: any) => wallet.walletType === "CRYPTO"
                    )}
                  />
                )
              }
              className="text-appPurple underline"
            >
              Add Wallet
            </button>
          </div>

          <h1 className="font-semibold text-xl mt-6">
            {user?.wallets?.length}
          </h1>
        </div>
        <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
          <h3 className="font-semibold">Transaction count</h3>
          <p className="text-[#999999] mt-1">
            Showing:{" "}
            <select className="border-none outline-none text-appPurple">
              <option defaultValue={"All"}>All</option>
            </select>
          </p>

          <h1 className="font-semibold text-xl mt-6">0</h1>
        </div>
      </div>

      <div className="flex flex-wrap justify-between gap-6 py-4 px-4 h-auto overflow-x-auto">
        <div className="inp w-full flex items-center gap-2 bg-[#fbfbfb] rounded-md p-2 grow">
          <MagnifyingGlassIcon className="w-4 h-4 text-[#b1b1b1]" />
          <input
            type="text"
            onChange={(e) => setWalletFilterSearch(e.target.value.trim())}
            className="border-none outline-none bg-transparent grow placeholder:text-[#b1b1b1]"
            placeholder="Search wallets"
          />
        </div>
        <table className="w-full">
          <thead className="text-[14px] text-ash rounded-lg bg-ash1 border-b border-b-ash2">
            <tr className="text-left">
              <th className="p-3 font-normal flex items-center gap-2">
                Name/Chain
              </th>
              <th className="p-3 font-normal">Balance</th>
              <th className="p-3 font-normal">Type</th>
              <th className="p-3 font-normal">Address/Account Number</th>
              <th className="p-3 font-normal">Actions</th>
            </tr>
          </thead>
          <tbody>
            {user?.wallets?.length &&
              user.wallets
                .filter((wallet: any) => {
                  if (walletFilterSearch) {
                    return (wallet?.name as string)
                      .toLowerCase()
                      .startsWith(walletFilterSearch.toLowerCase());
                  }
                  return true;
                })
                .map((wallet: any, idx: number) => {
                  return (
                    <tr
                      className="text-xs text-black hover:text-appPurpleDark border-b border-b-ash2 hover:bg-appWhite"
                      key={idx}
                    >
                      <td className="p-3 flex items-center gap-2 whitespace-nowrap">
                        <span className="font-semibold">{wallet.name}</span>
                      </td>
                      <td className="p-3 font-semibold whitespace-nowrap">
                        {wallet.symbol}{" "}
                        {wallet.walletType === "FIAT"
                          ? (wallet.balance | 0).toLocaleString()
                          : wallet.name === "BTC"
                          ? wallet.balance?.toLocaleString(undefined, {
                              minimumFractionDigits: 8,
                              maximumFractionDigits: 8,
                            })
                          : wallet.name === "USDT BEP20"
                          ? wallet.balance?.toLocaleString(undefined, {
                              minimumFractionDigits: 1,
                              maximumFractionDigits: 1,
                            })
                          : wallet.balance?.toLocaleString()}
                      </td>
                      <td className="p-3 whitespace-nowrap">
                        {wallet.walletType}
                      </td>
                      <td className="p-3 whitespace-nowrap">
                        {wallet.walletType === "CRYPTO" ? (
                          <p>{wallet.address}</p>
                        ) : (
                          <>
                            <p>{wallet.account_name}</p>
                            <p className="text-xs font-light mt-1">
                              {wallet.account_num} {wallet.bank_name}
                            </p>
                          </>
                        )}
                      </td>
                      <td className="space-x-2">
                        <button
                          onClick={() => UpdateWalletBalance(wallet)}
                          className="bg-appPurple text-white rounded px-2 py-1"
                        >
                          Update balance
                        </button>
                        <button
                          onClick={() => deleteWallet(wallet.id)}
                          className="bg-red-500 text-white rounded px-2 py-1"
                        >
                          Remove
                        </button>
                      </td>
                    </tr>
                  );
                })}
          </tbody>
        </table>
        {!user?.wallets?.length ? (
          <div className="text-center flex flex-col justify-center items-center min-h-80 mx-auto">
            <Image
              src={NotransactionIcon}
              alt="."
              className="block mx-auto grayscale"
            />
            <p className="my-2.5 text-black">You have no wallet</p>
            <p className="font-light">
              You can create up to 5 wallets.{" "}
              <button
                onClick={() =>
                  setModal(
                    <AddAssetModalAdmin
                      userId={params.userId}
                      wallets={user.wallets.filter(
                        (wallet: any) => wallet.walletType === "CRYPTO"
                      )}
                    />
                  )
                }
                className="text-appPurple underline"
              >
                Click here
              </button>{" "}
              to <br /> add them
            </p>
          </div>
        ) : null}
      </div>
    </section>
  );
}

export default Page;
