"use client";
import {
  OverviewIcon,
  SettingsIcon,
  SignoutIcon,
  SupportIcon1,
  TransactionsIcon,
} from "@/components/icons";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { ReactNode, useEffect, useState } from "react";
import { CONSTANTS, api } from "../utils";
import LoadingSpinner from "@/components/LoadingSpinner";
import useStore, { StoreState } from "@/store";
import axios from "axios";
import NavTop from "@/components/NavTop";
import { createContext, useContext } from "react";
import { MobileNavigationBar } from "@/components/dashboard/MobileNavigationBar";
import { SideNavigationBar } from "@/components/dashboard/side-nav";
import { toast } from "sonner";

const DashboardContext = createContext<{
  userId: string;
  bankData: any;
  transactions: any;
} | null>(null);

export function useDashboardData() {
  const context = useContext(DashboardContext);
  if (!context)
    throw new Error("useDashboardData must be used within DashboardLayout");
  return context;
}

type InviteStatusType = {
  accepted: boolean;
  email: string;
  id: string;
  organisationId: string;
  role: string;
};

function Layout({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { setUser, user, setFiatProvider } = useStore(
    (state: StoreState) => state
  );

  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");
  const [transactions, setTransactions] = useState([]);
  const userId = user?.id;
  const [bankData, setBankData] = useState({
    account_name: "",
    account_number: "",
    bank_code: "",
    bank_name: "",
    balance: 0,
  });
  const [inviteDetails, setInviteDetails] = useState<InviteStatusType>();

  useEffect(() => {
    const fetchFiatProvider = async () => {
      try {
        const response = await api.get("/get-fiat-provider");
        console.log(response);
        if (response.status === 200) {
          setFiatProvider(response.data.data);
          // toast.success(response.data.message);
        }
      } catch (error) {
        console.error(error);
      }
    };
    fetchFiatProvider();
  }, [setFiatProvider]);

  useEffect(() => {
    const auth_token = searchParams.get("auth_token");
    const getAccount = async () => {
      try {
        const response = await api.get("/v1/auth");
        if (response.data.success) {
          setUser(response.data.data);
          setLoading(false);
        } else if (response.data.statusCode.toString().startsWith("4")) {
          setLoading(false);
          logoutUser();
        }
      } catch (error) {
        setLoading(false);
        logoutUser();
      }
    };

    async function verifyOTP() {
      try {
        const res = await axios.get(
          CONSTANTS.SERVER_URL + "/v1/auth/verify-login",
          {
            params: {
              auth_token,
            },
          }
        );
        if (res.status.toString().startsWith("2")) {
          localStorage.setItem("tid", res.data.data.accessToken);
          // window.location.href = "/dashboard";
          await getAccount();
        } else {
          console.log("Login failed");
        }
      } catch (err: any) {
        if (err.response) {
          if (err.response.status == 400) {
            setErrorMessage(err.response.data.message);
          }
        }
      } finally {
        setLoading(false);
      }
    }

    if (auth_token) {
      verifyOTP();
    } else {
      getAccount();
    }
  }, [searchParams, setUser]);

  useEffect(() => {
    if (typeof window != "undefined") {
      api
        .get("/v1/wallets/transactions")
        .then((res) => {
          setTransactions(res.data.data);
        })
        .catch((err) => {
          console.log(err);
        });
      api
        .get("/v1/wallets/dashboard-data")
        .then((res) => {
          const fiat = res.data.data.wallets.find(
            (wallet: any) => wallet.walletType === "FIAT"
          );
          setBankData({
            account_name: fiat.account_name,
            account_number: fiat.account_num,
            bank_code: fiat.bank_code,
            bank_name: fiat.bank_name,
            balance: fiat.balance,
          });
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, []);

  const logoutUser = () => {
    localStorage.removeItem("tid");
    window.location.href = "/auth/login";
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (errorMessage) {
    return (
      <div>
        <header className="border-b">
          <nav className="max-w-7xl mx-auto px-4 py-6">
            <Link href={"/"} className="logo">
              <Image
                src={"/images/flincap-logo.svg"}
                alt="."
                width={100}
                height={50}
                priority
              />
            </Link>
          </nav>
        </header>
        <div className="text-center flex flex-col items-center justify-center pt-9 px-4 pb-6">
          <p className="max-w-xl my-6">
            Uh-oh! It seems like you&apos;ve encounted an error. <br />
            The link you followed might have expired or is no more in use.
          </p>

          <Link
            href="/"
            className="bg-appPurple px-4 py-2 rounded max-w-96 w-full block"
          >
            Go Back
          </Link>
        </div>
      </div>
    );
  }

  return (
    <DashboardContext.Provider value={{ userId, bankData, transactions }}>
      <div className="flex">
        <SideNavigationBar logoutUser={logoutUser} />
        <main className="pb-16 grow">
          <NavTop />
          <div className="overflow-auto h-screen grow pb-10 bg-[#f6f6f8]">
            {children}
          </div>
          <MobileNavigationBar />
        </main>
      </div>
    </DashboardContext.Provider>
  );
}

export default Layout;
