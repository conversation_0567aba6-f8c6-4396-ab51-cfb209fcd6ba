"use client";
import {
  <PERSON><PERSON><PERSON>Cir<PERSON>,
  ArrowUpCircle,
  <PERSON><PERSON>,
  BarChart2,
  <PERSON>ader2,
  <PERSON>otateCw,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { adminApi } from "@/app/utils";
import { isAxiosError } from "axios";
import { DateRange } from "react-day-picker";
import format from "date-fns/format";
import { DateRangePicker } from "./DateRangePicker";

interface AnalyticsData {
  totalTransactionValueNgn: number;
  totalTransactionValueUsd: number;
  totalTransactions: number;
  totalDepositValueNgn: number;
  totalDepositValueUsd: number;
  totalDeposits: number;
  totalWithdrawalValueNgn: number;
  totalWithdrawalValueUsd: number;
  totalWithdrawals: number;
  totalSwapValueNgn: number;
  totalSwapValueUsd: number;
  totalSwaps: number;
}

interface Analytics {
  userId?: string;
}

export default function Analytics({ userId }: Analytics) {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(
    null
  );
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const fetchAnalyticsData = async () => {
    setIsRefreshing(true);
    setError(null);

    const fromDate = dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : "";
    const toDate = dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : "";

    const params = new URLSearchParams();

    if (userId) params.append("userId", userId);
    if (fromDate) params.append("fromDate", fromDate);
    if (toDate) params.append("toDate", toDate);

    try {
      const response = await adminApi.get(
        `/v1/admin/analytics?${params.toString()}`
      );
      setAnalyticsData(response.data);
    } catch (error: any) {
      if (isAxiosError(error) && error.response?.status == 500)
        setError(
          "An error occurred while fetching analytics data. Please try again later."
        );
      else {
        const message = error.response?.data?.message || "An error occurred.";
        setError(message);
      }
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  return (
    <div className="w-full p-4 sm:p-6 lg:p-8 font-circularStd">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold tracking-normal">
          Analytics Overview
        </h2>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <DateRangePicker date={dateRange} setDate={setDateRange} />
          <Button
            variant="outline"
            className="text-white bg-purple-600 hover:bg-purple-700 active:bg-purple-800"
            onClick={fetchAnalyticsData}
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <div className="flex items-center gap-2">
                <Loader2 strokeWidth="3px" className="animate-spin" />
                <p>Refreshing...</p>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <RotateCw strokeWidth="3px" />
                <p>Refresh...</p>
              </div>
            )}
          </Button>
        </div>
        {/* <Button
          variant="outline"
          className="w-md text-white bg-purple-600 hover:bg-purple-700 active:bg-purple-800"
          onClick={fetchAnalyticsData}
          disabled={isRefreshing}
        >
          {isRefreshing ? (
            <div className="flex items-center gap-2">
              <Loader2 strokeWidth="3px" className="animate-spin" />
              <p>Refreshing...</p>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <RotateCw strokeWidth="3px" />
              <p>Refresh...</p>
            </div>
          )}
        </Button> */}
      </div>

      {error ? (
        <div className="flex flex-col items-center justify-center h-32">
          <span className="text-sm italic font-medium mb-2">{error}</span>
        </div>
      ) : analyticsData ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <AnalyticsCard
            icon={<BarChart2 className="text-[#7928FF] w-5 h-5" />}
            valueNgn={analyticsData.totalTransactionValueNgn}
            valueUsd={analyticsData.totalTransactionValueUsd}
            label="Total Transaction Value"
            sub={`${analyticsData.totalTransactions.toLocaleString()} Transactions`}
            bg="from-[#f3e8ff] to-white"
            textColor="text-[#7928FF]"
          />
          <AnalyticsCard
            icon={<ArrowDownCircle className="text-green-600 w-5 h-5" />}
            valueNgn={analyticsData.totalDepositValueNgn}
            valueUsd={analyticsData.totalDepositValueUsd}
            label="Total Deposit Value"
            sub={`${analyticsData.totalDeposits.toLocaleString()} Deposits`}
            bg="from-[#dcfce7] to-white"
            textColor="text-green-600"
          />
          <AnalyticsCard
            icon={<ArrowUpCircle className="text-blue-600 w-5 h-5" />}
            valueNgn={analyticsData.totalWithdrawalValueNgn}
            valueUsd={analyticsData.totalWithdrawalValueUsd}
            label="Total Withdrawal Value"
            sub={`${analyticsData.totalWithdrawals.toLocaleString()} Withdrawals`}
            bg="from-[#dbeafe] to-white"
            textColor="text-blue-600"
          />
          <AnalyticsCard
            icon={<Repeat className="text-orange-500 w-5 h-5" />}
            valueNgn={analyticsData.totalSwapValueNgn}
            valueUsd={analyticsData.totalSwapValueUsd}
            label="Total Swap Value"
            sub={`${analyticsData.totalSwaps.toLocaleString()} Swaps`}
            bg="from-[#fff7e6] to-white"
            textColor="text-orange-500"
          />
        </div>
      ) : (
        <div className="flex justify-center items-center h-32">
          <span className="text-gray-400 text-sm animate-pulse">
            Loading analytics...
          </span>
        </div>
      )}
    </div>
  );
}

type AnalyticsCardProps = {
  icon: React.ReactNode;
  valueNgn: number;
  valueUsd: number;
  label: string;
  sub: string;
  bg: string;
  textColor: string;
};

function formatCurrencyShort(value: number, currency: "NGN" | "USD") {
  const absValue = Math.abs(value);
  let short = "";
  let symbol = currency === "NGN" ? "₦" : "$";
  if (absValue >= 1_000_000_000) {
    short = (value / 1_000_000_000).toFixed(2) + "B";
  } else if (absValue >= 1_000_000) {
    short = (value / 1_000_000).toFixed(2) + "M";
  } else if (absValue >= 1_000) {
    short = (value / 1_000).toFixed(2) + "K";
  } else {
    short = value.toFixed(2);
  }
  return symbol + short;
}

function formatCurrencyFull(value: number, currency: "NGN" | "USD") {
  return value.toLocaleString(currency === "NGN" ? "en-NG" : "en-US", {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

function AnalyticsCard({
  icon,
  valueUsd,
  valueNgn,
  label,
  sub,
  bg,
  textColor,
}: AnalyticsCardProps) {
  const shortNgn = formatCurrencyShort(valueNgn, "NGN");
  const fullNgn = formatCurrencyFull(valueNgn, "NGN");
  const shortUsd = formatCurrencyShort(valueUsd, "USD");
  const fullUsd = formatCurrencyFull(valueUsd, "USD");
  return (
    <div
      className={`flex items-center gap-4 p-5 rounded-2xl shadow-sm bg-gradient-to-br ${bg} hover:shadow-md transition`}
    >
      <div className="bg-white rounded-full p-2 w-10 h-10 flex items-center justify-center shadow">
        {icon}
      </div>
      <div className="flex flex-col">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className={`text-lg font-semibold ${textColor}`}>
                {shortNgn}
              </div>
            </TooltipTrigger>
            <TooltipContent side="top" className="text-sm font-medium">
              {fullNgn}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="text-sm text-gray-500">{shortUsd}</div>
            </TooltipTrigger>
            <TooltipContent side="top" className="text-sm font-medium">
              {fullUsd}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <div className="text-xs text-gray-500">{label}</div>
        <div className="text-xs text-gray-700">{sub}</div>
      </div>
    </div>
  );
}
