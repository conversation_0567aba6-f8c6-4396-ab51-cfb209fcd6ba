"use client";
import {
  <PERSON>DownCircle,
  ArrowUpCircle,
  Repeat,
  BarChart2,
  Loader2,
  RotateCw,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { adminApi } from "@/app/utils";

interface AnalyticsData {
  totalDepositValue: number;
  totalDeposits: number;
  totalSwapValue: number;
  totalSwaps: number;
  totalTransactionValue: number;
  totalTransactions: number;
  totalWithdrawalValue: number;
  totalWithdrawals: number;
}

interface Analytics {
  userId?: string;
}

export default function Analytics({ userId }: Analytics) {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(
    null
  );
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchAnalyticsData = async () => {
    setIsRefreshing(true);
    try {
      const response = await adminApi.get(
        `/v1/admin/analytics${userId ? `/?userId=${userId}` : ""}`
      );
      setAnalyticsData(response.data);
    } catch (error) {
      console.error("Failed to fetch analytics data", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [userId]);

  return (
    <div className="w-full p-4 sm:p-6 lg:p-8 font-circularStd">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold tracking-normal">
          Analytics Overview
        </h2>
        <Button
          variant="outline"
          className="w-md text-white bg-purple-600 hover:bg-purple-700 active:bg-purple-800"
          onClick={fetchAnalyticsData}
          disabled={isRefreshing}
        >
          {isRefreshing ? (
            <div className="flex items-center gap-2">
              <Loader2 strokeWidth="3px" className="animate-spin" />
              <p>Refreshing...</p>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <RotateCw strokeWidth="3px" />
              <p>Refresh...</p>
            </div>
          )}
        </Button>
      </div>

      {analyticsData ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <AnalyticsCard
            icon={<BarChart2 className="text-[#7928FF] w-5 h-5" />}
            value={analyticsData.totalTransactionValue}
            label="Total Transaction Value"
            sub={`${analyticsData.totalTransactions.toLocaleString()} Transactions`}
            bg="from-[#f3e8ff] to-white"
            textColor="text-[#7928FF]"
          />
          <AnalyticsCard
            icon={<ArrowDownCircle className="text-green-600 w-5 h-5" />}
            value={analyticsData.totalDepositValue}
            label="Total Deposit Value"
            sub={`${analyticsData.totalDeposits.toLocaleString()} Deposits`}
            bg="from-[#dcfce7] to-white"
            textColor="text-green-600"
          />
          <AnalyticsCard
            icon={<ArrowUpCircle className="text-blue-600 w-5 h-5" />}
            value={analyticsData.totalWithdrawalValue}
            label="Total Withdrawal Value"
            sub={`${analyticsData.totalWithdrawals.toLocaleString()} Withdrawals`}
            bg="from-[#dbeafe] to-white"
            textColor="text-blue-600"
          />
          <AnalyticsCard
            icon={<Repeat className="text-orange-500 w-5 h-5" />}
            value={analyticsData.totalSwapValue}
            label="Total Swap Value"
            sub={`${analyticsData.totalSwaps.toLocaleString()} Swaps`}
            bg="from-[#fff7e6] to-white"
            textColor="text-orange-500"
          />
        </div>
      ) : (
        <div className="flex justify-center items-center h-32">
          <span className="text-gray-400 text-sm animate-pulse">
            Loading analytics...
          </span>
        </div>
      )}
    </div>
  );
}

type AnalyticsCardProps = {
  icon: React.ReactNode;
  value: number;
  label: string;
  sub: string;
  bg: string;
  textColor: string;
};

function AnalyticsCard({
  icon,
  value,
  label,
  sub,
  bg,
  textColor,
}: AnalyticsCardProps) {
  function formatNaira(value: number) {
    const absValue = Math.abs(value);
    let short = "";

    if (absValue >= 1_000_000_000) {
      short = (value / 1_000_000_000).toFixed(2) + "B";
    } else if (absValue >= 1_000_000) {
      short = (value / 1_000_000).toFixed(2) + "M";
    } else if (absValue >= 1_000) {
      short = (value / 1_000).toFixed(2) + "K";
    } else {
      short = value.toFixed(2);
    }

    const full = value.toLocaleString("en-NG", {
      style: "currency",
      currency: "NGN",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    return {
      short: `₦${short}`,
      full,
    };
  }
  const { short, full } = formatNaira(value);
  return (
    <div
      className={`flex items-center gap-4 p-5 rounded-2xl shadow-sm bg-gradient-to-br ${bg} hover:shadow-md transition`}
    >
      <div className="bg-white rounded-full p-2 w-10 h-10 flex items-center justify-center shadow">
        {icon}
      </div>
      <div className="flex flex-col">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div
                className={`text-xl font-semibold ${textColor} flex items-baseline gap-0.5`}
              >
                <span className="text-sm align-top">{short[0]}</span> {/* ₦ */}
                <span className="text-xl">{short.slice(1, -1)}</span>{" "}
                <span className="text-sm align-top">{short.slice(-1)}</span>{" "}
              </div>
            </TooltipTrigger>
            <TooltipContent side="top" className="text-sm font-medium">
              {full}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <div className="text-xs text-gray-500">{label}</div>
        <div className="text-xs text-gray-700">{sub}</div>
      </div>
    </div>
  );
}
