"use client";

import { useEffect, useState } from "react";
import axios from "axios";
import { CONSTANTS, getToken } from "../../utils";
import Link from "next/link";
import Image from "next/image";
import arrow from "@/assets/images/arrow.svg";

const navInfo = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    role: "Co-founder & CEO, Do<PERSON>",
    text: "“Flincap has played a crucial role in scaling, building out and evolving our go-to-market model. It allows my team to easily stay on top of everything.”",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "CEO",
    text: "“Flincap has played a crucial role in scaling, building out and evolving our go-to-market model. It allows my team to easily stay on top of everything.”",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    role: "Co-Founder",
    text: "“<PERSON>lin<PERSON> has played a crucial role in scaling, building out and evolving our go-to-market model. It allows my team to easily stay on top of everything.”",
  },
];

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [slide, setSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setSlide((prev) => (prev === navInfo.length - 1 ? 0 : prev + 1));
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    async function getAccount() {
      const api = axios.create({
        baseURL: CONSTANTS.SERVER_URL,
        headers: { Authorization: `Bearer ${getToken()}` },
      });

      try {
        const res = await api.get("/v1/auth");
        if (res.data.status === 403) return;
        else window.location.href = "/dashboard";
      } catch (err: any) {
        console.log(err);
      }
    }

    getAccount();
  }, []);

  return (
    <div className="bg-white min-h-screen">
      <div className="border-b w-full">
        <Link href={"/"} className="logo py-4 px-4 flex">
          <Image
            src={"/images/flincap-logo.svg"}
            alt="."
            width={100}
            height={50}
          />
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-10">
        <div className="bg-[#4F0EB8] col-span-4 min-h-[calc(100vh-65px)] hidden md:block">
          <div className="h-full bg-[url(/images/flincapauthlogo.svg)] bg-no-repeat flex justify-center items-center">
            <div className="max-w-sm">
              {navInfo.map(
                (info) =>
                  info.id === slide + 1 && (
                    <div key={info.id} className="text-white">
                      <p className="max-w-[300px]">{info.text}</p>
                      <div className="mt-5 flex items-center gap-2">
                        <div className="w-12 h-12 bg-white rounded-full overflow-hidden">
                          <Image
                            src={"/images/profile.svg"}
                            alt="Alon Bartur"
                            width={100}
                            height={100}
                          />
                        </div>
                        <div className="text-sm">
                          <h3>{info.name}</h3>
                          <p>{info.role}</p>
                        </div>
                      </div>
                    </div>
                  )
              )}
              <div className="mt-5 flex gap-2">
                <span
                  className={`${
                    slide == 0 ? "bg-white" : "bg-[#7F31FF]"
                  } w-2 h-2 rounded-full`}
                />
                <span
                  className={`${
                    slide == 1 ? "bg-white" : "bg-[#7F31FF]"
                  } w-2 h-2 rounded-full`}
                />
                <span
                  className={`${
                    slide == 2 ? "bg-white" : "bg-[#7F31FF]"
                  } w-2 h-2 rounded-full`}
                />
              </div>
            </div>
          </div>
        </div>
        <main className="col-span-6 flex flex-col-reverse md:flex-col py-2 md:px-4">
          <p className="self-center md:self-end flex items-center md:pt-6 px-4">
            Don&apos;t have an account?{" "}
            <Link
              href="/auth/get-started"
              className="text-appPurple ml-2 flex gap-1"
            >
              Create One
              <Image src={arrow} alt="" />
            </Link>
          </p>
          <div>{children}</div>
        </main>
      </div>
    </div>
  );
}
