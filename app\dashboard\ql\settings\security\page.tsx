"use client";

import React, { useState, useEffect } from "react";
import axios from "axios";
import { CONSTANTS } from "@/app/utils";
import LoadingSpinner from "@/components/LoadingSpinner";
import { toast } from "sonner";
import { usePathname } from "next/navigation";
import useStore from "@/store";

export default function Page() {
  const pathname = usePathname();

  const { user } = useStore();

  const [is2FAEnabled, setIs2FAEnabled] = useState<boolean | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setIsLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [qrCodeUrl, setQrCodeUrl] = useState("");
  const [otpSecret, setOtpSecret] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  async function fetchUserStatus() {
    try {
      const token = localStorage.getItem("tid");
      const response = await axios.get(`${CONSTANTS.SERVER_URL}/v1/auth`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status.toString().startsWith("2")) {
        setIs2FAEnabled(response.data.data.is2faActive);
      }
    } catch (error) {
      console.error("Failed to fetch user status:", error);
    } finally {
      setInitialLoading(false);
    }
  }

  useEffect(() => {
    fetchUserStatus();
  }, []);

  async function setup2FA() {
    try {
      setIsLoading(true);
      setErrorMessage("");

      const token = localStorage.getItem("tid");
      const response = await axios.post(
        `${CONSTANTS.SERVER_URL}/v1/auth/setup-2fa`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status.toString().startsWith("2")) {
        setQrCodeUrl(response.data.qrCodeDataURL);
        setOtpSecret(response.data.base32);
        setIsModalOpen(true);
      } else {
        setErrorMessage("Failed to setup 2FA");
      }
    } catch (error: any) {
      console.log(error);
      toast.error(error.response?.data?.message || "Failed to setup 2FA");
    } finally {
      setIsLoading(false);
    }
  }

  function closeModal() {
    setIsModalOpen(false);
    setVerificationCode("");
  }

  function handleBackdropClick(event: React.MouseEvent<HTMLDivElement>) {
    if (event.currentTarget === event.target) {
      closeModal();
    }
  }

  async function verify2FA() {
    try {
      setIsLoading(true);
      const token = localStorage.getItem("tid");
      const res = await axios.post(
        `${CONSTANTS.SERVER_URL}/v1/auth/verify-2fa`,
        {
          otp: verificationCode,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (res.status.toString().startsWith("2")) {
        fetchUserStatus();
        setIsModalOpen(false);
        toast.success("Two-factor authentication has been enabled.");
      } else {
        toast.error("Failed to verify code");
      }
    } catch (err) {
      toast.error("Failed to verify code");
    } finally {
      setIsLoading(false);
    }
  }

  async function unlink2FA() {
    try {
      setIsLoading(true);
      setErrorMessage("");
      const token = localStorage.getItem("tid");
      const response = await axios.post(
        `${CONSTANTS.SERVER_URL}/v1/auth/unlink-2fa`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status.toString().startsWith("2")) {
        fetchUserStatus();
        toast.success("2FA has been unlinked from your account.");
      } else {
        setErrorMessage("Failed to unlink 2FA");
      }
    } catch (error) {
      toast.error("Failed to unlink 2FA");
      setErrorMessage("Failed to unlink 2FA");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <section className="max-w-2xl p-4 sm:p-6 font-circularStd">
      <div>
        <div className="flex flex-col gap-2">
          <h1 className="text-xl sm:text-2xl font-bold">Security Settings</h1>
          <p className="text-sm text-gray-500">
            Enhance your account security with two-factor authentication.
          </p>
        </div>

        {errorMessage && (
          <div className="text-red-500 text-sm p-2 bg-red-50 rounded mt-4">
            {errorMessage}
          </div>
        )}

        <div className="bg-white rounded-lg shadow p-4 sm:p-6 mt-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-base sm:text-lg font-medium">
                Two-Factor Authentication (2FA)
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                Add an extra layer of security to your account with Google
                Authenticator.
              </p>
            </div>
            <div className="self-start sm:self-center">
              {initialLoading ? (
                <LoadingSpinner />
              ) : is2FAEnabled ? (
                <div className="flex items-center gap-2">
                  <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                    Enabled
                  </span>
                  <button
                    onClick={unlink2FA}
                    disabled={loading}
                    className="py-2 w-36 px-2 bg-red-500 text-xs text-white rounded-md hover:bg-red-600 transition-colors justify-center gap-2 flex items-center"
                  >
                    {loading ? <LoadingSpinner className="" /> : null}
                    Unlink 2FA
                  </button>
                </div>
              ) : (
                <button
                  onClick={setup2FA}
                  disabled={loading}
                  className="w-36 px-2 py-2 bg-appPurple text-xs text-white rounded-md justify-center gap-2 flex items-center"
                >
                  {loading ? <LoadingSpinner className="" /> : null}
                  Enable 2FA
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {isModalOpen && (
        <div
          onClick={handleBackdropClick}
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 p-4"
        >
          <div
            className="bg-white p-4 sm:p-6 rounded-lg shadow-lg w-full max-w-md"
            onClick={(e) => e.stopPropagation()}
          >
            <h2 className="text-lg sm:text-xl font-bold mb-4">
              Set Up Two-Factor Authentication
            </h2>

            <div className="flex flex-col items-center mb-6">
              <ol className="text-sm text-gray-600 mb-4 list-decimal pl-5 w-full">
                <li className="mb-1">
                  Install Google Authenticator app on your phone
                </li>
                <li className="mb-1">Scan this QR code with the app</li>
                <li className="mb-1">
                  Enter the verification code shown in the app
                </li>
              </ol>

              {qrCodeUrl && (
                <div className="border p-2 mb-4 flex justify-center">
                  <img
                    src={qrCodeUrl}
                    alt="QR Code"
                    className="w-36 h-36 sm:w-48 sm:h-48"
                  />
                </div>
              )}

              <div className="w-full mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Secret Key (if you can&apos;t scan the QR code):
                </label>
                <div className="flex">
                  <input
                    type="text"
                    value={otpSecret}
                    readOnly
                    className="w-full p-2 border border-gray-300 rounded-l text-sm"
                  />
                  <button
                    className="bg-gray-200 px-2 sm:px-3 rounded-r border-y border-r border-gray-300 text-sm whitespace-nowrap"
                    onClick={() => {
                      navigator.clipboard.writeText(otpSecret);
                    }}
                  >
                    Copy
                  </button>
                </div>
              </div>

              <div className="w-full">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Verification Code:
                </label>
                <input
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  value={verificationCode}
                  onChange={(e) =>
                    setVerificationCode(e.target.value.replace(/[^0-9]/g, ""))
                  }
                  className="w-full p-2 border border-gray-300 rounded text-sm"
                  placeholder="Enter 6-digit code"
                  maxLength={6}
                />
              </div>
            </div>

            <div className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 border border-gray-300 rounded text-gray-700 mt-2 sm:mt-0"
              >
                Cancel
              </button>
              <button
                onClick={verify2FA}
                disabled={verificationCode.length !== 6 || loading}
                className={`px-4 py-2 bg-appPurple text-white rounded flex items-center justify-center ${
                  verificationCode.length !== 6 || loading
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
              >
                {loading ? <LoadingSpinner className="mr-2" /> : null}
                Verify & Enable
              </button>
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
