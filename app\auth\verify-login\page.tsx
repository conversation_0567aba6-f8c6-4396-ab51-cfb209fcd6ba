"use client";

import { CONSTANTS } from '@/app/utils';
import LoadingSpinner from '@/components/LoadingSpinner';
import Link from 'next/link'
import { useSearchParams } from 'next/navigation';
import React, { FormEvent, useEffect, useState } from 'react'
import axios from 'axios';

function Page() {
  const searchParams = useSearchParams();
  const [loaded, setLoaded] = useState(false);
  const [verified, setVerified] = useState(false);

  useEffect(() => {
    const auth_token = searchParams.get("auth_token") as string;

    async function verifyOTP() {
      try {
        const res = await axios.get(CONSTANTS.SERVER_URL + "/v1/auth/verify-login", {
          params: {
            auth_token,
          }
        });
        if (res.status.toString().startsWith("2")) {
          localStorage.setItem("tid", res.data.data.accessToken);
          window.location.href = "/dashboard";
          setVerified(true);
        } else {
          console.log("Login failed");
        }
      } catch (err: any) {
        setVerified(false);
        console.log(err);
        if (err.response) {
          console.log(err.response.data);
        }
      }

    }
    verifyOTP();
    // eslint-disable-next-line
  }, []);

  return (
    <div className="flex items-center justify-center px-4 py-10 md:py-4 md:h-[550px] w-full">
      <div className="grow max-w-md">
        <h1 className="text-3xl font-semibold">Verify Login</h1>

        <div className='my-4'>
          {loaded ? (verified ? <p className="py-2.5">You&apos;ll be redirected</p> : <p className="py-2.5">Link expired</p>) : <LoadingSpinner className="mx-auto" />}
        </div>
        <Link href={"/"} className="bg-appPurple text-white rounded-md py-2.5 w-full block mt-2 text-sm text-center">
          Back home
        </Link>
      </div>
    </div>
  )
}

export default Page