import React, { useEffect, useState } from "react";
import useStore from "../../store";
import { CloseIcon } from "../icons";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { adminApi, api } from "@/app/utils";
import Select from "react-select";

function CreateAssetFeesModal() {
  const [loading, setLoading] = useState(false);
  const { setModal } = useStore((state) => state);
  const [formValues, setFormValues] = useState({
    name: "",
    fee: "",
  });
  const [assets, setAssets] = useState<Record<any, any>[]>([]);

  useEffect(() => {
    setLoading(true);
    api("/v1/wallets/available-assets")
      .then((res: any) => {
        console.log(res);
        setAssets(res.data);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const handleChange = (e: any) => {
    const { name, value } = e.target;

    setFormValues({ ...formValues, [name]: value });
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setLoading(true);

    await adminApi
      .post("/v1/admin/asset-fees", {
        name: formValues.name,
        fee: Number(formValues.fee),
      })
      .then((res) => {
        console.log(res);
        if (res.data.success) {
          toast.success(res.data.message);
        } else {
          toast.error("Failed to create asset fee!");
        }
      })
      .catch((error) => {
        console.error(error);
        if (error.response) toast.error(error.response.data.message);
      })
      .finally(() => {
        setLoading(false);
      });

    setModal(null);
  };

  return (
    <>
      <ToastContainer />
      <form onSubmit={handleSubmit}>
        <div className="w-72 sm:w-96 text-[#324063] bg-[#FFFFFF] rounded p-5">
          <div className="flex justify-between items-center">
            <h3 className="text-[#324063] text-sm leading-8 font-semibold my-1">
              Create Asset Fees
            </h3>
            <span className="cursor-pointer" onClick={() => setModal(null)}>
              <CloseIcon />
            </span>
          </div>
          <div>
            <label className="font-light text-ash text-sm my-1 flex flex-col">
              <span className="mb-1">Name</span>
              <Select
                options={assets.map((asset) => ({
                  label: asset.label,
                  value: asset.assetType,
                }))}
                placeholder="Select Asset"
                isSearchable={true}
                onChange={(e: any) =>
                  setFormValues((prev: any) => ({
                    ...prev,
                    name: e.value,
                  }))
                }
                styles={{
                  control: (baseStyles: any) => ({
                    ...baseStyles,
                    border: "1px solid #CACACA",
                    boxShadow: "none",
                    ":focus-within": {
                      border: "1px solid #7928FF",
                    },
                    padding: "4px 2px 4px 2px",
                    fontSize: "14px",
                    marginTop: "8px",
                  }),
                }}
              />
            </label>
            <label className="font-light text-ash text-sm my-1 flex flex-col">
              <span className="mb-1">Fee</span>
              <input
                className="border border-ash2 outline-none p-2 mb-2 rounded"
                type="text"
                name="fee"
                placeholder="Enter fee for asset"
                onChange={handleChange}
              />
            </label>
          </div>

          <button
            className="w-full p-2 my-2 rounded bg-appPurple text-white text-sm text-center justify-center items-center flex gap-3"
            type="submit"
            disabled={loading}
          >
            {!loading ? (
              "Create"
            ) : (
              <div className="w-4 h-4 border-b-2 border-r-2 border-gray-200 rounded-full animate-spin"></div>
            )}
          </button>
        </div>
      </form>
    </>
  );
}

function UpdateAssetFeesModal({ asset }: any) {
  const [loading, setLoading] = useState(false);
  const { setModal } = useStore((state) => state);
  const [formValues, setFormValues] = useState({
    fee: asset.fee,
  });

  const handleChange = (e: any) => {
    const { name, value } = e.target;

    setFormValues({ ...formValues, [name]: value });
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setLoading(true);

    await adminApi
      .post("/v1/admin/asset-fees", {
        id: asset.id,
        fee: Number(formValues.fee),
        name: asset.name,
      })
      .then((res) => {
        console.log(res);
        if (res.data.success) {
          toast.success(res.data.message);
        } else {
          toast.error("Asset fee update failed");
        }
      })
      .catch((error) => {
        console.error(error);
        if (error.response) toast.error(error.response.data.message);
      })
      .finally(() => {
        setLoading(false);
      });

    setModal(null);
  };

  return (
    <>
      <ToastContainer />
      <form onSubmit={handleSubmit}>
        <div className="w-72 sm:w-96 text-[#324063] bg-[#FFFFFF] rounded p-5">
          <div className="flex justify-between items-center">
            <h3 className="text-[#324063] text-sm leading-8 font-semibold my-1">
              Update Asset Fees
            </h3>
            <span className="cursor-pointer" onClick={() => setModal(null)}>
              <CloseIcon />
            </span>
          </div>
          <div>
            <label className="font-light text-ash text-sm my-1 flex flex-col">
              <span className="mb-1">Fee</span>
              <input
                className="border border-ash2 outline-none p-2 mb-2 rounded"
                type="text"
                name="fee"
                placeholder="Enter fee for asset"
                onChange={handleChange}
              />
            </label>
          </div>

          <button
            className="w-full p-2 my-2 rounded bg-appPurple text-white text-sm text-center justify-center items-center flex gap-3"
            type="submit"
            disabled={loading}
          >
            {!loading ? (
              "Update"
            ) : (
              <div className="w-4 h-4 border-b-2 border-r-2 border-gray-200 rounded-full animate-spin"></div>
            )}
          </button>
        </div>
      </form>
    </>
  );
}

export { CreateAssetFeesModal, UpdateAssetFeesModal };
