"use client";
import { adminApi } from "@/app/utils";
import InviteBusineessUser from "@/components/modals/InviteBusineessUser";
import useStore from "@/store";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

function Page() {
  const { user, setModal } = useStore();
  const [users, setUsers] = useState<Record<string, any>[]>([]);
  const [clientType, setClientType] = useState("");
  const navigator = useRouter();

  // Make a request to fetch users on page load
  useEffect(() => {
    async function fetchUsers() {
      try {
        const res = await adminApi.get("/v1/admin/users");
        setUsers(res.data.data.users);
      } catch (err) {
        console.log(err);
      }
    }

    fetchUsers();
  }, []);

  const exportToExcel = async () => {
    try {
      const response = await adminApi.get("/v1/admin/users/excel", {
        responseType: "blob",
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));

      const link = document.createElement("a");
      link.href = url;

      link.setAttribute("download", "users.csv");

      document.body.appendChild(link);
      link.click();

      link?.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error exporting users:", error);
    }
  };

  return (
    <div>
      <section className="pt-4">
        <a download="email.csv" className="cursor-pointer px-4">
          <button
            className="outline bg-appPurple rounded text-white py-1.5 px-4 text-sm md:text-base active:scale-95"
            onClick={exportToExcel}
          >
            Export Users
          </button>
        </a>
        <div className="cards grid md:grid-cols-3 gap-4 px-4 my-4">
          <div className="card rounded-md bg-[#faf7ff] px-4 py-6 text-sm">
            <h3 className="font-semibold">Total Clients</h3>
            <div className="flex gap-1 mt-3">
              <p className="text-appGrayText2">Filter:</p>
              <select
                onChange={(e) => setClientType(e.target.value)}
                className="border-none bg-transparent outline-none accent-transparent text-appPurple"
              >
                <option
                  value={"All"}
                  defaultValue={"AllTime"}
                  className="bg-transparent"
                >
                  All Clients
                </option>
                <option value={"Active"} className="bg-transparent">
                  Active
                </option>
                <option value={"Inactive"} className="bg-transparent">
                  Inctive
                </option>
              </select>
            </div>
            <h1 className="font-semibold text-2xl mt-6">{users.length}</h1>
            {/* <button className="px-4 py-1.5 rounded bg-black text-white" onClick={() => setModal(<InviteBusineessUser />)}>Invite User</button> */}
          </div>

          <div className="card rounded-md bg-[#faf7ff] px-4 py-6 text-sm">
            <h3 className="font-semibold">Active</h3>
            <div className="flex gap-1 mt-3">
              <p className="text-appGrayText2">Currently active clients</p>
            </div>
            <h1 className="font-semibold text-xl mt-6">
              {users.filter((x) => x.isActive).length}
            </h1>
          </div>

          <div
            className="card rounded-md bg-[#faf7ff] px-4 py-6 text-sm cursor-pointer"
            onClick={() => setModal(<InviteBusineessUser />)}
          >
            <h3 className="font-semibold">Add new client</h3>
            <div className="flex gap-1 mt-3">
              <p className="text-appGrayText2">
                Setup a new account for a client.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="min-w-full mt-5 overflow-x-auto px-4">
        <table className="w-full">
          <thead className="text-[14px] text-ash rounded-lg bg-ash1 border-b border-b-ash2">
            <tr className="text-left">
              <th className="p-3 font-normal">Name</th>
              <th className="p-3 font-normal">Email</th>
              <th className="p-3 font-normal">Status</th>
              <th className="p-3 font-normal flex items-center gap-2">
                Exchange Name
              </th>
              <th className="p-3 font-normal">Last login</th>
            </tr>
          </thead>
          <tbody>
            {users
              .filter((user) => {
                if (clientType === "Active") return user.isActive;
                else if (clientType === "Inactive") return !user.isActive;
                else return true;
              })
              .map((user) => (
                <tr
                  key={user.id}
                  onClick={() => window.open("/admin/clients/" + user.id)}
                  className="text-xs text-black hover:text-appPurpleDark border-b border-b-ash2 cursor-pointer hover:bg-appWhite"
                >
                  <td className="p-3 flex items-center gap-2 whitespace-nowrap">
                    <Image
                      src={"https://ui-avatars.com/api/?name=" + user.fullName}
                      alt="bitcoin up"
                      width={30}
                      height={30}
                      className="rounded-full"
                    />
                    <span className="font-semibold capitalize">
                      {user.fullName}
                    </span>
                  </td>
                  <td className="p-3 font-semibold whitespace-nowrap">
                    {user.email}
                  </td>
                  <td
                    className={`p-3 ${
                      user.isActive ? "text-[#13BF62]" : "text-red-500"
                    } font-semibold`}
                  >
                    {user.isActive ? "Active" : "Inactive"}
                  </td>
                  <td className="p-3 font-semibold">{user.companyID}</td>
                  <td className="p-3 font-semibold whitespace-nowrap">
                    {new Date(user.lastLogin).toLocaleDateString()}
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </section>
    </div>
  );
}

export default Page;
