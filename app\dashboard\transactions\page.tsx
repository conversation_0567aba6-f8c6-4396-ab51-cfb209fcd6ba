"use client";
import RecentTransactionsTable from "@/components/dashboard/RecentTransactionsTable";
import { useDashboardData } from "../layout";
import { generatePDF, exportReceiptAsImage } from "@/lib/utils";

export default function Page() {
  const { transactions, bankData } = useDashboardData();
  return (
    <div className="font-circularStd">
      <RecentTransactionsTable
        transactions={transactions}
        fiatBankData={bankData}
        exportReceiptAsImage={exportReceiptAsImage}
        generatePDF={generatePDF}
      />
    </div>
  );
}
