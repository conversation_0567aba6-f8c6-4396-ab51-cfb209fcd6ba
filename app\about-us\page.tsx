"use client";

import FeaturedOn from "@/components/homepage/FeaturedOn";
import Nav from "@/components/Nav";
import PuzzlePieces from "@/assets/images/puzzle.svg";
import Image from "next/image";

export default function AboutUs() {
  return (
    <>
      <Nav />
      <section className="md:h-[520px] bg-[url(/images/hero-bg.svg)] bg-cover bg-left bg-no-repeat font-circularStd">
        <div className="max-w-2xl mx-auto flex flex-col gap-10 md:pt-20">
          <h3 className="text-center font-bold">ABOUT US</h3>
          <h1 className="text-3xl md:text-6xl font-extrabold text-black text-center">
            We want to see you and your business grow!
          </h1>
          <p className="text-center">
            We aim to be the number one platform for OTC crypto exchangers by
            providing advanced tech features and services to aid the growth of
            your business as a P2P Trader.{" "}
          </p>
        </div>
      </section>
      <FeaturedOn />
      <section className="bg-purple-100 font-circularStd py-14 flex justify-evenly">
        <div className="flex flex-col gap-2 items-center max-w-72">
          <p className="text-primary font-extrabold text-5xl">10+</p>
          <p className="font-extrabold">Onboarded</p>
          <p className="text-center">
            Businesses currently active in private beta
          </p>
        </div>
        <div className="flex flex-col gap-2 items-center max-w-72">
          <p className="text-primary font-extrabold text-5xl">$27,000</p>
          <p className="font-extrabold">Transaction Processed</p>
          <p className="text-center">
            We spent the past months building amazing technology Private Beta
            (2023)
          </p>
        </div>
        <div className="flex flex-col gap-2 items-center max-w-72">
          <p className="text-primary font-extrabold text-5xl">2+</p>
          <p className="font-extrabold">Active</p>
          <p>Have launched</p>
        </div>
      </section>
      <section className="py-20 font-circularStd">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-[#0A2540] text-center text-3xl font-extrabold">
            Our Flincap Story
          </h2>
          <div className="flex flex-col gap-4 text-center pt-8 font-light">
            <p>
              Flincap was founded in 2023 as a whitelabel cryptocurrency
              exchange after our co-founder Nathaniel Luz tried to build a
              crypto exchange in 2019, but couldn’t scale it due to the costs
              and technicalities involved in running a standard crypto exchange.
            </p>{" "}
            <p>
              After joining the founding team of Flincap as the CTO, Victory
              Oyekpen led a product pivot from targeting startups to powering
              OTC crypto exchanges across Africa. This pivot was built on the
              premise of the over 10,000 OTC crypto merchants in Sub-Saharan
              Africa processing 6 billion USD annually, despite lacking access
              to proper crypto exchange infrastructure and resulting in trading
              on Telegram and WhatsApp.
            </p>
            <p>
              Flincap has since evolved to cater to the wholesome needs of OTC
              crypto exchanges, helping them manage transactions and secure
              digital assets. Flincap offers a comprehensive, automated, and
              cost-effective solution to the issues faced by OTC crypto traders,
              helping them build secure and efficient crypto exchanges.
            </p>
          </div>
        </div>
      </section>
      <section className="py-20">
        <div className="max-w-7xl mx-auto flex gap-4">
          <div>
            <h2 className="text-[#0A2540] text-center text-3xl font-extrabold">
              Our Core Principles
            </h2>
            <div className="flex flex-col gap-4">
              <div className="bg-[#EDE2FF]">
                <h4>Support</h4>
                <p>We are always available to help you.</p>
              </div>
              <div></div>
              <div></div>
              <div></div>
            </div>
          </div>
          <div className="bg-[#EDE2FF] p-7 py-24 rounded-2xl">
            <Image src={PuzzlePieces} alt="" width={400} />
          </div>
        </div>
      </section>
    </>
  );
}
